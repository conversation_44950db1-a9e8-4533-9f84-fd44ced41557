# app/extensions.py
"""
Flask extensions initialization.
This file contains instances of Flask extensions that are used across the application.
These extensions are initialized in the create_app() function in app/__init__.py.
"""
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from flask_mail import Mail
from flask_wtf.csrf import CSRFProtect
from flask_migrate import Migrate

# Initialize extensions without binding to an application instance
# They will be initialized with the app in the create_app() function
db = SQLAlchemy()
login_manager = LoginManager()
mail = Mail()
csrf = CSRFProtect()
migrate = Migrate()

# Configure LoginManager
login_manager.login_view = 'auth.login'
login_manager.login_message_category = 'info'
login_manager.login_message = 'Please log in to access this page.'
login_manager.needs_refresh_message = 'Please re-authenticate to access this page.'
login_manager.needs_refresh_message_category = 'info'