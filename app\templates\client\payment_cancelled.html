<!-- app/templates/client/payment_cancelled.html -->
{% extends "base.html" %}

{% block title %}{{ t('payment.cancelled') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-times-circle text-danger" style="font-size: 5rem;"></i>
                </div>
                <h2 class="mb-3">{{ t('payment.cancelled_title') }}</h2>
                <p class="lead mb-4">{{ t('payment.cancelled_message') }}</p>
                <p class="mb-4">{{ t('payment.invoice_number') }}: <strong>#{{ invoice.id }}</strong></p>
                <p class="mb-4">{{ t('payment.amount') }}: <strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></p>
                
                <div class="mt-4">
                    <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-primary">
                        <i class="fas fa-credit-card"></i> {{ t('payment.try_again') }}
                    </a>
                    <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-file-invoice"></i> {{ t('payment.view_invoice') }}
                    </a>
                    <a href="{{ url_for('client.dashboard') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-home"></i> {{ t('payment.back_to_dashboard') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
