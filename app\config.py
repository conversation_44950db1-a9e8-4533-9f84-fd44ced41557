# app/config.py
import os
from datetime import timedelta

class Config:
    """Base configuration."""
    # Flask settings
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-key-change-in-production')

    # SQLAlchemy settings
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,  # Check if connection is still alive
        'pool_recycle': 300,    # Recycle connections after 5 minutes
        'pool_timeout': 30,     # Timeout after 30 seconds
        'pool_size': 10         # Connection pool size
    }

    # Email settings
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')

    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    SESSION_TYPE = 'filesystem'

    # Application settings
    TIMEZONE = os.environ.get('TIMEZONE', 'America/New_York')  # EST timezone

    # Stripe API settings
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY')
    STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_PUBLISHABLE_KEY')
    STRIPE_WEBHOOK_SECRET = os.environ.get('STRIPE_WEBHOOK_SECRET')

    # CSRF protection
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # 1 hour timeout for CSRF tokens

    # Appointment settings
    BUSINESS_HOURS_START = 8  # 8 AM
    BUSINESS_HOURS_END = 21   # 9 PM

    # Password policy
    MIN_PASSWORD_LENGTH = 8

    # Encryption settings
    ENCRYPTION_KEY = os.environ.get('ENCRYPTION_KEY', 'YourDefaultDevKeyHere-ChangeInProduction-32Bytes')

    @staticmethod
    def get_database_url():
        """Get database URL with fallback to DATABASE_URL env variable."""
        url = os.environ.get('DATABASE_URL')

        # Handle SQLAlchemy 1.4+ compatibility issue with postgres:// URLs
        if url and url.startswith('postgres://'):
            url = url.replace('postgres://', 'postgresql://', 1)

        return url

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = Config.get_database_url() or 'postgresql://postgres:postgres@localhost/tutoring_dev'

    # Enable debug toolbar in development
    DEBUG_TB_ENABLED = True
    DEBUG_TB_INTERCEPT_REDIRECTS = False

    # Temporarily disable CSRF for development (REMOVE IN PRODUCTION!)
    # WTF_CSRF_ENABLED = False

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = Config.get_database_url() or 'postgresql://postgres:postgres@localhost/tutoring_test'
    # Disable CSRF during testing
    WTF_CSRF_ENABLED = False

    # Use an in-memory server for testing emails
    MAIL_SERVER = 'localhost'
    MAIL_PORT = 1025
    MAIL_USE_TLS = False

class ProductionConfig(Config):
    """Production configuration."""
    # Fetch database URL from environment variable
    SQLALCHEMY_DATABASE_URI = Config.get_database_url()

    # Production-specific settings
    WTF_CSRF_ENABLED = True
    PREFERRED_URL_SCHEME = 'https'

    # Security settings
    SESSION_COOKIE_SECURE = True
    REMEMBER_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    REMEMBER_COOKIE_HTTPONLY = True

# Configuration dictionary for app factory
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}