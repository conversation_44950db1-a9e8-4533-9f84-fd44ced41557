# Tutor Profile Enhancement Setup

This document provides instructions for setting up the enhanced tutor profile features, including address information, banking details, availability management, and service rates.

## Database Migration

1. Run the SQL migration script in your PostgreSQL database via DBeaver:
   - Open the file `app/docs/database/tutor_profile_enhancement.sql` in DBeaver
   - Execute the script against your database

## Encryption Key Setup

For secure storage of banking information, you need to set up an encryption key:

1. Generate a secure encryption key:
   ```bash
   python app/docs/generate_encryption_key.py
   ```

2. Add the generated key to your environment variables:
   ```bash
   export ENCRYPTION_KEY=your_generated_key
   ```

   Or add it to your `.env` file:
   ```
   ENCRYPTION_KEY=your_generated_key
   ```

## Install Dependencies

Install the required cryptography package:

```bash
pip install -r requirements.txt
```

## Features Overview

### Enhanced Tutor Profile

The tutor profile now includes:
- Contact information (name, email, phone)
- Address information (street, city, province, zip code, country)
- Personal information (birthdate)
- Banking information (encrypted transit number, institution number, account number)
- Professional information (bio, qualifications)

### Tutor Availability Management

Tutors can now:
- Set their regular availability schedule
- Specify which days and times they are available
- Manage their availability slots (add, edit, delete)

### Service Rates Management

Tutors can now:
- Set their rates for each service they provide
- Specify tutor rate, client rate, and transport fees
- Manage their service rates (add, edit, delete)

## Accessing the Features

### For Tutors:
1. Log in as a tutor
2. Click on the "My Profile" dropdown in the navigation bar
3. You'll see three options:
   - "Personal Info" - The enhanced profile form with all the new fields
   - "Availability" - Manage your availability schedule
   - "Service Rates" - Set your rates for each service

### For Managers:
1. Log in as a manager
2. Go to "Management" > "Tutors" to see the list of tutors
3. Click on a tutor's name to view their details
4. You'll see all the new information including:
   - Address and personal information
   - Banking information (decrypted for viewing)
   - Availability schedule
   - Service rates

## Security Considerations

- Banking information is stored encrypted in the database
- Only authorized users (the tutor themselves and managers) can view the decrypted information
- The encryption key should be kept secure and not committed to version control
- In production, use a strong, unique encryption key
