{% extends "base.html" %}

{% block title %}TECFÉE Enrollments - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-users text-primary"></i>
                TECFÉE Enrollments
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">TECFÉE</a></li>
                    <li class="breadcrumb-item active">Enrollments</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.tecfee_enroll_client') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Enroll New Client
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {% if current_status == 'all' %}selected{% endif %}>All Statuses</option>
                                <option value="active" {% if current_status == 'active' %}selected{% endif %}>Active</option>
                                <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                                <option value="withdrawn" {% if current_status == 'withdrawn' %}selected{% endif %}>Withdrawn</option>
                                <option value="suspended" {% if current_status == 'suspended' %}selected{% endif %}>Suspended</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <a href="{{ url_for('manager.tecfee_enrollments') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Enrollments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Enrollments
                        <span class="badge bg-secondary">{{ enrollments|length }}</span>
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-file-csv"></i> Export CSV</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-file-excel"></i> Export Excel</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {% if enrollments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Enrollment Date</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Progress</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for enrollment in enrollments %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {% if enrollment.client %}
                                                    {{ enrollment.client.first_name[0] if enrollment.client.first_name else '?' }}{{ enrollment.client.last_name[0] if enrollment.client.last_name else '?' }}
                                                {% else %}
                                                    ??
                                                {% endif %}
                                            </div>
                                            <div>
                                                {% if enrollment.client %}
                                                    <strong>{{ enrollment.client.first_name or 'Unknown' }} {{ enrollment.client.last_name or 'Client' }}</strong><br>
                                                    <small class="text-muted">{{ enrollment.client.email or 'No email' }}</small>
                                                {% else %}
                                                    <strong class="text-danger">Missing Client Data</strong><br>
                                                    <small class="text-muted">Client ID: {{ enrollment.client_id }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ enrollment.enrollment_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ enrollment.start_date.strftime('%Y-%m-%d') if enrollment.start_date else '-' }}</td>
                                    <td>{{ enrollment.end_date.strftime('%Y-%m-%d') if enrollment.end_date else '-' }}</td>
                                    <td>
                                        {% if enrollment.status == 'active' %}
                                        <span class="badge bg-success">Active</span>
                                        {% elif enrollment.status == 'completed' %}
                                        <span class="badge bg-primary">Completed</span>
                                        {% elif enrollment.status == 'withdrawn' %}
                                        <span class="badge bg-danger">Withdrawn</span>
                                        {% elif enrollment.status == 'suspended' %}
                                        <span class="badge bg-warning">Suspended</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ enrollment.status|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set completed_modules = enrollment.module_progress.filter_by(status='completed').count() %}
                                        {% set total_modules = program.total_modules %}
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: {{ (completed_modules / total_modules * 100) if total_modules > 0 else 0 }}%">
                                                {{ completed_modules }}/{{ total_modules }}
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ "%.0f"|format((completed_modules / total_modules * 100) if total_modules > 0 else 0) }}% complete</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                                Actions
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#enrollmentModal{{ enrollment.id }}">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a></li>
                                                <li><a class="dropdown-item" href="#">
                                                    <i class="fas fa-chart-line"></i> View Progress
                                                </a></li>
                                                {% if enrollment.status == 'active' %}
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-warning" href="#" onclick="suspendEnrollment({{ enrollment.id }})">
                                                    <i class="fas fa-pause"></i> Suspend
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="withdrawEnrollment({{ enrollment.id }})">
                                                    <i class="fas fa-times"></i> Withdraw
                                                </a></li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Enrollment Details Modal -->
                                <div class="modal fade" id="enrollmentModal{{ enrollment.id }}" tabindex="-1">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    Enrollment Details -
                                                    {% if enrollment.client %}
                                                        {{ enrollment.client.first_name or 'Unknown' }} {{ enrollment.client.last_name or 'Client' }}
                                                    {% else %}
                                                        Missing Client Data
                                                    {% endif %}
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h6>Client Information</h6>
                                                        {% if enrollment.client %}
                                                            <p><strong>Name:</strong> {{ enrollment.client.first_name or 'Unknown' }} {{ enrollment.client.last_name or 'Client' }}</p>
                                                            <p><strong>Email:</strong> {{ enrollment.client.email or 'Not provided' }}</p>
                                                            <p><strong>Phone:</strong> {{ enrollment.client.phone or 'Not provided' }}</p>
                                                        {% else %}
                                                            <div class="alert alert-warning">
                                                                <i class="fas fa-exclamation-triangle"></i>
                                                                <strong>Missing Client Data</strong><br>
                                                                Client ID: {{ enrollment.client_id }}<br>
                                                                <small>This enrollment has a missing or deleted client record.</small>
                                                            </div>
                                                        {% endif %}
                                                    </div>
                                                    <div class="col-md-6">
                                                        <h6>Enrollment Information</h6>
                                                        <p><strong>Enrollment Date:</strong> {{ enrollment.enrollment_date.strftime('%Y-%m-%d') }}</p>
                                                        <p><strong>Start Date:</strong> {{ enrollment.start_date.strftime('%Y-%m-%d') if enrollment.start_date else 'Not set' }}</p>
                                                        <p><strong>End Date:</strong> {{ enrollment.end_date.strftime('%Y-%m-%d') if enrollment.end_date else 'Not set' }}</p>
                                                        <p><strong>Status:</strong>
                                                            {% if enrollment.status == 'active' %}
                                                            <span class="badge bg-success">Active</span>
                                                            {% elif enrollment.status == 'completed' %}
                                                            <span class="badge bg-primary">Completed</span>
                                                            {% elif enrollment.status == 'withdrawn' %}
                                                            <span class="badge bg-danger">Withdrawn</span>
                                                            {% elif enrollment.status == 'suspended' %}
                                                            <span class="badge bg-warning">Suspended</span>
                                                            {% endif %}
                                                        </p>
                                                    </div>
                                                </div>

                                                <hr>

                                                <h6>Module Progress</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Module</th>
                                                                <th>Status</th>
                                                                <th>Start Date</th>
                                                                <th>Completion Date</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {% for progress in enrollment.module_progress %}
                                                            <tr>
                                                                <td>
                                                                    <strong>Module {{ progress.module.module_number }}</strong><br>
                                                                    <small>{{ progress.module.name }}</small>
                                                                </td>
                                                                <td>
                                                                    {% if progress.status == 'not_started' %}
                                                                    <span class="badge bg-secondary">Not Started</span>
                                                                    {% elif progress.status == 'in_progress' %}
                                                                    <span class="badge bg-warning">In Progress</span>
                                                                    {% elif progress.status == 'completed' %}
                                                                    <span class="badge bg-success">Completed</span>
                                                                    {% endif %}
                                                                </td>
                                                                <td>{{ progress.start_date.strftime('%Y-%m-%d') if progress.start_date else '-' }}</td>
                                                                <td>{{ progress.completion_date.strftime('%Y-%m-%d') if progress.completion_date else '-' }}</td>
                                                            </tr>
                                                            {% endfor %}
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No enrollments found</h5>
                        <p class="text-muted">
                            {% if current_status != 'all' %}
                            No enrollments found with status "{{ current_status }}".
                            {% else %}
                            No clients have enrolled in the TECFÉE program yet.
                            {% endif %}
                        </p>
                        <a href="{{ url_for('manager.tecfee_enroll_client') }}" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i> Enroll First Client
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}
</style>

<script>
function suspendEnrollment(enrollmentId) {
    if (confirm('Are you sure you want to suspend this enrollment?')) {
        // Implementation for suspending enrollment
        console.log('Suspending enrollment:', enrollmentId);
    }
}

function withdrawEnrollment(enrollmentId) {
    if (confirm('Are you sure you want to withdraw this enrollment? This action cannot be undone.')) {
        // Implementation for withdrawing enrollment
        console.log('Withdrawing enrollment:', enrollmentId);
    }
}
</script>
{% endblock %}
