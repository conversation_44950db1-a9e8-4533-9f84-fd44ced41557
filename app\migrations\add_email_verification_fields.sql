-- Add Email Verification Fields to Users Table
-- This script adds the necessary fields for email verification functionality
-- Run this script on the production database to enable email verification

-- Step 1: Add email verification fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_token VARCHAR(255),
ADD COLUMN IF NOT EXISTS verification_token_expires TIMESTAMP;

-- Step 2: Update existing users to have email_verified = TRUE for managers and tutors
-- (They were created before email verification was required)
UPDATE users 
SET email_verified = TRUE 
WHERE role IN ('manager', 'tutor') AND email_verified = FALSE;

-- Step 3: Create index for verification token lookups
CREATE INDEX IF NOT EXISTS idx_users_verification_token ON users(verification_token);

-- Step 4: Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('email_verified', 'verification_token', 'verification_token_expires')
ORDER BY column_name;

-- Step 5: Show current email verification status
SELECT 
    role,
    COUNT(*) as total_users,
    COUNT(CASE WHEN email_verified = TRUE THEN 1 END) as verified_users,
    COUNT(CASE WHEN email_verified = FALSE THEN 1 END) as unverified_users
FROM users 
GROUP BY role
ORDER BY role;

-- Expected result:
-- - email_verified: BOOLEAN column with default FALSE
-- - verification_token: VARCHAR(255) column for storing verification tokens
-- - verification_token_expires: TIMESTAMP column for token expiration
-- - Existing managers and tutors should have email_verified = TRUE
-- - New clients will have email_verified = FALSE until they verify their email
