<!-- app/templates/tutor/appointment_update.html -->
{% extends "base.html" %}

{% block title %}Update Appointment - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Update Appointment</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Appointment
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Session Details</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>
                            {% if appointment.dependant_id %}
                                Student
                            {% else %}
                                Client
                            {% endif %}
                        </h6>
                        <p>
                            {% if appointment.dependant_id and appointment.dependant %}
                                {{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}
                                <br>
                                <small class="text-muted">
                                    Client: {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                </small>
                            {% else %}
                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Date & Time</h6>
                        <p>
                            {{ appointment.start_time.strftime('%Y-%m-%d') }}<br>
                            {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>Service</h6>
                        <p>
                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                {{ appointment.tutor_service.service.name }}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>Duration</h6>
                        <p>{{ appointment.duration_minutes }} minutes</p>
                    </div>
                </div>

                <form method="POST" action="{{ url_for('tutor.update_appointment', id=appointment.id) }}">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        {{ form.status(class="form-control" + (" is-invalid" if form.status.errors else "")) }}
                        {% if form.status.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Session Notes</label>
                        {{ form.notes(class="form-control", rows=5) }}
                        <div class="form-text">
                            Add notes about the session, client progress, or any concerns. These notes will be visible to managers.
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}