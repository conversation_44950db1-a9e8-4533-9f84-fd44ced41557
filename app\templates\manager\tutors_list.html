<!-- app/templates/manager/tutors_list.html -->
{% extends "base.html" %}

{% block title %}Tutors - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Tutors</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Tutor
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.tutors_list') }}" class="row g-3 filter-form">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" value="{{ form.search.data or '' }}" placeholder="Search by name, email, or phone...">
                </div>
            </div>
            <div class="col-md-5">
                <select name="service_id" class="form-select">
                    <option value="">All Services</option>
                    {% for service in services %}
                        <option value="{{ service.id }}" {% if request.args.get('service_id') == service.id|string %}selected{% endif %}>
                            {{ service.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Tutors Table -->
<div class="card shadow">
    <div class="card-body">
        {% if tutors %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Services</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tutor in tutors %}
                            <tr>
                                <td>{{ tutor.first_name }} {{ tutor.last_name }}</td>
                                <td>{{ tutor.user.email }}</td>
                                <td>{{ tutor.phone }}</td>
                                <td>
                                    {% for tutor_service in tutor.tutor_services %}
                                        <span class="badge bg-info">{{ tutor_service.service.name }}</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if tutor.is_active else 'danger' }}">
                                        {{ 'Active' if tutor.is_active else 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.edit_tutor', id=tutor.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{{ url_for('manager.view_tutor', id=tutor.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                <h5>No tutors found</h5>
                <p class="text-muted">No tutors match your search criteria or no tutors have been added yet.</p>
                <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> Add Tutor
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}