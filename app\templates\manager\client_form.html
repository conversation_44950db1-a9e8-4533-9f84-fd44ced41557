{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.clients_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Clients
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if client %}action="{{ url_for('manager.edit_client', id=client.id) }}"{% else %}action="{{ url_for('manager.new_client') }}"{% endif %}>
                    {{ form.csrf_token }}

                    <!-- Client Type Selection -->
                    <div class="mb-4">
                        <label class="form-label">Client Type</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" id="client_type_individual" name="client_type" value="individual" {% if form.client_type.data == 'individual' %}checked{% endif %}>
                                    <label class="form-check-label" for="client_type_individual">
                                        <i class="fas fa-user"></i> Individual Client
                                    </label>
                                    <div class="form-text">A single person receiving tutoring services</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" id="client_type_institutional" name="client_type" value="institutional" {% if form.client_type.data == 'institutional' %}checked{% endif %}>
                                    <label class="form-check-label" for="client_type_institutional">
                                        <i class="fas fa-building"></i> Institutional Client
                                    </label>
                                    <div class="form-text">An organization or institution</div>
                                </div>
                            </div>
                        </div>
                        {% if form.client_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.client_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Contact Information -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Contact Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="first_name" class="form-label">First Name <span class="text-danger">*</span></label>
                                    {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.first_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="last_name" class="form-label">Last Name <span class="text-danger">*</span></label>
                                    {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.last_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                    {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                                    {% if form.email.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.email.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Phone <span class="text-danger">*</span></label>
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.phone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Address Section -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <h6 class="text-muted mb-3">Address Information</h6>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="civic_number" class="form-label">Civic Number</label>
                                    {{ form.civic_number(class="form-control" + (" is-invalid" if form.civic_number.errors else "")) }}
                                    {% if form.civic_number.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.civic_number.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-9">
                                    <label for="street" class="form-label">Street</label>
                                    {{ form.street(class="form-control" + (" is-invalid" if form.street.errors else "")) }}
                                    {% if form.street.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.street.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="city" class="form-label">City</label>
                                    {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.city.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="province" class="form-label">Province</label>
                                    {{ form.province(class="form-select" + (" is-invalid" if form.province.errors else "")) }}
                                    {% if form.province.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.province.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4">
                                    <label for="postal_code" class="form-label">Postal Code</label>
                                    {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                                    {% if form.postal_code.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.postal_code.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="country" class="form-label">Country</label>
                                    {{ form.country(class="form-select" + (" is-invalid" if form.country.errors else "")) }}
                                    {% if form.country.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.country.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Individual Client Fields -->
                    <div id="individual-fields" class="card mb-4" style="display: none;">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Individual Client Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="date_of_birth" class="form-label">Date of Birth</label>
                                    {{ form.date_of_birth(class="form-control" + (" is-invalid" if form.date_of_birth.errors else ""), type="date") }}
                                    {% if form.date_of_birth.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.date_of_birth.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="school_grade" class="form-label">School Grade</label>
                                    {{ form.school_grade(class="form-control" + (" is-invalid" if form.school_grade.errors else "")) }}
                                    {% if form.school_grade.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.school_grade.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Institutional Client Fields -->
                    <div id="institutional-fields" class="card mb-4" style="display: none;">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">Institutional Client Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="organization_name" class="form-label">Organization Name</label>
                                    {{ form.organization_name(class="form-control" + (" is-invalid" if form.organization_name.errors else "")) }}
                                    {% if form.organization_name.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.organization_name.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="contact_person" class="form-label">Contact Person</label>
                                    {{ form.contact_person(class="form-control" + (" is-invalid" if form.contact_person.errors else "")) }}
                                    {% if form.contact_person.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.contact_person.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Dependants Section -->
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Dependants</h5>
                            <button type="button" class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#addDependantModal">
                                <i class="fas fa-plus"></i> Add New Dependant
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="dependant-search" class="form-label">Search and Add Existing Dependants</label>
                                <div class="input-group">
                                    <input type="text" id="dependant-search" class="form-control"
                                           placeholder="Type to search for existing clients..." autocomplete="off">
                                    <button type="button" class="btn btn-outline-secondary" id="clear-search">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="search-results" class="mt-2" style="display: none;"></div>
                                <div class="form-text">Start typing a name or email to search for existing clients to add as dependants.</div>
                            </div>

                            <div id="selected-dependants" class="mt-3">
                                <h6>Selected Dependants:</h6>
                                <div id="dependants-list" class="row">
                                    <!-- Selected dependants will be displayed here -->
                                </div>
                            </div>

                            <!-- Hidden fields for form submission -->
                            {{ form.dependant_ids() }}
                            {{ form.dependant_relationships() }}
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        {{ form.notes(class="form-control", rows=3) }}
                        <div class="form-text">Optional notes about this client.</div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.clients_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add New Dependant Modal -->
<div class="modal fade" id="addDependantModal" tabindex="-1" aria-labelledby="addDependantModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addDependantModalLabel">
                    <i class="fas fa-user-plus"></i> Add New Dependant
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="dependant-form">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Note:</strong> Email and password are only needed if the dependant will log into the system.
                        For children or dependants who won't access the system directly, you can leave these fields blank.
                        The dependant will inherit the same client type as the parent.
                    </div>

                    <div class="alert alert-secondary">
                        <i class="fas fa-inherit"></i>
                        <strong>Client Type:</strong> <span id="inherited-client-type">Will inherit from parent</span>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="dependant-email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="dependant-email">
                            <div class="form-text">Optional - for system access only</div>
                        </div>
                        <div class="col-md-6">
                            <label for="dependant-password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="dependant-password" minlength="8">
                            <div class="form-text">Optional - required only if email is provided</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="dependant-first-name" class="form-label">First Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="dependant-first-name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="dependant-last-name" class="form-label">Last Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="dependant-last-name" required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="dependant-phone" class="form-label">Phone</label>
                            <input type="tel" class="form-control" id="dependant-phone">
                            <div class="form-text">Optional - dependant's phone number</div>
                        </div>
                    </div>

                    <!-- Hidden field for client type - will inherit from parent -->
                    <input type="hidden" id="dependant-client-type" value="">

                    <div class="mb-3">
                        <label for="dependant-address" class="form-label">Address</label>
                        <textarea class="form-control" id="dependant-address" rows="2"></textarea>
                    </div>

                    <!-- Individual Client Fields -->
                    <div id="dependant-individual-fields" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dependant-date-of-birth" class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" id="dependant-date-of-birth">
                            </div>
                            <div class="col-md-6">
                                <label for="dependant-school-grade" class="form-label">School Grade</label>
                                <select class="form-select" id="dependant-school-grade">
                                    <option value="">Select Grade Level</option>
                                    <option value="accueil">Accueil</option>
                                    <option value="primaire_1">Primaire 1</option>
                                    <option value="primaire_2">Primaire 2</option>
                                    <option value="primaire_3">Primaire 3</option>
                                    <option value="primaire_4">Primaire 4</option>
                                    <option value="primaire_5">Primaire 5</option>
                                    <option value="primaire_6">Primaire 6</option>
                                    <option value="secondaire_1">Secondaire 1</option>
                                    <option value="secondaire_2">Secondaire 2</option>
                                    <option value="secondaire_3">Secondaire 3</option>
                                    <option value="secondaire_4">Secondaire 4</option>
                                    <option value="secondaire_5">Secondaire 5</option>
                                    <option value="cegep">CÉGEP</option>
                                    <option value="universite_1">Université 1ère année</option>
                                    <option value="universite_2_3">Université 2e-3e année</option>
                                    <option value="adulte">Adulte</option>
                                    <option value="autre">Autre</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Institutional Client Fields -->
                    <div id="dependant-institutional-fields" style="display: none;">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="dependant-organization-name" class="form-label">Organization Name</label>
                                <input type="text" class="form-control" id="dependant-organization-name">
                            </div>
                            <div class="col-md-6">
                                <label for="dependant-contact-person" class="form-label">Contact Person</label>
                                <input type="text" class="form-control" id="dependant-contact-person">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="dependant-relationship-type" class="form-label">Relationship Type <span class="text-danger">*</span></label>
                        <select class="form-select" id="dependant-relationship-type" required>
                            <option value="">Select Relationship</option>
                            <option value="child">Child</option>
                            <option value="student">Student</option>
                            <option value="employee">Employee</option>
                            <option value="dependent">Dependent</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dependant-is-primary">
                            <label class="form-check-label" for="dependant-is-primary">
                                Primary contact for this dependant
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="dependant-notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="dependant-notes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-dependant">
                    <i class="fas fa-save"></i> Create Dependant
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Store selected dependants
        let selectedDependants = [];

        // Initialize with existing dependants if editing
        {% if existing_dependants %}
            selectedDependants = {{ existing_dependants|tojson }};
        {% endif %}

        // Client type toggle functionality
        const clientTypeRadios = document.querySelectorAll('input[name="client_type"]');
        const individualFields = document.getElementById('individual-fields');
        const institutionalFields = document.getElementById('institutional-fields');

        function toggleClientTypeFields() {
            const selectedType = document.querySelector('input[name="client_type"]:checked');
            if (selectedType) {
                if (selectedType.value === 'individual') {
                    individualFields.style.display = 'block';
                    institutionalFields.style.display = 'none';
                } else if (selectedType.value === 'institutional') {
                    individualFields.style.display = 'none';
                    institutionalFields.style.display = 'block';
                }
            }
        }

        clientTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                toggleClientTypeFields();
                // Update dependant modal if it's visible
                if (document.getElementById('addDependantModal').classList.contains('show')) {
                    setDependantClientType();
                }
            });
        });

        // Initialize on page load
        toggleClientTypeFields();

        // Function to set dependant client type based on parent client type (defined early for accessibility)
        let setDependantClientType; // Will be defined later when modal elements are available

        // Dependant search functionality
        const searchInput = document.getElementById('dependant-search');
        const searchResults = document.getElementById('search-results');
        const clearSearchBtn = document.getElementById('clear-search');
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                fetch(`/api/dependants/search?q=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        displaySearchResults(data);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        searchResults.innerHTML = '<div class="alert alert-danger">Search failed. Please try again.</div>';
                        searchResults.style.display = 'block';
                    });
            }, 300);
        });

        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            searchResults.style.display = 'none';
        });

        function displaySearchResults(dependants) {
            if (dependants.length === 0) {
                searchResults.innerHTML = '<div class="alert alert-info">No dependants found.</div>';
                searchResults.style.display = 'block';
                return;
            }

            let html = '<div class="list-group">';
            dependants.forEach(dependant => {
                // Check if already selected
                const isSelected = selectedDependants.some(dep => dep.id === dependant.id);
                if (!isSelected) {
                    html += `
                        <button type="button" class="list-group-item list-group-item-action"
                                onclick="selectDependant(${dependant.id}, '${dependant.name}', '${dependant.email}', 'dependant', '${dependant.phone}')">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${dependant.name}</strong><br>
                                    <small class="text-muted">${dependant.email} • Dependant</small>
                                </div>
                                <span class="badge bg-primary">Add</span>
                            </div>
                        </button>
                    `;
                }
            });
            html += '</div>';

            searchResults.innerHTML = html;
            searchResults.style.display = 'block';
        }

        // Make selectDependant function global
        window.selectDependant = function(id, name, email, clientType, phone) {
            // Show relationship selection modal or directly add with default relationship
            const relationshipType = prompt('Enter relationship type (child, student, employee, dependent, other):', 'child');
            if (relationshipType) {
                addDependant(id, name, email, clientType, phone, relationshipType, false);
                searchInput.value = '';
                searchResults.style.display = 'none';
            }
        };

        function addDependant(id, name, email, clientType, phone, relationshipType, isPrimary) {
            // Check if already added
            if (selectedDependants.some(dep => dep.id === id)) {
                alert('This dependant is already selected.');
                return;
            }

            const dependant = {
                id: id,
                name: name,
                email: email,
                client_type: clientType,
                phone: phone,
                relationship_type: relationshipType,
                is_primary: isPrimary
            };

            selectedDependants.push(dependant);
            updateDependantsList();
            updateHiddenFields();
        }

        function updateDependantsList() {
            const dependantsList = document.getElementById('dependants-list');

            if (selectedDependants.length === 0) {
                dependantsList.innerHTML = '<div class="col-12"><p class="text-muted">No dependants selected.</p></div>';
                return;
            }

            let html = '';
            selectedDependants.forEach((dependant, index) => {
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">${dependant.name}</h6>
                                        <p class="card-text">
                                            <small class="text-muted">${dependant.email}</small><br>
                                            <span class="badge bg-info">${dependant.relationship_type}</span>
                                            ${dependant.is_primary ? '<span class="badge bg-success">Primary</span>' : ''}
                                        </p>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="removeDependant(${index})" title="Remove">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            dependantsList.innerHTML = html;
        }

        // Make removeDependant function global
        window.removeDependant = function(index) {
            selectedDependants.splice(index, 1);
            updateDependantsList();
            updateHiddenFields();
        };

        function updateHiddenFields() {
            const dependantIds = selectedDependants.map(dep => dep.id);
            const dependantRelationships = selectedDependants.map(dep => ({
                id: dep.id,
                relationship_type: dep.relationship_type,
                is_primary: dep.is_primary
            }));

            document.getElementById('dependant_ids').value = JSON.stringify(dependantIds);
            document.getElementById('dependant_relationships').value = JSON.stringify(dependantRelationships);
        }

        // Modal functionality for creating new dependants
        const dependantModal = new bootstrap.Modal(document.getElementById('addDependantModal'));
        const dependantForm = document.getElementById('dependant-form');
        const saveDependantBtn = document.getElementById('save-dependant');
        const dependantClientTypeHidden = document.getElementById('dependant-client-type');
        const dependantIndividualFields = document.getElementById('dependant-individual-fields');
        const dependantInstitutionalFields = document.getElementById('dependant-institutional-fields');

        // Function to set dependant client type based on parent client type
        setDependantClientType = function() {
            const parentClientType = document.querySelector('input[name="client_type"]:checked');
            const inheritedClientTypeSpan = document.getElementById('inherited-client-type');

            console.log('Setting dependant client type...');
            console.log('Parent client type element:', parentClientType);
            console.log('Parent client type value:', parentClientType ? parentClientType.value : 'none');

            if (parentClientType) {
                dependantClientTypeHidden.value = parentClientType.value;
                console.log('Set hidden field value to:', dependantClientTypeHidden.value);

                // Update the display to show inherited client type
                const clientTypeLabel = parentClientType.value === 'individual' ? 'Individual' : 'Institutional';
                inheritedClientTypeSpan.textContent = `${clientTypeLabel} (inherited from parent)`;
                inheritedClientTypeSpan.className = 'text-success fw-bold';

                // Show appropriate fields based on inherited client type
                if (parentClientType.value === 'individual') {
                    dependantIndividualFields.style.display = 'block';
                    dependantInstitutionalFields.style.display = 'none';
                    console.log('Showing individual fields');
                } else if (parentClientType.value === 'institutional') {
                    dependantIndividualFields.style.display = 'none';
                    dependantInstitutionalFields.style.display = 'block';
                    console.log('Showing institutional fields');
                }
            } else {
                inheritedClientTypeSpan.textContent = 'Please select parent client type first';
                inheritedClientTypeSpan.className = 'text-warning fw-bold';
                console.log('No parent client type selected');
            }
        };

        // Set client type when modal is opened
        document.getElementById('addDependantModal').addEventListener('show.bs.modal', function() {
            console.log('Modal opening, setting client type...');
            setDependantClientType();
            console.log('Client type set to:', dependantClientTypeHidden.value);
        });

        // Save new dependant
        saveDependantBtn.addEventListener('click', function() {
            // Get form field values
            const emailField = document.getElementById('dependant-email');
            const passwordField = document.getElementById('dependant-password');
            const firstNameField = document.getElementById('dependant-first-name');
            const lastNameField = document.getElementById('dependant-last-name');
            const phoneField = document.getElementById('dependant-phone');
            const clientTypeField = document.getElementById('dependant-client-type');
            const addressField = document.getElementById('dependant-address');
            const notesField = document.getElementById('dependant-notes');

            // Debug: Log field elements
            console.log('Form fields:', {
                emailField, passwordField, firstNameField, lastNameField,
                phoneField, clientTypeField, addressField, notesField
            });

            // Debug: Log field values before processing
            console.log('Raw field values:');
            console.log('  email:', emailField ? `"${emailField.value}"` : 'field not found');
            console.log('  password:', passwordField ? `"${passwordField.value}"` : 'field not found');
            console.log('  first_name:', firstNameField ? `"${firstNameField.value}"` : 'field not found');
            console.log('  last_name:', lastNameField ? `"${lastNameField.value}"` : 'field not found');
            console.log('  phone:', phoneField ? `"${phoneField.value}"` : 'field not found');
            console.log('  client_type:', clientTypeField ? `"${clientTypeField.value}"` : 'field not found');
            console.log('  address:', addressField ? `"${addressField.value}"` : 'field not found');
            console.log('  notes:', notesField ? `"${notesField.value}"` : 'field not found');

            // Collect form data with more robust handling
            const formData = {};

            // Helper function to safely get field value
            function getFieldValue(field, trim = true) {
                if (!field) return '';
                const value = field.value || '';
                return trim ? value.trim() : value;
            }

            formData.email = getFieldValue(emailField);
            formData.password = getFieldValue(passwordField);
            formData.first_name = getFieldValue(firstNameField);
            formData.last_name = getFieldValue(lastNameField);
            formData.phone = getFieldValue(phoneField);
            formData.client_type = getFieldValue(clientTypeField, false); // Don't trim client_type
            formData.address = getFieldValue(addressField);
            formData.notes = getFieldValue(notesField);

            // Debug: Log individual field values
            console.log('Individual field values:', {
                email: emailField ? emailField.value : 'field not found',
                first_name: firstNameField ? firstNameField.value : 'field not found',
                last_name: lastNameField ? lastNameField.value : 'field not found',
                client_type: clientTypeField ? clientTypeField.value : 'field not found'
            });

            // Add type-specific fields
            if (formData.client_type === 'individual') {
                formData.date_of_birth = document.getElementById('dependant-date-of-birth').value;
                formData.school_grade = document.getElementById('dependant-school-grade').value;
            } else if (formData.client_type === 'institutional') {
                formData.organization_name = document.getElementById('dependant-organization-name').value;
                formData.contact_person = document.getElementById('dependant-contact-person').value;
            }

            // Validate required fields (only first_name and last_name are required)
            if (!formData.first_name || formData.first_name === '') {
                alert('Please fill in the First Name field.');
                console.error('First name is missing or empty:', formData.first_name);
                return;
            }

            if (!formData.last_name || formData.last_name === '') {
                alert('Please fill in the Last Name field.');
                console.error('Last name is missing or empty:', formData.last_name);
                return;
            }

            // Ensure client type is set (should be inherited from parent)
            if (!formData.client_type || formData.client_type === '') {
                alert('Client type not set. Please select a client type for the parent first.');
                console.error('Client type is missing or empty:', formData.client_type);
                return;
            }

            // If email is provided, password should also be provided for login capability
            if (formData.email && !formData.password) {
                alert('If you provide an email, please also provide a password for login capability.');
                return;
            }

            // If password is provided, email should also be provided
            if (formData.password && !formData.email) {
                alert('If you provide a password, please also provide an email for the account.');
                return;
            }

            const relationshipType = document.getElementById('dependant-relationship-type').value;
            if (!relationshipType) {
                alert('Please select a relationship type.');
                return;
            }

            const isPrimary = document.getElementById('dependant-is-primary').checked;

            // Debug: Log the form data being sent
            console.log('Form data being sent:');
            console.log('  email:', `"${formData.email}"`);
            console.log('  password:', `"${formData.password}"`);
            console.log('  first_name:', `"${formData.first_name}"`);
            console.log('  last_name:', `"${formData.last_name}"`);
            console.log('  phone:', `"${formData.phone}"`);
            console.log('  client_type:', `"${formData.client_type}"`);
            console.log('  address:', `"${formData.address}"`);
            console.log('  notes:', `"${formData.notes}"`);

            // Additional debugging for first name specifically
            console.log('=== FIRST NAME DEBUGGING ===');
            console.log('firstNameField element:', firstNameField);
            console.log('firstNameField.value raw:', firstNameField ? firstNameField.value : 'FIELD NOT FOUND');
            console.log('firstNameField.value length:', firstNameField ? firstNameField.value.length : 'N/A');
            console.log('formData.first_name:', formData.first_name);
            console.log('formData.first_name length:', formData.first_name ? formData.first_name.length : 'N/A');
            console.log('formData.first_name type:', typeof formData.first_name);
            console.log('JSON.stringify(formData):', JSON.stringify(formData));

            // Get CSRF token from meta tag
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            console.log('CSRF token:', csrfToken);

            // Show loading state
            saveDependantBtn.disabled = true;
            saveDependantBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

            // Create the dependant via API
            fetch('/api/dependants', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add to selected dependants
                    addDependant(
                        data.dependant.id,
                        data.dependant.name,
                        data.dependant.email,
                        'dependant',
                        data.dependant.phone,
                        relationshipType,
                        isPrimary
                    );

                    // Reset form and close modal
                    dependantForm.reset();
                    dependantModal.hide();

                    // Show success message
                    alert('Dependant created successfully!');
                } else {
                    alert('Error creating dependant: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to create dependant. Please try again.');
            })
            .finally(() => {
                // Reset button state
                saveDependantBtn.disabled = false;
                saveDependantBtn.innerHTML = '<i class="fas fa-save"></i> Create Dependant';
            });
        });

        // Reset modal when closed
        document.getElementById('addDependantModal').addEventListener('hidden.bs.modal', function() {
            dependantForm.reset();
            dependantIndividualFields.style.display = 'none';
            dependantInstitutionalFields.style.display = 'none';
        });

        // Initialize dependants list and hidden fields
        updateDependantsList();
        updateHiddenFields();
    });
</script>
{% endblock %}
