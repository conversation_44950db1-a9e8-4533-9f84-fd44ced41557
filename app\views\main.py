# app/views/main.py
from flask import Blueprint, redirect, url_for, render_template, flash
from flask_login import current_user, login_required
from flask_wtf import FlaskForm
from wtforms import String<PERSON>ield, PasswordField, SubmitField
from wtforms.validators import <PERSON>Required, Email
from app.extensions import db
from app.models.user import User

main = Blueprint('main', __name__)


class CreateManagerForm(FlaskForm):
    """Form for creating a new manager user."""
    email = StringField('Email Address', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Create Manager User')


@main.route('/')
def index():
    """Redirect to the appropriate dashboard based on user role or to login page if not authenticated."""
    if current_user.is_authenticated:
        if current_user.role == 'manager':
            return redirect(url_for('manager.dashboard'))
        elif current_user.role == 'tutor':
            return redirect(url_for('tutor.dashboard'))
        elif current_user.role in ['parent', 'client']:
            return redirect(url_for('client.dashboard'))

    # If not authenticated or role not recognized, redirect to login
    return redirect(url_for('auth.login'))


@main.route('/create-manager', methods=['GET', 'POST'])
@login_required
def create_manager():
    """Route to create a manager user - requires manager authentication."""
    # Only allow existing managers to create new managers
    if not current_user.is_authenticated or current_user.role != 'manager':
        flash('Access denied. Only managers can create new manager accounts.', 'danger')
        return redirect(url_for('auth.login'))

    form = CreateManagerForm()

    if form.validate_on_submit():
        # Check if user already exists
        existing_user = User.query.filter_by(email=form.email.data).first()
        if existing_user:
            flash(f'User with email {form.email.data} already exists with role: {existing_user.role}', 'warning')
            return render_template('create_manager.html', form=form)

        try:
            # Create the manager user
            manager_user = User(
                email=form.email.data,
                password=form.password.data,  # This will be hashed automatically
                role='manager'
            )

            db.session.add(manager_user)
            db.session.commit()

            flash(f'Manager user created successfully! Email: {form.email.data}', 'success')
            return redirect(url_for('manager.dashboard'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error creating manager user: {e}', 'danger')

    return render_template('create_manager.html', form=form)


