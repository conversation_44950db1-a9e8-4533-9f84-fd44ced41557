<!-- app/templates/tutor/dashboard.html -->
{% extends "base.html" %}

{% block title %}Tutor Dashboard - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">Welcome, {{ tutor.first_name }}!</h2>
        <p class="text-muted">Your tutoring dashboard</p>
    </div>
</div>

<!-- Today's Appointments -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Today's Appointments</h5>
            </div>
            <div class="card-body">
                {% if todays_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>Client</th>
                                    <th>Service</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in todays_appointments %}
                                    <tr>
                                        <td>
                                            {{ appointment.start_time.strftime('%H:%M') }} -
                                            {{ appointment.end_time.strftime('%H:%M') }}
                                        </td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            {% set tutor_service = appointment.tutor_service %}
                                            {% if tutor_service and tutor_service.service %}
                                                {{ tutor_service.service.name }}
                                            {% else %}
                                                Unknown Service
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            <a href="{{ url_for('tutor.update_appointment', id=appointment.id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> Update
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No appointments scheduled for today.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Upcoming Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Upcoming Appointments</h5>
                <a href="{{ url_for('tutor.schedule') }}" class="btn btn-sm btn-outline-primary">
                    View Schedule
                </a>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Client</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {{ appointment.start_time.strftime('%H:%M') }} -
                                            {{ appointment.end_time.strftime('%H:%M') }}
                                        </td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No upcoming appointments.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Past Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0">Recent Appointments</h5>
            </div>
            <div class="card-body">
                {% if past_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Client</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'danger' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No past appointments.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">Quick Links</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('tutor.schedule') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-calendar-alt mb-2 fa-2x"></i>
                            <span>My Schedule</span>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('tutor.client_list') }}" class="btn btn-lg btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-users mb-2 fa-2x"></i>
                            <span>My Clients</span>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('tutor.profile') }}" class="btn btn-lg btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-user-edit mb-2 fa-2x"></i>
                            <span>My Profile</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}