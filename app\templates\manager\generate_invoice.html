<!-- app/templates/manager/generate_invoice.html -->
{% extends "base.html" %}

{% block title %}Generate Invoice - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Generate Invoice</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.invoices_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Invoices
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Invoice Parameters</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('manager.generate_invoices') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client</label>
                        <select class="form-select" id="client_id" name="client_id" required>
                            <option value="">Select a client</option>
                            {% for client in clients %}
                                <option value="{{ client.id }}" {% if request.args.get('client_id') == client.id|string %}selected{% endif %}>
                                    {{ client.first_name }} {{ client.last_name }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Select the client to generate an invoice for.</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date (Optional)</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                            <div class="form-text">Only include appointments after this date.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date (Optional)</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                            <div class="form-text">Only include appointments before this date.</div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <h6 class="alert-heading">Note:</h6>
                        <p class="mb-0">This will generate an invoice for all completed appointments that haven't been invoiced yet. If date range is specified, only appointments within that range will be included.</p>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-invoice-dollar"></i> Generate Invoice
                        </button>
                        <a href="{{ url_for('manager.invoices_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}