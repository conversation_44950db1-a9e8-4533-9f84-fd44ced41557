{% extends "base.html" %}

{% block title %}Group Session Details - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-users text-primary"></i>
                Group Session Details
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">TECFÉE</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_group_sessions') }}">Group Sessions</a></li>
                    <li class="breadcrumb-item active">Session #{{ group_session.id }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                {% if group_session.status == 'scheduled' and group_session.has_minimum_participants %}
                <button type="button" class="btn btn-success" onclick="confirmSession({{ group_session.id }})">
                    <i class="fas fa-check"></i> Confirm Session
                </button>
                {% endif %}
                {% if group_session.status == 'confirmed' %}
                <form method="POST" action="{{ url_for('manager.complete_tecfee_group_session', id=group_session.id) }}"
                      style="display: inline;" onsubmit="return confirm('Mark this session as completed?')">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-flag-checkered"></i> Complete Session
                    </button>
                </form>
                {% endif %}
                <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Sessions
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Session Information -->
        <div class="col-lg-8">
            <!-- Session Overview -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Session Overview</h5>
                    <span class="badge bg-{% if group_session.status == 'scheduled' %}warning{% elif group_session.status == 'confirmed' %}success{% elif group_session.status == 'completed' %}primary{% elif group_session.status == 'cancelled' %}danger{% endif %} fs-6">
                        {{ group_session.status|title }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Module Information</h6>
                            <p><strong>Module {{ group_session.module.module_number }}:</strong> {{ group_session.module.name }}</p>
                            <p><strong>Description:</strong> {{ group_session.module.description }}</p>
                            <p><strong>Learning Objectives:</strong> {{ group_session.module.learning_objectives }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Session Details</h6>
                            <p><strong>Date:</strong> {{ group_session.session_date.strftime('%A, %B %d, %Y') }}</p>
                            <p><strong>Time:</strong> {{ group_session.session_date.strftime('%H:%M') }}</p>
                            <p><strong>Duration:</strong> {{ group_session.duration_minutes }} minutes</p>
                            <p><strong>Tutor:</strong> {{ group_session.tutor.first_name }} {{ group_session.tutor.last_name }}</p>
                        </div>
                    </div>

                    {% if group_session.notes %}
                    <hr>
                    <h6>Notes</h6>
                    <p class="text-muted">{{ group_session.notes }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Participants -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Participants
                        <span class="badge bg-info">{{ group_session.current_participants_count }}/{{ group_session.max_participants }}</span>
                    </h5>
                    {% if group_session.status in ['scheduled', 'confirmed'] %}
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addParticipantModal">
                        <i class="fas fa-user-plus"></i> Add Participant
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if participants %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Registration Date</th>
                                    <th>Attendance</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for participant, enrollment, client in participants %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                {{ client.first_name[0] }}{{ client.last_name[0] }}
                                            </div>
                                            <div>
                                                <strong>{{ client.first_name }} {{ client.last_name }}</strong><br>
                                                <small class="text-muted">{{ client.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ participant.registration_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        {% if participant.attendance_status == 'registered' %}
                                        <span class="badge bg-secondary">Registered</span>
                                        {% elif participant.attendance_status == 'attended' %}
                                        <span class="badge bg-success">Attended</span>
                                        {% elif participant.attendance_status == 'absent' %}
                                        <span class="badge bg-danger">Absent</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if group_session.status == 'confirmed' %}
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="markAttendance({{ participant.id }}, 'attended')"
                                                    title="Mark as Attended">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="markAttendance({{ participant.id }}, 'absent')"
                                                    title="Mark as Absent">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            {% if group_session.status in ['scheduled', 'confirmed'] %}
                                            <button type="button" class="btn btn-outline-warning"
                                                    onclick="removeParticipant({{ participant.id }})"
                                                    title="Remove from Session">
                                                <i class="fas fa-user-minus"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No participants yet</h6>
                        <p class="text-muted">Add participants to this group session.</p>
                        {% if group_session.status in ['scheduled', 'confirmed'] %}
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addParticipantModal">
                            <i class="fas fa-user-plus"></i> Add First Participant
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Session Statistics -->
        <div class="col-lg-4">
            <!-- Capacity Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Capacity Status</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Current Participants</span>
                            <span>{{ group_session.current_participants_count }}/{{ group_session.max_participants }}</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar"
                                 style="width: {{ (group_session.current_participants_count / group_session.max_participants * 100) if group_session.max_participants > 0 else 0 }}%">
                            </div>
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <strong class="text-warning">{{ group_session.min_participants }}</strong><br>
                                <small class="text-muted">Minimum</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2">
                                <strong class="text-primary">{{ group_session.max_participants }}</strong><br>
                                <small class="text-muted">Maximum</small>
                            </div>
                        </div>
                    </div>

                    {% if group_session.current_participants < group_session.min_participants %}
                    <div class="alert alert-warning mt-3">
                        <small>
                            <i class="fas fa-exclamation-triangle"></i>
                            Need {{ group_session.min_participants - group_session.current_participants }} more participant(s) to proceed.
                        </small>
                    </div>
                    {% elif group_session.has_minimum_participants and group_session.status == 'scheduled' %}
                    <div class="alert alert-success mt-3">
                        <small>
                            <i class="fas fa-check-circle"></i>
                            Ready to confirm! Minimum participants reached.
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Payment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Payment Information</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center mb-3">
                        <div class="col-12">
                            <div class="border rounded p-3">
                                <h4 class="text-success mb-1">${{ "%.2f"|format(group_session.total_tutor_payment) }}</h4>
                                <small class="text-muted">Current Total Payment</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        <strong>Rate per Student:</strong> ${{ "%.2f"|format(group_session.tutor_rate_per_student) }}
                    </div>
                    <div class="mb-2">
                        <strong>Current Participants:</strong> {{ group_session.current_participants }}
                    </div>

                    {% if group_session.status == 'completed' %}
                    {% set attended_count = participants | selectattr('0.attendance_status', 'equalto', 'attended') | list | length %}
                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            Final payment based on {{ attended_count }} attendee(s):
                            <strong>${{ "%.2f"|format(group_session.tutor_rate_per_student * attended_count) }}</strong>
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if group_session.status == 'scheduled' and group_session.has_minimum_participants %}
                        <button type="button" class="btn btn-success" onclick="confirmSession({{ group_session.id }})">
                            <i class="fas fa-check"></i> Confirm Session
                        </button>
                        {% endif %}

                        {% if group_session.status == 'confirmed' %}
                        <form method="POST" action="{{ url_for('manager.complete_tecfee_group_session', id=group_session.id) }}"
                              onsubmit="return confirm('Mark this session as completed?')">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-flag-checkered"></i> Complete Session
                            </button>
                        </form>
                        {% endif %}

                        {% if group_session.status in ['scheduled', 'confirmed'] %}
                        <button type="button" class="btn btn-outline-danger" onclick="cancelSession({{ group_session.id }})">
                            <i class="fas fa-times"></i> Cancel Session
                        </button>
                        {% endif %}

                        <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> View All Sessions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Participant Modal -->
<div class="modal fade" id="addParticipantModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Participant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('manager.add_participant_to_session', id=group_session.id) }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="enrollment_id" class="form-label">Select Enrollment</label>
                        <select name="enrollment_id" id="enrollment_id" class="form-select" required>
                            <option value="">Choose an enrollment...</option>
                            <!-- This would be populated with available TECFÉE enrollments -->
                        </select>
                        <div class="form-text">
                            Only active TECFÉE enrollments that are not already in this session are shown.
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Participant</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 14px;
    font-weight: bold;
}
</style>

<script>
function confirmSession(sessionId) {
    if (confirm('Confirm this group session? This will notify all participants.')) {
        fetch(`/manager/tecfee/group-sessions/${sessionId}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            console.log('Confirm response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Confirm response data:', data);
            if (data.success) {
                alert('Session confirmed successfully!');
                location.reload();
            } else {
                alert('Error confirming session: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while confirming the session: ' + error.message);
        });
    }
}

function cancelSession(sessionId) {
    if (confirm('Cancel this group session? This action cannot be undone and will notify all participants.')) {
        const csrfToken = document.querySelector('meta[name=csrf-token]').getAttribute('content');
        const formData = new FormData();
        formData.append('csrf_token', csrfToken);

        fetch(`/manager/tecfee/group-sessions/${sessionId}/cancel`, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response ok:', response.ok);

            if (!response.ok) {
                return response.text().then(text => {
                    console.log('Error response body:', text);
                    throw new Error(`HTTP error! status: ${response.status}, body: ${text.substring(0, 200)}`);
                });
            }

            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                alert('Session cancelled successfully!');
                location.reload();
            } else {
                alert('Error cancelling session: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error details:', error);
            alert('An error occurred while cancelling the session: ' + error.message);
        });
    }
}

function markAttendance(participantId, status) {
    fetch(`/manager/tecfee/participants/${participantId}/attendance`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating attendance: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating attendance.');
    });
}

function removeParticipant(participantId) {
    if (confirm('Remove this participant from the session?')) {
        fetch(`/manager/tecfee/participants/${participantId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error removing participant: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while removing the participant.');
        });
    }
}
</script>
{% endblock %}
