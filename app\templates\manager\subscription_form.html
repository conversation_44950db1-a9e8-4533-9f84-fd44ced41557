<!-- app/templates/manager/subscription_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.subscriptions_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Subscriptions
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if subscription %}action="{{ url_for('manager.edit_subscription', id=subscription.id) }}"{% else %}action="{{ url_for('manager.new_subscription') }}"{% endif %}>
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client</label>
                        {{ form.client_id(class="form-control" + (" is-invalid" if form.client_id.errors else "")) }}
                        {% if form.client_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="plan_id" class="form-label">Subscription Plan</label>
                        {{ form.plan_id(class="form-control" + (" is-invalid" if form.plan_id.errors else "")) }}
                        {% if form.plan_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.plan_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div id="plan-details" class="form-text mt-2"></div>
                    </div>

                    <div class="mb-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else ""), type="date") }}
                        {% if form.start_date.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.start_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="alert alert-info mb-4">
                        <h6 class="alert-heading">Important Information</h6>
                        <p class="mb-0">The subscription will start on the selected date and will last for the duration of the selected plan. Any transport fees will be waived for appointments made with this subscription.</p>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.subscriptions_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show plan details when a plan is selected
        const planSelect = document.getElementById('plan_id');
        const planDetails = document.getElementById('plan-details');

        if (planSelect && planDetails) {
            planSelect.addEventListener('change', function() {
                // Get the selected option text
                const selectedOption = planSelect.options[planSelect.selectedIndex];

                if (selectedOption.value) {
                    // Display plan details
                    planDetails.innerHTML = `
                        <div class="card mt-2">
                            <div class="card-body">
                                <h6 class="card-title">Plan Details</h6>
                                <p>${selectedOption.text}</p>
                            </div>
                        </div>
                    `;
                } else {
                    planDetails.innerHTML = '';
                }
            });

            // Trigger change event if a plan is already selected
            if (planSelect.value) {
                planSelect.dispatchEvent(new Event('change'));
            }
        }
    });
</script>
{% endblock %}