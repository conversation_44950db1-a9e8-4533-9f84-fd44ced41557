# app/utils/email.py
from flask import current_app, render_template
from flask_mail import Message
from threading import Thread
from app import mail

def send_async_email(app, msg):
    """Send email asynchronously."""
    with app.app_context():
        mail.send(msg)

def send_email(subject, recipients, text_body, html_body, sender=None):
    """Send an email with both plain text and HTML versions."""
    sender = sender or current_app.config['MAIL_DEFAULT_SENDER']
    msg = Message(subject, sender=sender, recipients=recipients)
    msg.body = text_body
    msg.html = html_body

    # Send email asynchronously
    Thread(
        target=send_async_email,
        args=(current_app._get_current_object(), msg)
    ).start()

def send_password_reset_email(user, token):
    """Send a password reset email to a user."""
    from flask import url_for
    reset_url = url_for('auth.reset_password', token=token, _external=True)

    send_email(
        subject='Password Reset Request',
        recipients=[user.email],
        text_body=render_template('emails/reset_password.txt', user=user, reset_url=reset_url),
        html_body=render_template('emails/reset_password.html', user=user, reset_url=reset_url)
    )

def send_invoice_email(invoice, recipient_email, message=None):
    """Send an invoice email to a client."""
    from flask import url_for
    invoice_url = url_for('client.view_invoice', id=invoice.id, _external=True)

    send_email(
        subject=f'Invoice #{invoice.id} - Tutoring Services',
        recipients=[recipient_email],
        text_body=render_template('emails/invoice.txt',
                                 invoice=invoice,
                                 invoice_url=invoice_url,
                                 message=message),
        html_body=render_template('emails/invoice.html',
                                 invoice=invoice,
                                 invoice_url=invoice_url,
                                 message=message)
    )

def send_attendance_confirmation_email(appointment):
    """Send an attendance confirmation email to a tutor."""
    from flask import url_for
    from itsdangerous import URLSafeTimedSerializer
    from flask import current_app

    # Generate a token for the tutor
    s = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    token = s.dumps(appointment.tutor_id, salt='attendance-confirmation')

    send_email(
        subject=f'Attendance Confirmation Required - {appointment.start_time.strftime("%Y-%m-%d")}',
        recipients=[appointment.tutor.user.email],
        text_body=render_template('emails/attendance_confirmation.txt',
                                 appointment=appointment,
                                 tutor=appointment.tutor,
                                 token=token),
        html_body=render_template('emails/attendance_confirmation.html',
                                 appointment=appointment,
                                 tutor=appointment.tutor,
                                 token=token)
    )

def send_appointment_reminder(appointment):
    """Send a reminder email for an upcoming appointment."""
    from flask import url_for

    # Send to client
    if appointment.client and (appointment.client.email or (appointment.client.user and appointment.client.user.email)):
        client_email = appointment.client.email or appointment.client.user.email
        client_url = url_for('client.view_appointment', id=appointment.id, _external=True)

        send_email(
            subject='Upcoming Tutoring Appointment Reminder',
            recipients=[client_email],
            text_body=render_template('emails/appointment_reminder_client.txt',
                                    appointment=appointment,
                                    url=client_url),
            html_body=render_template('emails/appointment_reminder_client.html',
                                    appointment=appointment,
                                    url=client_url)
        )

    # Send to tutor
    tutor_email = appointment.tutor.user.email
    tutor_url = url_for('tutor.view_appointment', id=appointment.id, _external=True)

    send_email(
        subject='Upcoming Tutoring Appointment Reminder',
        recipients=[tutor_email],
        text_body=render_template('emails/appointment_reminder_tutor.txt',
                                 appointment=appointment,
                                 url=tutor_url),
        html_body=render_template('emails/appointment_reminder_tutor.html',
                                 appointment=appointment,
                                 url=tutor_url)
    )