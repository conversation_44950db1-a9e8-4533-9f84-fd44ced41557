<!-- app/templates/auth/reset_password_request.html -->
{% extends "base.html" %}

{% block title %}Reset Password - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Reset Password</h4>
            </div>
            <div class="card-body">
                <p class="mb-3">Enter your email address to receive a password reset link.</p>
                
                <form method="POST" action="{{ url_for('auth.reset_password_request') }}">
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Remember your password? <a href="{{ url_for('auth.login') }}">Login here</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}