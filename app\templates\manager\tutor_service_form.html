<!-- app/templates/manager/tutor_service_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.services_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Services
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if tutor_service %}action="{{ url_for('manager.edit_tutor_service', id=tutor_service.id) }}"{% else %}action="{{ url_for('manager.new_tutor_service') }}"{% endif %}>
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label for="tutor_id" class="form-label">Tutor</label>
                        {{ form.tutor_id(class="form-control" + (" is-invalid" if form.tutor_id.errors else "")) }}
                        {% if form.tutor_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.tutor_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="service_id" class="form-label">Service</label>
                        {{ form.service_id(class="form-control" + (" is-invalid" if form.service_id.errors else "")) }}
                        {% if form.service_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.service_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="tutor_rate" class="form-label">Tutor Rate ($)</label>
                            {{ form.tutor_rate(class="form-control" + (" is-invalid" if form.tutor_rate.errors else "")) }}
                            {% if form.tutor_rate.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.tutor_rate.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Amount paid to the tutor per hour.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="client_rate" class="form-label">Client Rate ($)</label>
                            {{ form.client_rate(class="form-control" + (" is-invalid" if form.client_rate.errors else "")) }}
                            {% if form.client_rate.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.client_rate.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Amount charged to the client per hour.</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="transport_fee" class="form-label">Transport Fee ($)</label>
                            {{ form.transport_fee(class="form-control" + (" is-invalid" if form.transport_fee.errors else ""), step="0.01", min="0") }}
                            {% if form.transport_fee.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.transport_fee.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Transport fee charged per session.</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="transport_fee_description" class="form-label">Transport Fee Description</label>
                        {{ form.transport_fee_description(class="form-control" + (" is-invalid" if form.transport_fee_description.errors else ""), rows=3) }}
                        {% if form.transport_fee_description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.transport_fee_description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Optional description of the transport fee.</div>
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.services_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}