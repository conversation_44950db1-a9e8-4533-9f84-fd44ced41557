<!-- app/templates/manager/service_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.services_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Services
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if service %}action="{{ url_for('manager.edit_service', id=service.id) }}"{% else %}action="{{ url_for('manager.new_service') }}"{% endif %}>
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Service Name</label>
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        {{ form.description(class="form-control", rows=3) }}
                        <div class="form-text">Provide details about what this service includes.</div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="default_price" class="form-label">Default Price ($)</label>
                            {{ form.default_price(class="form-control" + (" is-invalid" if form.default_price.errors else "")) }}
                            {% if form.default_price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.default_price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Standard hourly rate charged to clients.</div>
                        </div>
                        <div class="col-md-6">
                            <label for="duration_minutes" class="form-label">Duration (minutes)</label>
                            {{ form.duration_minutes(class="form-control" + (" is-invalid" if form.duration_minutes.errors else "")) }}
                            {% if form.duration_minutes.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.duration_minutes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Standard session length (e.g., 60, 90, 120 minutes).</div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.services_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
