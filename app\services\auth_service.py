# app/services/auth_service.py
from app.extensions import db
from app.models.user import User
from app.models.manager import Manager
from app.models.client import Client
from app.models.tutor import Tu<PERSON>
from app.utils.email import send_password_reset_email
from flask_login import login_user
from datetime import datetime
import uuid

class AuthService:
    @staticmethod
    def authenticate_user(email, password, remember=False):
        """
        Authenticate a user with email and password.

        Args:
            email: User's email address
            password: User's password
            remember: Whether to remember the user's session

        Returns:
            Tuple of (success, user, message)
        """
        user = User.query.filter_by(email=email).first()

        if not user:
            return False, None, "Invalid email or password"

        if not user.is_active:
            return False, None, "Your account has been deactivated"

        if not user.check_password(password):
            return False, None, "Invalid email or password"

        # Update last login time
        user.last_login = datetime.utcnow()
        db.session.commit()

        # Login the user
        login_user(user, remember=remember)

        return True, user, "Login successful"

    @staticmethod
    def initiate_password_reset(email):
        """
        Initiate the password reset process for a user.

        Args:
            email: User's email address

        Returns:
            Tuple of (success, message)
        """
        user = User.query.filter_by(email=email).first()

        if not user:
            # Don't reveal that the email doesn't exist for security reasons
            return True, "If your email exists in our system, a password reset link has been sent."

        token = user.generate_reset_token()
        db.session.commit()

        # Send password reset email
        send_password_reset_email(user, token)

        return True, "If your email exists in our system, a password reset link has been sent."

    @staticmethod
    def reset_password(token, new_password):
        """
        Reset a user's password using a reset token.

        Args:
            token: Password reset token
            new_password: New password to set

        Returns:
            Tuple of (success, message)
        """
        user = User.query.filter_by(reset_token=token).first()

        if not user or not user.verify_reset_token(token):
            return False, "Invalid or expired token"

        # Set new password
        user.set_password(new_password)
        user.clear_reset_token()
        db.session.commit()

        return True, "Your password has been reset"

    @staticmethod
    def get_dashboard_url(user):
        """
        Get the appropriate dashboard URL for a user based on their role.

        Args:
            user: User object

        Returns:
            Dashboard URL for the user's role
        """
        if not user:
            return None

        if user.role == 'manager':
            return 'manager.dashboard'
        elif user.role == 'tutor':
            return 'tutor.dashboard'
        elif user.role == 'client':
            return 'client.dashboard'
        elif user.role == 'parent':  # Legacy support
            return 'client.dashboard'
        else:
            return 'auth.login'