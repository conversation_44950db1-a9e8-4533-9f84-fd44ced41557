/**
 * Enhanced Navigation Functionality for <PERSON><PERSON> Interface
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run for tutor role
    if (!document.querySelector('.navbar-nav li a[href*="tutor"]')) {
        return;
    }

    // Set active navigation item based on current URL
    setActiveNavItem();

    // Generate breadcrumbs based on current page
    generateBreadcrumbs();
});

/**
 * Sets the active navigation item based on the current URL
 */
function setActiveNavItem() {
    const currentPath = window.location.pathname;

    // Find all nav links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    // Check each link against the current path
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/' && href !== '/tutor') {
            // If this is a dropdown toggle, add active class to parent
            if (link.classList.contains('dropdown-toggle')) {
                link.parentElement.classList.add('active');
            }

            // Add active class to the link itself
            link.classList.add('active');
        }
    });

    // Also check dropdown items
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/' && href !== '/tutor') {
            // Add active class to the item
            item.classList.add('active');
            item.setAttribute('aria-current', 'page');

            // Also activate the parent dropdown
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                const dropdownToggle = dropdownMenu.previousElementSibling;
                if (dropdownToggle && dropdownToggle.classList.contains('dropdown-toggle')) {
                    dropdownToggle.classList.add('active');
                    dropdownToggle.parentElement.classList.add('active');
                }
            }
        }
    });
}

/**
 * Generates breadcrumbs based on the current page
 */
function generateBreadcrumbs() {
    const breadcrumbContainer = document.querySelector('.breadcrumb');
    if (!breadcrumbContainer) return;

    // Clear existing breadcrumbs except the first one (Dashboard)
    while (breadcrumbContainer.children.length > 1) {
        breadcrumbContainer.removeChild(breadcrumbContainer.lastChild);
    }

    // Get page title
    const pageTitle = document.title.split(' - ')[0];

    // Get current path segments
    const pathSegments = window.location.pathname.split('/').filter(segment => segment);

    // Map of path segments to readable names
    const pathMap = {
        'tutor': 'Dashboard',
        'schedule': 'Schedule',
        'appointments': 'Appointments',
        'clients': 'Clients',
        'client': 'Client',
        'payments': 'Payments',
        'profile': 'Profile',
        'availability': 'Availability',
        'time-off-requests': 'Time-Off Requests',
        'new': 'New',
        'edit': 'Edit',
        'view': 'View'
    };

    // Build breadcrumbs based on path segments
    if (pathSegments.length > 1) {
        // Skip the first segment (tutor) as it's already in the first breadcrumb
        for (let i = 1; i < pathSegments.length; i++) {
            const segment = pathSegments[i];

            // Skip numeric segments (likely IDs)
            if (!isNaN(segment)) continue;

            // Get readable name for the segment
            const readableSegment = pathMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');

            // For the last segment, add as active breadcrumb
            if (i === pathSegments.length - 1 || (!isNaN(pathSegments[i+1]) && i === pathSegments.length - 2)) {
                const li = document.createElement('li');
                li.className = 'breadcrumb-item active';
                li.setAttribute('aria-current', 'page');
                li.textContent = readableSegment;
                breadcrumbContainer.appendChild(li);
            } else {
                // For intermediate segments, add as links
                const li = document.createElement('li');
                li.className = 'breadcrumb-item';

                const a = document.createElement('a');
                a.href = '/' + pathSegments.slice(0, i + 1).join('/');
                a.textContent = readableSegment;

                li.appendChild(a);
                breadcrumbContainer.appendChild(li);
            }
        }
    }
}
