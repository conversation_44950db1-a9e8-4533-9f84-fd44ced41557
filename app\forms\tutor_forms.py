# app/forms/tutor_forms.py
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, TextAreaField, SelectField, SubmitField, DateField
from wtforms.validators import Optional, Length, EqualTo, DataRequired, ValidationError
from datetime import date

class TutorProfileForm(FlaskForm):
    # Contact Information
    phone = StringField('Phone Number', validators=[DataRequired()])

    # Address Information
    street_address = StringField('Street Address', validators=[Optional()])
    city = StringField('City', validators=[Optional()])
    province = StringField('Province/State', validators=[Optional()])
    zip_code = StringField('Zip/Postal Code', validators=[Optional()])
    country = StringField('Country', validators=[Optional()])

    # Personal Information
    birthdate = DateField('Birthdate', format='%Y-%m-%d', validators=[Optional()])

    # Banking Information
    bank_transit_number = StringField('Bank Transit Number', validators=[Optional()])
    bank_institution_number = StringField('Bank Institution Number', validators=[Optional()])
    bank_account_number = StringField('Bank Account Number', validators=[Optional()])

    # Professional Information
    bio = TextAreaField('Bio', validators=[Optional()])
    qualifications = TextAreaField('Qualifications', validators=[Optional()])

    # Password Change
    password = PasswordField('New Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long.')
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        Optional(),
        EqualTo('password', message='Passwords must match.')
    ])

    submit = SubmitField('Update Profile')

class AppointmentNotesForm(FlaskForm):
    status = SelectField('Status', choices=[
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no-show', 'No Show')
    ], validators=[DataRequired()])
    notes = TextAreaField('Session Notes', validators=[Optional()])
    submit = SubmitField('Update Appointment')

class TimeOffRequestForm(FlaskForm):
    start_date = DateField('Start Date', format='%Y-%m-%d', validators=[DataRequired()])
    end_date = DateField('End Date', format='%Y-%m-%d', validators=[DataRequired()])
    reason = TextAreaField('Reason for Time Off', validators=[DataRequired()])
    submit = SubmitField('Submit Request')

    def validate_start_date(self, field):
        if field.data < date.today():
            raise ValidationError('Start date cannot be in the past.')

    def validate_end_date(self, field):
        if field.data < self.start_date.data:
            raise ValidationError('End date must be after or equal to start date.')