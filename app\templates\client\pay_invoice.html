<!-- app/templates/client/pay_invoice.html -->
{% extends "base.html" %}

{% block title %}{{ t('invoices.pay_invoice') }} #{{ invoice.id }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('invoices.pay_invoice') }} #{{ invoice.id }}</h2>
        <p class="text-muted">{{ t('invoices.secure_payment') }}</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('invoices.payment_details') }}</h5>
            </div>
            <div class="card-body">
                <div id="payment-element">
                    <!-- Stripe Elements will be inserted here -->
                </div>
                <div id="payment-message" class="mt-3"></div>
            </div>
            <div class="card-footer">
                <button id="submit-payment" class="btn btn-primary">
                    {{ t('invoices.pay_now') }} ${{ "%.2f"|format(invoice.total_amount) }}
                </button>
                <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-outline-secondary ms-2">
                    {{ t('invoices.cancel') }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('invoices.invoice_summary') }}</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ t('invoices.invoice_number') }}</span>
                    <span>#{{ invoice.id }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ t('invoices.invoice_date') }}</span>
                    <span>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ t('invoices.due_date') }}</span>
                    <span>{{ invoice.due_date.strftime('%Y-%m-%d') }}</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ t('invoices.subtotal') }}</span>
                    <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>{{ t('invoices.tax') }}</span>
                    <span>$0.00</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold">
                    <span>{{ t('invoices.total') }}</span>
                    <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Stripe
        const stripe = Stripe("{{ config['STRIPE_PUBLISHABLE_KEY'] }}");
        let elements;

        // Create payment intent
        fetch("{{ url_for('client.pay_invoice', id=invoice.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': "{{ csrf_token() }}"
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.client_secret) {
                // Initialize Elements with the client secret
                elements = stripe.elements({
                    clientSecret: data.client_secret,
                    appearance: {
                        theme: 'stripe',
                        variables: {
                            colorPrimary: '#0d6efd',
                        }
                    }
                });

                // Create and mount the Payment Element
                const paymentElement = elements.create('payment');
                paymentElement.mount('#payment-element');

                // Handle form submission
                document.getElementById('submit-payment').addEventListener('click', async function() {
                    // Show loading state
                    this.disabled = true;
                    this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ t("invoices.processing") }}';

                    // Confirm payment
                    const { error } = await stripe.confirmPayment({
                        elements,
                        confirmParams: {
                            return_url: "{{ url_for('client.payment_success', invoice_id=invoice.id, _external=True) }}"
                        }
                    });

                    if (error) {
                        // Show error message
                        document.getElementById('payment-message').innerHTML = `
                            <div class="alert alert-danger">
                                ${error.message}
                            </div>
                        `;

                        // Reset button
                        this.disabled = false;
                        this.innerHTML = '{{ t("invoices.pay_now") }} ${{ "%.2f"|format(invoice.total_amount) }}';
                    }
                });
            } else {
                document.getElementById('payment-message').innerHTML = `
                    <div class="alert alert-danger">
                        ${data.error || '{{ t("invoices.payment_error") }}'}
                    </div>
                `;
                document.getElementById('submit-payment').disabled = true;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('payment-message').innerHTML = `
                <div class="alert alert-danger">
                    {{ t("invoices.payment_error") }}
                </div>
            `;
            document.getElementById('submit-payment').disabled = true;
        });
    });
</script>
{% endblock %}
