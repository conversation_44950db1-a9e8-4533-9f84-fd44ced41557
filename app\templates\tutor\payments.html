<!-- app/templates/tutor/payments.html -->
{% extends "base.html" %}

{% block title %}My Payments - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">My Payments</h2>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('tutor.payments') }}" class="row g-3 filter-form">
            <div class="col-md-10">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>All Payments</option>
                    <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="paid" {% if current_status == 'paid' %}selected{% endif %}>Paid</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Payment Summary Card -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Payment Summary</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">Service Earnings</h5>
                        <p class="display-6">${{ "%.2f"|format(service_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">Transport Fees</h5>
                        <p class="display-6">${{ "%.2f"|format(transport_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">Total Earnings</h5>
                        <p class="display-6">${{ "%.2f"|format(grand_total) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">Payment History</h5>
    </div>
    <div class="card-body">
        {% if payments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Appointment</th>
                            <th>Client</th>
                            <th>Service Amount</th>
                            <th>Transport Fee</th>
                            <th>Total</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                            <tr>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        {{ payment.insert_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('tutor.view_appointment', id=payment.appointment_id) }}">
                                        {{ payment.appointment.start_time.strftime('%Y-%m-%d %H:%M') }}
                                    </a>
                                </td>
                                <td>{{ payment.appointment.client.first_name }} {{ payment.appointment.client.last_name }}</td>
                                <td>${{ "%.2f"|format(payment.service_amount) }}</td>
                                <td>${{ "%.2f"|format(payment.transport_amount) }}</td>
                                <td><strong>${{ "%.2f"|format(payment.total_amount) }}</strong></td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success">Paid</span>
                                    {% elif payment.status == 'ready' %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h5>No payments found</h5>
                <p class="text-muted">No payments match your current filters or no payments have been processed yet.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Payment Process Information -->
<div class="card shadow mt-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Payment Process Information</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>How payments work:</h6>
                <ol>
                    <li>Payments are processed for completed sessions.</li>
                    <li>When you mark an appointment as "Completed", it becomes eligible for payment.</li>
                    <li>Management reviews the appointment and marks payment as "Ready".</li>
                    <li>Payments are processed on a regular schedule (typically weekly).</li>
                    <li>Transport fees are included with the service payment.</li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6>Payment status definitions:</h6>
                <ul>
                    <li><strong>Pending</strong>: Payment has been calculated but not yet processed.</li>
                    <li><strong>Paid</strong>: Payment has been processed and sent.</li>
                </ul>
                <p class="mt-3">If you have any questions about your payments, please contact the administrative team.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}