{% extends "base.html" %}

{% block title %}Enroll Client in TECFÉE - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-user-plus text-primary"></i>
                Enroll Client in TECFÉE Program
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">TECFÉE</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_enrollments') }}">Enrollments</a></li>
                    <li class="breadcrumb-item active">New Enrollment</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.tecfee_enrollments') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Enrollments
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Enrollment Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Enrollment Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <!-- Client Selection -->
                        <div class="mb-4">
                            <label for="{{ form.client_id.id }}" class="form-label">
                                {{ form.client_id.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.client_id(class="form-select") }}
                            {% if form.client_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.client_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Select the client to enroll in the TECFÉE program. 
                                <a href="{{ url_for('manager.new_client') }}" target="_blank">Create new client</a> if not listed.
                            </div>
                        </div>

                        <!-- Pricing Option -->
                        <div class="mb-4">
                            <label for="{{ form.pricing_type.id }}" class="form-label">
                                {{ form.pricing_type.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.pricing_type(class="form-select") }}
                            {% if form.pricing_type.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.pricing_type.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Choose between individual session pricing or the full package discount.
                            </div>
                        </div>

                        <!-- Start Date -->
                        <div class="mb-4">
                            <label for="{{ form.start_date.id }}" class="form-label">
                                {{ form.start_date.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.start_date(class="form-control") }}
                            {% if form.start_date.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.start_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                The date when the client will begin the program.
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="{{ form.notes.id }}" class="form-label">
                                {{ form.notes.label.text }}
                            </label>
                            {{ form.notes(class="form-control", rows="3", placeholder="Optional notes about this enrollment...") }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('manager.tecfee_enrollments') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Program Information Sidebar -->
        <div class="col-lg-4">
            <!-- Program Overview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">TECFÉE Program Overview</h6>
                </div>
                <div class="card-body">
                    <h6 class="text-primary">PARCOURS COMPLET LINGUISTIQUE TECFÉE</h6>
                    <p class="small text-muted mb-3">
                        Programme complet de préparation au test TECFÉE en 9 sessions incluant manipulation syntaxique, 
                        connecteurs, pléonasmes, affixes et expressions, et simulation d'examen.
                    </p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-primary">9</strong><br>
                                <small class="text-muted">Sessions</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-primary">60</strong><br>
                                <small class="text-muted">Minutes Each</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-success">4-10</strong><br>
                                <small class="text-muted">Students</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-info">Online</strong><br>
                                <small class="text-muted">Format</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Pricing Options</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                            <div>
                                <strong>Per Session</strong><br>
                                <small class="text-muted">Pay as you go</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">$44.99</strong><br>
                                <small class="text-muted">per session</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center p-3 border rounded bg-light">
                            <div>
                                <strong>Full Package</strong><br>
                                <small class="text-success">Save $10.00</small>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">$395.00</strong><br>
                                <small class="text-muted">all 9 sessions</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="fas fa-info-circle"></i>
                            The full package saves clients $10 compared to individual session pricing.
                        </small>
                    </div>
                </div>
            </div>

            <!-- Enrollment Process -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">What Happens Next?</h6>
                </div>
                <div class="card-body">
                    <div class="timeline-sm">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <strong>Enrollment Created</strong><br>
                                <small class="text-muted">Client is enrolled in all 9 modules</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <strong>Group Sessions</strong><br>
                                <small class="text-muted">Client can join available group sessions</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <strong>Progress Tracking</strong><br>
                                <small class="text-muted">Monitor completion of each module</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline-sm {
    position: relative;
    padding-left: 20px;
}

.timeline-sm::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -16px;
    top: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
}

.timeline-content {
    margin-left: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-update pricing display when selection changes
    const pricingSelect = document.getElementById('{{ form.pricing_type.id }}');
    if (pricingSelect) {
        pricingSelect.addEventListener('change', function() {
            // Could add dynamic pricing display updates here
        });
    }
    
    // Set minimum date to today
    const startDateInput = document.getElementById('{{ form.start_date.id }}');
    if (startDateInput) {
        const today = new Date().toISOString().split('T')[0];
        startDateInput.setAttribute('min', today);
    }
});
</script>
{% endblock %}
