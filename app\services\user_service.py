# app/services/user_service.py
from app.extensions import db
from app.models.user import User
from app.models.manager import Manager
from app.models.client import Client
from app.models.tutor import <PERSON><PERSON>
from datetime import datetime
from werkzeug.security import generate_password_hash

class UserService:
    @staticmethod
    def create_manager(email, password, first_name, last_name, phone=None):
        """
        Create a new manager user.

        Args:
            email: Manager's email address
            password: Manager's password
            first_name: Manager's first name
            last_name: Manager's last name
            phone: Manager's phone number (optional)

        Returns:
            Tuple of (success, manager, message)
        """
        # Check if email is already in use
        if User.query.filter_by(email=email).first():
            return False, None, "Email is already in use"

        try:
            # Create user with manager role
            user = User(
                email=email,
                password=password,
                role='manager'
            )
            db.session.add(user)
            db.session.flush()  # Get user ID without committing

            # Create manager profile
            manager = Manager(
                user_id=user.id,
                first_name=first_name,
                last_name=last_name,
                phone=phone
            )
            db.session.add(manager)
            db.session.commit()

            return True, manager, "Manager created successfully"

        except Exception as e:
            db.session.rollback()
            return False, None, f"Error creating manager: {str(e)}"

    @staticmethod
    def create_client(email, password, first_name, last_name, phone, address=None, client_type='individual', date_of_birth=None, notes=None):
        """
        Create a new client user.

        Args:
            email: Client's email address
            password: Client's password
            first_name: Client's first name
            last_name: Client's last name
            phone: Client's phone number
            address: Client's address (optional)
            client_type: Type of client ('individual' or 'institutional')
            date_of_birth: Client's date of birth (for individual clients)
            notes: Notes about the client (optional)

        Returns:
            Tuple of (success, client, message)
        """
        # Check if email is already in use
        if User.query.filter_by(email=email).first():
            return False, None, "Email is already in use"

        try:
            # Create user with client role (email not verified yet)
            user = User(
                email=email,
                password=password,
                role='client'
            )

            # Generate verification token
            verification_token = user.generate_verification_token()

            db.session.add(user)
            db.session.flush()  # Get user ID without committing

            # Create client profile
            if client_type == 'individual':
                from app.models.client import IndividualClient
                client = IndividualClient(
                    user_id=user.id,
                    first_name=first_name,
                    last_name=last_name,
                    phone=phone,
                    address=address,
                    date_of_birth=date_of_birth,
                    notes=notes
                )
            else:
                from app.models.client import InstitutionalClient
                client = InstitutionalClient(
                    user_id=user.id,
                    first_name=first_name,
                    last_name=last_name,
                    phone=phone,
                    address=address,
                    institution_name=notes or f"{first_name} {last_name}",  # Use name as institution name if not provided
                    notes=notes
                )

            db.session.add(client)

            # Commit the transaction before sending email
            db.session.commit()

            # Send verification email
            from app.services.email_service import EmailService
            email_sent = EmailService.send_verification_email(user, verification_token)

            if email_sent:
                return True, client, "Client account created successfully. Please check your email to verify your account."
            else:
                return True, user, "Client account created successfully. However, there was an issue sending the verification email. Please contact support."

        except Exception as e:
            db.session.rollback()
            return False, None, f"Error creating client account: {str(e)}"

    @staticmethod
    def create_google_client(email, first_name, last_name, google_id):
        """Create a new client account using Google authentication."""
        # Check if email is already in use
        if User.query.filter_by(email=email).first():
            return False, None, "Email is already in use"

        try:
            # Create user with Google authentication (no password needed)
            user = User(
                email=email,
                password=None,  # No password for Google users
                role='client',
                google_id=google_id,
                auth_provider='google'
            )

            db.session.add(user)
            db.session.flush()  # Get user ID without committing

            # Create client profile with Google info
            from app.models.client import IndividualClient
            client = IndividualClient(
                user_id=user.id,
                first_name=first_name,
                last_name=last_name,
                phone='',  # Will be filled in profile completion
                address='',  # Will be filled in profile completion
                notes='Created via Google Sign-In'
            )

            db.session.add(client)
            db.session.commit()

            return True, client, "Google client account created successfully"

        except Exception as e:
            db.session.rollback()
            return False, None, f"Error creating Google client account: {str(e)}"

    @staticmethod
    def create_google_user(email, first_name, last_name, google_id, user_type):
        """Create a new user account using Google authentication for any user type."""
        # Check if email is already in use
        if User.query.filter_by(email=email).first():
            return False, None, "Email is already in use"

        try:
            # Create user with Google authentication (no password needed)
            user = User(
                email=email,
                password=None,  # No password for Google users
                role=user_type,
                google_id=google_id,
                auth_provider='google'
            )

            db.session.add(user)
            db.session.flush()  # Get user ID without committing

            # Create appropriate profile based on user type
            if user_type == 'manager':
                from app.models.manager import Manager
                profile = Manager(
                    user_id=user.id,
                    first_name=first_name,
                    last_name=last_name,
                    phone='',  # Will be filled later
                    notes='Created via Google Sign-In'
                )
            elif user_type == 'tutor':
                from app.models.tutor import Tutor
                profile = Tutor(
                    user_id=user.id,
                    first_name=first_name,
                    last_name=last_name,
                    phone='',  # Will be filled later
                    notes='Created via Google Sign-In'
                )
            elif user_type == 'client':
                from app.models.client import IndividualClient
                profile = IndividualClient(
                    user_id=user.id,
                    first_name=first_name,
                    last_name=last_name,
                    phone='',  # Will be filled later
                    address='',  # Will be filled later
                    notes='Created via Google Sign-In'
                )
            else:
                db.session.rollback()
                return False, None, f"Invalid user type: {user_type}"

            db.session.add(profile)
            db.session.commit()

            return True, user, f"Google {user_type} account created successfully"

        except Exception as e:
            db.session.rollback()
            return False, None, f"Error creating Google {user_type} account: {str(e)}"

    @staticmethod
    def create_tutor(email, password, first_name, last_name, phone,
                    street_address=None, city=None, province=None, zip_code=None, country=None,
                    birthdate=None, bank_transit_number=None, bank_institution_number=None,
                    bank_account_number=None, bio=None, qualifications=None):
        """
        Create a new tutor user.

        Args:
            email: Tutor's email address
            password: Tutor's password
            first_name: Tutor's first name
            last_name: Tutor's last name
            phone: Tutor's phone number
            street_address: Tutor's street address (optional)
            city: Tutor's city (optional)
            province: Tutor's province/state (optional)
            zip_code: Tutor's zip/postal code (optional)
            country: Tutor's country (optional)
            birthdate: Tutor's birthdate (optional)
            bank_transit_number: Tutor's bank transit number (optional)
            bank_institution_number: Tutor's bank institution number (optional)
            bank_account_number: Tutor's bank account number (optional)
            bio: Tutor's bio (optional)
            qualifications: Tutor's qualifications (optional)

        Returns:
            Tuple of (success, tutor, message)
        """
        # Check if email is already in use
        if User.query.filter_by(email=email).first():
            return False, None, "Email is already in use"

        try:
            # Create user with tutor role
            user = User(
                email=email,
                password=password,
                role='tutor'
            )
            db.session.add(user)
            db.session.flush()  # Get user ID without committing

            # Create tutor profile
            tutor = Tutor(
                user_id=user.id,
                first_name=first_name,
                last_name=last_name,
                phone=phone,

                # Address information
                street_address=street_address,
                city=city,
                province=province,
                zip_code=zip_code,
                country=country,

                # Personal information
                birthdate=birthdate,

                # Professional information
                bio=bio,
                qualifications=qualifications
            )

            # Set encrypted banking information if provided
            if bank_transit_number:
                tutor.set_bank_transit_number(bank_transit_number)
            if bank_institution_number:
                tutor.set_bank_institution_number(bank_institution_number)
            if bank_account_number:
                tutor.set_bank_account_number(bank_account_number)

            db.session.add(tutor)
            db.session.commit()

            return True, tutor, "Tutor account created successfully"

        except Exception as e:
            db.session.rollback()
            return False, None, f"Error creating tutor account: {str(e)}"

    # Note: create_student method removed as part of client model migration
    # Students are now represented as individual clients with relationships

    @staticmethod
    def update_user_password(user_id, new_password):
        """
        Update a user's password.

        Args:
            user_id: ID of the user
            new_password: New password to set

        Returns:
            Tuple of (success, message)
        """
        user = User.query.get(user_id)
        if not user:
            return False, "User not found"

        try:
            user.set_password(new_password)
            db.session.commit()
            return True, "Password updated successfully"
        except Exception as e:
            db.session.rollback()
            return False, f"Error updating password: {str(e)}"

    @staticmethod
    def update_user_status(user_id, is_active):
        """
        Update a user's active status.

        Args:
            user_id: ID of the user
            is_active: Whether the user should be active

        Returns:
            Tuple of (success, message)
        """
        user = User.query.get(user_id)
        if not user:
            return False, "User not found"

        try:
            user.is_active = is_active
            db.session.commit()

            status = "activated" if is_active else "deactivated"
            return True, f"User {status} successfully"
        except Exception as e:
            db.session.rollback()
            return False, f"Error updating user status: {str(e)}"

    @staticmethod
    def get_user_profile(user_id):
        """
        Get the profile associated with a user based on their role.

        Args:
            user_id: ID of the user

        Returns:
            Tuple of (profile, role)
        """
        user = User.query.get(user_id)
        if not user:
            return None, None

        if user.role == 'manager':
            return user.manager, 'manager'
        elif user.role == 'tutor':
            return user.tutor, 'tutor'
        elif user.role == 'client':
            # Find the client record associated with this user
            client = Client.query.filter_by(user_id=user.id).first()
            return client, 'client'
        # Legacy parent role has been migrated to client role
        else:
            return None, None