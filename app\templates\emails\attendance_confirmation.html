<!-- app/templates/emails/attendance_confirmation.html -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Confirmation Required</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #4a6fdc;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .appointment-details {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 5px;
            text-decoration: none;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        .button-attended {
            background-color: #28a745;
        }
        .button-cancelled-notice {
            background-color: #ffc107;
            color: #212529;
        }
        .button-cancelled-no-notice {
            background-color: #dc3545;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .footer {
            margin-top: 30px;
            font-size: 12px;
            color: #777;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Attendance Confirmation Required</h1>
    </div>
    <div class="content">
        <p>Hello {{ tutor.first_name }},</p>

        <p>Please confirm the attendance status for your recent tutoring session:</p>

        <div class="appointment-details">
            <p><strong>Client:</strong> {{ appointment.client.first_name }} {{ appointment.client.last_name }}</p>
            <p><strong>Date:</strong> {{ appointment.start_time.strftime('%A, %B %d, %Y') }}</p>
            <p><strong>Time:</strong> {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}</p>
            <p><strong>Service:</strong> {{ appointment.tutor_service.service.name }}</p>
        </div>

        <p>Please confirm the status of this session by clicking one of the buttons below:</p>

        <div class="button-container">
            <a href="{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='attended', _external=True) }}" class="button button-attended">Session Attended</a>
            <a href="{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='cancelled_with_notice', _external=True) }}" class="button button-cancelled-notice">Cancelled (With Notice)</a>
            <a href="{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='cancelled_without_notice', _external=True) }}" class="button button-cancelled-no-notice">Cancelled (No Notice)</a>
        </div>

        <p>Alternatively, you can log in to your account and update the session status there.</p>

        <p>Thank you for your prompt attention to this matter.</p>

        <p>Best regards,<br>
        Tutoring Appointment System</p>
    </div>
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
    </div>
</body>
</html>
