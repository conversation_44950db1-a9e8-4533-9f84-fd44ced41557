# app/views/api.py
from flask import Blueprint, request, jsonify, session, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.models.client import Client
from app.models.tutor import Tutor
from app.models.service import Service, TutorService
from app.models.appointment import Appointment
from app.services.appointment_service import AppointmentService
from datetime import datetime, timedelta
import stripe
import hmac
import hashlib

api = Blueprint('api', __name__, url_prefix='/api')

# Exempt all API routes from CSRF protection
@api.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE')
    return response

@api.route('/tutor-services/<int:tutor_id>')
@login_required
def get_tutor_services(tutor_id):
    """Get services offered by a specific tutor."""
    # Ensure user is authorized (manager or the tutor themselves)
    if current_user.role not in ['manager', 'tutor'] or \
       (current_user.role == 'tutor' and current_user.tutor.id != tutor_id):
        return jsonify({'error': 'Unauthorized'}), 403

    tutor_services = TutorService.query.filter_by(tutor_id=tutor_id, is_active=True).all()

    result = []
    for ts in tutor_services:
        service = Service.query.get(ts.service_id)
        result.append({
            'id': ts.id,
            'service_id': service.id,
            'name': service.name,
            'description': service.description,
            'client_rate': float(ts.client_rate),
            'duration_minutes': service.duration_minutes,
            'transport_fee': float(ts.transport_fee) if ts.transport_fee else 0,
            'transport_fee_description': ts.transport_fee_description
        })

    return jsonify(result)

# Parent-student model has been migrated to client-dependant model

@api.route('/check-availability', methods=['POST'])
@login_required
def check_availability():
    """Check if a tutor is available for a specific time slot."""
    # Only managers can check availability
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    data = request.json
    tutor_id = data.get('tutor_id')

    # Parse date and time
    try:
        start_date = datetime.strptime(data.get('date'), '%Y-%m-%d').date()
        start_time = datetime.strptime(data.get('start_time'), '%H:%M').time()
        end_time = datetime.strptime(data.get('end_time'), '%H:%M').time()

        start_datetime = datetime.combine(start_date, start_time)
        end_datetime = datetime.combine(start_date, end_time)

        # If we have an appointment ID, we're checking for an update
        appointment_id = data.get('appointment_id')

        is_available = AppointmentService.check_availability(
            tutor_id, start_datetime, end_datetime, appointment_id
        )

        return jsonify({'available': is_available})

    except (ValueError, TypeError) as e:
        return jsonify({'error': str(e)}), 400

@api.route('/calendar-data')
@login_required
def get_calendar_data():
    """Get calendar data for appointments."""
    view_type = request.args.get('view', 'week')
    start_date_str = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    tutor_id = request.args.get('tutor_id', type=int)

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    except ValueError:
        start_date = datetime.now()

    # Get end date based on view type
    if view_type == 'day':
        end_date = start_date + timedelta(days=1)
    elif view_type == 'week':
        # Start from Monday of the current week
        monday = start_date - timedelta(days=start_date.weekday())
        start_date = monday
        end_date = start_date + timedelta(days=7)
    elif view_type == 'month':
        # Start from the 1st of the month
        start_date = start_date.replace(day=1)
        # Go to the 1st of next month
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year+1, month=1)
        else:
            end_date = start_date.replace(month=start_date.month+1)

    # Base query for appointments with eager loading of relationships
    from sqlalchemy.orm import joinedload
    query = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.start_time >= start_date,
        Appointment.start_time < end_date
    )

    # Filter by tutor if specified
    if tutor_id:
        query = query.filter(Appointment.tutor_id == tutor_id)
    # Filter by client's appointments if client is viewing
    elif current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.id).first()
        if client:
            query = query.filter(Appointment.client_id == client.id)
    # Filter by tutor's appointments if tutor is viewing
    elif current_user.role == 'tutor':
        tutor = Tutor.query.filter_by(user_id=current_user.id).first()
        if tutor:
            query = query.filter(Appointment.tutor_id == tutor.id)

    appointments = query.all()

    # Get tutors for display (only for manager view)
    tutors = []
    if current_user.role == 'manager':
        tutors = Tutor.query.filter_by(is_active=True).all()
        tutors = [{'id': t.id, 'name': f"{t.first_name} {t.last_name}"} for t in tutors]

    # Prepare calendar data
    calendar_data = AppointmentService.prepare_calendar_data(appointments, tutors, start_date, end_date, view_type)

    return jsonify(calendar_data)

@api.route('/appointment/<int:id>', methods=['GET'])
@login_required
def get_appointment(id):
    """Get detailed information for a specific appointment."""
    from sqlalchemy.orm import joinedload
    appointment = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).get_or_404(id)

    # Check authorization
    if current_user.role == 'manager':
        pass  # Managers can access all appointments
    elif current_user.role == 'tutor':
        tutor = Tutor.query.filter_by(user_id=current_user.id).first()
        if not tutor or appointment.tutor_id != tutor.id:
            return jsonify({'error': 'Unauthorized'}), 403
    elif current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.id).first()
        if not client:
            return jsonify({'error': 'Unauthorized'}), 403

        if appointment.client_id != client.id:
            return jsonify({'error': 'Unauthorized'}), 403
    else:
        return jsonify({'error': 'Unauthorized'}), 403

    # Get related data from eager-loaded relationships
    tutor = appointment.tutor
    client = appointment.client
    tutor_service = appointment.tutor_service
    service = tutor_service.service if tutor_service else None
    dependant = appointment.dependant

    # Prepare response data
    result = {
        'id': appointment.id,
        'tutor': {
            'id': tutor.id,
            'name': f"{tutor.first_name} {tutor.last_name}"
        } if tutor else None,
        'client': {
            'id': client.id,
            'name': f"{client.first_name} {client.last_name}"
        } if client else None,
        'dependant': {
            'id': dependant.id,
            'name': f"{dependant.first_name} {dependant.last_name}"
        } if dependant else None,
        'student': {
            'id': dependant.id if dependant else client.id,
            'name': f"{dependant.first_name} {dependant.last_name}" if dependant else f"{client.first_name} {client.last_name}"
        } if (dependant or client) else None,
        'service': {
            'id': service.id if service else None,
            'name': service.name if service else "Unknown",
            'duration_minutes': service.duration_minutes if service else 0,
            'rate': float(tutor_service.client_rate) if tutor_service else 0
        },
        'start_time': appointment.start_time.strftime('%Y-%m-%d %H:%M'),
        'end_time': appointment.end_time.strftime('%Y-%m-%d %H:%M'),
        'status': appointment.status,
        'notes': appointment.notes,
        'is_for_dependant': appointment.dependant_id is not None
    }

    return jsonify(result)

# Legacy generate invoice endpoint removed - use manager interface instead



# Continuation of app/views/api.py

# Legacy invoice endpoint removed - use manager interface instead

# Subscription available check endpoint
@api.route('/subscription/<int:id>/check-availability', methods=['POST'])
@login_required
def check_subscription_availability(id):
    """Check if a subscription has enough hours for an appointment."""
    from app.services.subscription_service import SubscriptionService

    data = request.json
    if not data or 'duration_minutes' not in data:
        return jsonify({'error': 'Duration minutes required'}), 400

    duration_minutes = data['duration_minutes']
    available = SubscriptionService.check_subscription_available(id, duration_minutes)

    return jsonify({
        'subscription_id': id,
        'duration_minutes': duration_minutes,
        'available': available
    })

@api.route('/manager/pending-time-off-count')
@login_required
def get_pending_time_off_count():
    """Get count of pending time-off requests for badge display."""
    # Only managers can access this
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.models.time_off import TimeOff
    count = TimeOff.query.filter_by(status='pending').count()

    return jsonify({'count': count})

# Legacy student subscriptions endpoint removed - use client-based subscriptions instead

# Legacy parent consent endpoints removed - use client-based consent system instead

@api.route('/clients/search')
@login_required
def search_clients():
    """Search for clients by name or email for dynamic selection."""
    # Only managers can search clients
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    query = request.args.get('q', '').strip()
    exclude_id = request.args.get('exclude_id', type=int)

    if not query or len(query) < 2:
        return jsonify([])

    # Search clients by name or email
    search_term = f"%{query}%"
    clients_query = db.session.query(Client).join(Client.user).filter(
        db.or_(
            Client.first_name.ilike(search_term),
            Client.last_name.ilike(search_term),
            db.func.concat(Client.first_name, ' ', Client.last_name).ilike(search_term),
            Client.user.has(email=search_term)
        )
    )

    # Exclude specific client if provided (useful when editing)
    if exclude_id:
        clients_query = clients_query.filter(Client.id != exclude_id)

    clients = clients_query.limit(10).all()

    results = []
    for client in clients:
        results.append({
            'id': client.id,
            'name': f"{client.first_name} {client.last_name}",
            'email': client.user.email,
            'client_type': client.client_type,
            'phone': client.phone
        })

    return jsonify(results)

@api.route('/dependants/search')
@login_required
def search_dependants():
    """Search for dependants by name, email, or phone."""
    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify([])

    from app.models.dependant import Dependant
    from sqlalchemy import or_

    # Search dependants
    dependants = Dependant.query.filter(
        or_(
            Dependant.first_name.ilike(f'%{query}%'),
            Dependant.last_name.ilike(f'%{query}%'),
            Dependant.email.ilike(f'%{query}%'),
            Dependant.phone.ilike(f'%{query}%')
        )
    ).limit(10).all()

    results = []
    for dependant in dependants:
        results.append({
            'id': dependant.id,
            'name': f"{dependant.first_name} {dependant.last_name}",
            'email': dependant.email or 'No email',
            'phone': dependant.phone or 'No phone',
            'type': 'dependant'
        })

    return jsonify(results)


@api.route('/clients-and-dependants/search')
@login_required
def search_clients_and_dependants():
    """Search for both clients and dependants for appointment scheduling."""
    # Only managers can search for appointment scheduling
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify([])

    from app.models.dependant import Dependant
    from sqlalchemy import or_

    results = []

    # Search clients (individual clients only)
    search_term = f"%{query}%"
    clients = Client.query.filter(
        Client.client_type == 'individual',
        or_(
            Client.first_name.ilike(search_term),
            Client.last_name.ilike(search_term),
            db.func.concat(Client.first_name, ' ', Client.last_name).ilike(search_term),
            Client.email.ilike(search_term)
        )
    ).limit(10).all()

    for client in clients:
        results.append({
            'id': client.id,
            'name': f"{client.first_name} {client.last_name}",
            'email': client.email or 'No email',
            'phone': client.phone or 'No phone',
            'type': 'client'
        })

    # Search dependants
    dependants = Dependant.query.filter(
        or_(
            Dependant.first_name.ilike(search_term),
            Dependant.last_name.ilike(search_term),
            Dependant.email.ilike(search_term),
            Dependant.phone.ilike(search_term)
        )
    ).limit(10).all()

    for dependant in dependants:
        results.append({
            'id': dependant.id,
            'name': f"{dependant.first_name} {dependant.last_name}",
            'email': dependant.email or 'No email',
            'phone': dependant.phone or 'No phone',
            'type': 'dependant'
        })

    # Sort results by name
    results.sort(key=lambda x: x['name'])

    return jsonify(results)


@api.route('/clients/<int:client_id>')
@login_required
def get_client(client_id):
    """Get individual client information."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    client = Client.query.get_or_404(client_id)
    return jsonify({
        'id': client.id,
        'first_name': client.first_name,
        'last_name': client.last_name,
        'email': client.email,
        'phone': client.phone,
        'client_type': client.client_type
    })


@api.route('/dependants/<int:dependant_id>')
@login_required
def get_dependant(dependant_id):
    """Get individual dependant information."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.models.dependant import Dependant
    dependant = Dependant.query.get_or_404(dependant_id)
    return jsonify({
        'id': dependant.id,
        'first_name': dependant.first_name,
        'last_name': dependant.last_name,
        'email': dependant.email,
        'phone': dependant.phone
    })


@api.route('/client/<int:client_id>/subscriptions')
@login_required
def get_client_subscriptions(client_id):
    """Get active subscriptions for a specific client."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.services.subscription_service import SubscriptionService
    from app.models.subscription import Subscription

    # Get active subscriptions for this client
    subscriptions = SubscriptionService.get_active_subscriptions_for_client(client_id)

    # Format the response
    subscription_data = []
    for subscription in subscriptions:
        subscription_data.append({
            'id': subscription.id,
            'plan_name': subscription.plan.name,
            'hours_remaining': float(subscription.hours_remaining),
            'hours_used': float(subscription.hours_used),
            'max_hours': subscription.plan.max_hours,
            'start_date': subscription.start_date.isoformat(),
            'end_date': subscription.end_date.isoformat(),
            'status': subscription.status,
            'owner_name': f"{subscription.client.first_name} {subscription.client.last_name}"
        })

    return jsonify(subscription_data)


@api.route('/dependant/<int:dependant_id>/subscriptions')
@login_required
def get_dependant_subscriptions(dependant_id):
    """Get active subscriptions available to a dependant through their client relationships."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.services.subscription_service import SubscriptionService

    # Get active subscriptions available to this dependant
    subscriptions = SubscriptionService.get_active_subscriptions_for_dependant(dependant_id)

    # Format the response
    subscription_data = []
    for subscription in subscriptions:
        subscription_data.append({
            'id': subscription.id,
            'plan_name': subscription.plan.name,
            'hours_remaining': float(subscription.hours_remaining),
            'hours_used': float(subscription.hours_used),
            'max_hours': subscription.plan.max_hours,
            'start_date': subscription.start_date.isoformat(),
            'end_date': subscription.end_date.isoformat(),
            'status': subscription.status,
            'owner_name': f"{subscription.client.first_name} {subscription.client.last_name}"
        })

    return jsonify(subscription_data)


@api.route('/dependants', methods=['POST'])
@login_required
def create_dependant_api():
    """Create a new dependant via API (for modal creation)."""
    print("=== DEPENDANT CREATION API CALLED ===")
    print(f"Request method: {request.method}")
    print(f"Request URL: {request.url}")
    print(f"Request headers: {dict(request.headers)}")
    print(f"Request content type: {request.content_type}")
    print(f"Raw request data: {request.data}")

    # Only managers can create dependants
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    # Check CSRF token
    from flask_wtf.csrf import validate_csrf
    try:
        validate_csrf(request.headers.get('X-CSRFToken'))
    except Exception as e:
        print(f"CSRF validation failed: {e}")
        return jsonify({'error': 'CSRF token missing or invalid'}), 400

    data = request.json

    # Debug: Log received data
    print(f"Parsed JSON data: {data}")
    print(f"Data type: {type(data)}")
    if data:
        print("Individual field analysis:")
        for key, value in data.items():
            print(f"  {key}: '{value}' (type: {type(value)}, len: {len(str(value)) if value is not None else 'N/A'})")
    else:
        print("NO DATA RECEIVED!")
    print("=== END DEBUG INFO ===")

    # Check if data is None or empty
    if not data:
        return jsonify({'error': 'No data received'}), 400

    # Check if all critical fields are None (indicates form collection failure)
    if (data.get('first_name') is None and
        data.get('last_name') is None and
        data.get('client_type') is None):
        return jsonify({'error': 'Form data collection failed - all required fields are None. Please check the form and try again.'}), 400

    # Validate required fields (first_name, last_name, and client_type are required)
    # Note: client_type is inherited from parent and should always be provided by the frontend
    required_fields = ['first_name', 'last_name', 'client_type']
    for field in required_fields:
        if field not in data:
            print(f"Field {field} not in data")
            return jsonify({'error': f'Missing field: {field}'}), 400

        field_value = data[field]
        print(f"Validating field {field}: value='{field_value}', type={type(field_value)}")

        if field_value is None:
            print(f"Field {field} is None")
            return jsonify({'error': f'Field {field} cannot be None'}), 400

        if str(field_value).strip() == '':
            print(f"Field {field} is empty after stripping")
            # More specific error message for first_name
            if field == 'first_name':
                return jsonify({'error': 'first name is required and cannot be empty'}), 400
            else:
                return jsonify({'error': f'Empty value for required field: {field}'}), 400

    # Extract and clean the validated data
    first_name = str(data.get('first_name')).strip()
    last_name = str(data.get('last_name')).strip()
    client_type = str(data.get('client_type')).strip()

    print(f"Final processed values:")
    print(f"  first_name: '{first_name}'")
    print(f"  last_name: '{last_name}'")
    print(f"  client_type: '{client_type}'")

    # If email is provided, validate it and check for duplicates
    email = data.get('email', '').strip()
    password = data.get('password', '').strip()

    # If email is provided, password should also be provided (and vice versa)
    if email and not password:
        return jsonify({'error': 'If email is provided, password is also required'}), 400
    if password and not email:
        return jsonify({'error': 'If password is provided, email is also required'}), 400

    # Check if email already exists (only if email is provided)
    user = None
    if email:
        from app.models.user import User
        if User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already registered'}), 400

    try:
        # Import the Dependant model
        from app.models.dependant import Dependant

        # Create user only if email is provided
        if email:
            from app.models.user import User
            user = User(
                email=email,
                password=password,
                role='client'
            )
            db.session.add(user)
            db.session.flush()  # Get the user ID without committing
            user_id = user.id
        else:
            user_id = None

        # Create dependant record (not client record)
        phone = data.get('phone', '').strip()
        address = data.get('address', '').strip()

        # Create dependant with explicit assignment
        dependant = Dependant()
        dependant.user_id = user_id
        dependant.first_name = first_name
        dependant.last_name = last_name
        dependant.email = email if email else None
        dependant.phone = phone if phone else None
        dependant.address = address if address else None
        dependant.is_active = True
        dependant.preferred_language = 'en'

        # Add type-specific fields for individual dependants
        if client_type == 'individual':
            if data.get('date_of_birth', '').strip():
                from datetime import datetime
                dependant.date_of_birth = datetime.strptime(data['date_of_birth'], '%Y-%m-%d').date()
            dependant.school_grade = data.get('school_grade', '').strip() if data.get('school_grade', '').strip() else None

        # Add notes
        dependant.notes = data.get('notes', '').strip() if data.get('notes', '').strip() else None

        db.session.add(dependant)
        db.session.commit()

        return jsonify({
            'success': True,
            'dependant': {
                'id': dependant.id,
                'name': f"{dependant.first_name} {dependant.last_name}",
                'email': email if email else 'No email provided',
                'type': 'dependant',  # Always return 'dependant' as type
                'phone': dependant.phone if dependant.phone else 'No phone provided'
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"Exception occurred during client creation: {str(e)}")
        print(f"Exception type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

        # Debug: Print the data that was being used when the exception occurred
        print(f"Data being processed when exception occurred:")
        print(f"  first_name: '{first_name}' (length: {len(first_name)})")
        print(f"  last_name: '{last_name}' (length: {len(last_name)})")
        print(f"  client_type: '{client_type}' (length: {len(client_type)})")
        print(f"  email: '{email}' (length: {len(email)})")
        print(f"  phone: '{phone}' (length: {len(phone)})")

        # Provide more specific error messages
        error_message = str(e)
        if 'null value' in error_message.lower():
            if 'first_name' in error_message:
                return jsonify({'error': f'Database constraint violation for first_name. Value was: "{first_name}". Error: {error_message}'}), 400
            elif 'last_name' in error_message:
                return jsonify({'error': f'Database constraint violation for last_name. Value was: "{last_name}". Error: {error_message}'}), 400
            elif 'client_type' in error_message:
                return jsonify({'error': f'Database constraint violation for client_type. Value was: "{client_type}". Error: {error_message}'}), 400
            else:
                return jsonify({'error': f'Database constraint violation: {error_message}'}), 400
        else:
            return jsonify({'error': f'Failed to create client: {error_message}'}), 500

@api.route('/set-language', methods=['POST'])
@login_required
def set_language():
    """Set the user's preferred language."""
    data = request.json

    # Validate required fields
    if 'language' not in data:
        return jsonify({'success': False, 'message': 'Missing required field: language'}), 400

    language = data['language']

    # Validate language code
    if language not in ['en', 'fr']:
        return jsonify({'success': False, 'message': 'Invalid language code. Supported: en, fr'}), 400

    # Store in session
    session['language'] = language

    # If user is a client, store preference in database
    if current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.id).first()
        if client:
            # Update preferred_language if the column exists
            try:
                client.preferred_language = language
                db.session.commit()
            except:
                # Column might not exist yet, just use session
                pass

    return jsonify({'success': True})

@api.route('/stripe/webhook', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhook events with proper verification and race condition protection."""
    payload = request.get_data()
    sig_header = request.headers.get('Stripe-Signature')

    # Verify webhook signature
    webhook_secret = current_app.config.get('STRIPE_WEBHOOK_SECRET')
    if not webhook_secret:
        current_app.logger.error('Stripe webhook secret not configured')
        return jsonify({'error': 'Webhook secret not configured'}), 500

    try:
        # Verify the webhook signature
        stripe.api_key = current_app.config['STRIPE_SECRET_KEY']
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
    except ValueError as e:
        # Invalid payload
        current_app.logger.error(f'Invalid payload in Stripe webhook: {str(e)}')
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        current_app.logger.error(f'Invalid signature in Stripe webhook: {str(e)}')
        return jsonify({'error': 'Invalid signature'}), 400

    # Process the event using our race-condition-safe service
    try:
        from app.services.stripe_service import StripeService
        success = StripeService.handle_webhook_event(event)

        if success:
            current_app.logger.info(f'Successfully processed Stripe webhook event: {event["type"]}')
            return jsonify({'status': 'success'}), 200
        else:
            current_app.logger.warning(f'Unhandled Stripe webhook event type: {event["type"]}')
            return jsonify({'status': 'ignored'}), 200

    except Exception as e:
        current_app.logger.error(f'Error processing Stripe webhook: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500