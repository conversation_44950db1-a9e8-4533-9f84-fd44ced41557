<!-- app/templates/manager/tutor_earnings_report.html -->
{% extends "base.html" %}

{% block title %}Tu<PERSON> Earnings Report - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Tutor Earnings Report</h2>
    </div>
    <div class="col-md-4 text-end">
        <button type="button" class="btn btn-primary" id="print-report-btn">
            <i class="fas fa-print"></i> Print Report
        </button>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.tutor_earnings_report') }}" class="row g-3 filter-form">
            <div class="col-md-4">
                <label for="tutor_id" class="form-label">Tutor</label>
                <select name="tutor_id" id="tutor_id" class="form-select" required>
                    <option value="">Select a Tutor</option>
                    {% for tutor in tutors %}
                        <option value="{{ tutor.id }}" {% if current_tutor_id == tutor.id %}selected{% endif %}>
                            {{ tutor.first_name }} {{ tutor.last_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" name="start_date" id="start_date" class="form-control" value="{{ start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" name="end_date" id="end_date" class="form-control" value="{{ end_date }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">Generate Report</button>
            </div>
        </form>
    </div>
</div>

{% if earnings %}
<!-- Earnings Report -->
<div class="card shadow mb-4" id="report-content">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            {% if current_tutor_id %}
                {% set tutor = tutors|selectattr('id', 'equalto', current_tutor_id)|first %}
                Earnings Report for {{ tutor.first_name }} {{ tutor.last_name }}
            {% else %}
                Tutor Earnings Report
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Report Period</h6>
                <p>
                    {% if start_date and end_date %}
                        {{ start_date }} to {{ end_date }}
                    {% elif start_date %}
                        From {{ start_date }} onwards
                    {% elif end_date %}
                        Until {{ end_date }}
                    {% else %}
                        All time
                    {% endif %}
                </p>
            </div>
            <div class="col-md-6 text-end">
                <h6>Report Generated</h6>
                <p>{{ now.strftime('%Y-%m-%d %H:%M') }}</p>
            </div>
        </div>

        <hr class="my-4">

        <!-- Earnings Summary -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">Service Earnings</h5>
                        <p class="display-5">${{ "%.2f"|format(earnings.service_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h5 class="card-title">Transport Fees</h5>
                        <p class="display-5">${{ "%.2f"|format(earnings.transport_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total Earnings</h5>
                        <p class="display-5">${{ "%.2f"|format(earnings.grand_total) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Statistics -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Payment Statistics</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Total Appointments Paid:</span>
                            <span><strong>{{ earnings.payment_count }}</strong></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Average Earnings Per Appointment:</span>
                            <span><strong>${{ "%.2f"|format(earnings.grand_total / earnings.payment_count if earnings.payment_count > 0 else 0) }}</strong></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Service Income Percentage:</span>
                            <span><strong>{{ "%.1f"|format((earnings.service_total / earnings.grand_total * 100) if earnings.grand_total > 0 else 0) }}%</strong></span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Transport Fee Percentage:</span>
                            <span><strong>{{ "%.1f"|format((earnings.transport_total / earnings.grand_total * 100) if earnings.grand_total > 0 else 0) }}%</strong></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Income Breakdown</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="earningsChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment History -->
        <div class="row">
            <div class="col-12">
                <h5 class="mb-3">Payment History</h5>
                {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Appointment</th>
                                    <th>Client</th>
                                    <th>Service Amount</th>
                                    <th>Transport Fee</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                    <tr>
                                        <td>{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ payment.appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>{{ payment.appointment.client.first_name }} {{ payment.appointment.client.last_name }}</td>
                                        <td>${{ "%.2f"|format(payment.service_amount) }}</td>
                                        <td>${{ "%.2f"|format(payment.transport_amount) }}</td>
                                        <td><strong>${{ "%.2f"|format(payment.total_amount) }}</strong></td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-primary">
                                    <td colspan="3"><strong>Total</strong></td>
                                    <td><strong>${{ "%.2f"|format(earnings.service_total) }}</strong></td>
                                    <td><strong>${{ "%.2f"|format(earnings.transport_total) }}</strong></td>
                                    <td><strong>${{ "%.2f"|format(earnings.grand_total) }}</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">No payment history available for the selected period.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
    <div class="card shadow">
        <div class="card-body text-center p-5">
            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
            <h5>No Earnings Report Available</h5>
            <p class="text-muted">Please select a tutor and date range to generate an earnings report.</p>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if earnings %}
        // Create earnings chart
        const ctx = document.getElementById('earningsChart').getContext('2d');
        const earningsChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Service Income', 'Transport Fees'],
                datasets: [{
                    data: [{{ earnings.service_total }}, {{ earnings.transport_total }}],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.formattedValue;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((context.raw / total) * 100);
                                return `${label}: $${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        {% endif %}

        // Handle print button
        const printButton = document.getElementById('print-report-btn');
        if (printButton) {
            printButton.addEventListener('click', function() {
                window.print();
            });
        }
    });
</script>
{% endblock %}