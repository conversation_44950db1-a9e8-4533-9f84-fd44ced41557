# app/forms/manager_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, SelectField, TextAreaField, DecimalField, IntegerField, DateField, TimeField, DateTimeField, RadioField, HiddenField
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError, Optional, NumberRange
from app.models.user import User
from app.models.client import Client, IndividualClient, InstitutionalClient
from app.models.tutor import Tutor
from app.models.service import Service, TutorService
from datetime import datetime, time, timedelta

def get_default_appointment_time():
    """Calculate the next available 30-minute increment for appointments."""
    now = datetime.now()
    current_hour = now.hour
    current_minute = now.minute

    # Calculate next 30-minute increment
    next_hour = current_hour
    next_minute = 30 if current_minute <= 30 else 0

    if current_minute > 30:
        next_hour += 1

    # Ensure we're within business hours (8 AM - 9 PM)
    if next_hour < 8:
        next_hour = 8
        next_minute = 0
    elif next_hour >= 21:
        # If it's after 9 PM, set to 8 AM (will be next day)
        next_hour = 8
        next_minute = 0

    return f"{next_hour:02d}:{next_minute:02d}"

def get_time_choices():
    """Generate time choices for appointment scheduling (8 AM - 9 PM in 30-minute increments)."""
    choices = []
    for hour in range(8, 21):  # 8 AM to 8 PM (9 PM is end time, not start time)
        for minute in [0, 30]:
            time_value = f"{hour:02d}:{minute:02d}"
            choices.append((time_value, time_value))
    return choices

class FilterForm(FlaskForm):
    search = StringField('Search', validators=[Optional()])
    submit = SubmitField('Filter')


class ClientForm(FlaskForm):
    # User account fields
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long.')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        Optional(),
        EqualTo('password', message='Passwords must match.')
    ])

    # Basic contact information
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    phone = StringField('Phone', validators=[DataRequired()])

    # Structured address fields
    civic_number = StringField('Civic Number', validators=[Optional()])
    street = StringField('Street', validators=[Optional()])
    city = StringField('City', validators=[Optional()])
    postal_code = StringField('Postal Code', validators=[Optional()])
    province = SelectField('Province', choices=[
        ('Quebec', 'Quebec'),
        ('Ontario', 'Ontario'),
        ('British Columbia', 'British Columbia'),
        ('Alberta', 'Alberta'),
        ('Manitoba', 'Manitoba'),
        ('Saskatchewan', 'Saskatchewan'),
        ('Nova Scotia', 'Nova Scotia'),
        ('New Brunswick', 'New Brunswick'),
        ('Newfoundland and Labrador', 'Newfoundland and Labrador'),
        ('Prince Edward Island', 'Prince Edward Island'),
        ('Northwest Territories', 'Northwest Territories'),
        ('Nunavut', 'Nunavut'),
        ('Yukon', 'Yukon')
    ], default='Quebec', validators=[Optional()])
    country = SelectField('Country', choices=[
        ('Canada', 'Canada'),
        ('United States', 'United States'),
        ('Other', 'Other')
    ], default='Canada', validators=[Optional()])

    # Client type selection (radio field to match template)
    client_type = RadioField('Client Type', choices=[
        ('individual', 'Individual'),
        ('institutional', 'Institutional')
    ], validators=[DataRequired()])

    # Individual client fields
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    school_grade = StringField('School Grade', validators=[Optional()])

    # Institutional client fields
    organization_name = StringField('Organization Name', validators=[Optional()])
    contact_person = StringField('Contact Person', validators=[Optional()])

    # General notes
    notes = TextAreaField('Notes', validators=[Optional()])

    # Dependant relationships (for dynamic handling)
    dependant_ids = HiddenField('Dependant IDs')  # JSON string of selected dependant IDs
    dependant_relationships = HiddenField('Dependant Relationships')  # JSON string of relationship types

    submit = SubmitField('Save')

class IndividualClientDetailsForm(FlaskForm):
    client_id = SelectField('Client', validators=[Optional()], coerce=int)
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    school_grade = StringField('School Grade', validators=[Optional()])
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Save')

class TutorForm(FlaskForm):
    # Account Information
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long.')
    ])
    confirm_password = PasswordField('Confirm Password', validators=[
        Optional(),
        EqualTo('password', message='Passwords must match.')
    ])

    # Basic Information
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    phone = StringField('Phone', validators=[DataRequired()])

    # Address Information
    street_address = StringField('Street Address', validators=[Optional()])
    city = StringField('City', validators=[Optional()])
    province = StringField('Province/State', validators=[Optional()])
    zip_code = StringField('Zip/Postal Code', validators=[Optional()])
    country = StringField('Country', validators=[Optional()])

    # Personal Information
    birthdate = DateField('Birthdate', format='%Y-%m-%d', validators=[Optional()])

    # Banking Information
    bank_transit_number = StringField('Bank Transit Number', validators=[Optional()])
    bank_institution_number = StringField('Bank Institution Number', validators=[Optional()])
    bank_account_number = StringField('Bank Account Number', validators=[Optional()])

    # Professional Information
    bio = TextAreaField('Bio', validators=[Optional()])
    qualifications = TextAreaField('Qualifications', validators=[Optional()])

    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save')

class ServiceForm(FlaskForm):
    name = StringField('Service Name', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[Optional()])
    default_price = DecimalField('Default Price ($)', validators=[DataRequired(), NumberRange(min=0)])
    duration_minutes = IntegerField('Duration (minutes)', validators=[DataRequired(), NumberRange(min=15, max=240)])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save')

class TutorServiceForm(FlaskForm):
    tutor_id = SelectField('Tutor', validators=[DataRequired()], coerce=int)
    service_id = SelectField('Service', validators=[DataRequired()], coerce=int)
    tutor_rate = DecimalField('Tutor Rate ($)', validators=[DataRequired(), NumberRange(min=0)])
    client_rate = DecimalField('Client Rate ($)', validators=[DataRequired(), NumberRange(min=0)])
    transport_fee = DecimalField('Transport Fee ($)', validators=[Optional(), NumberRange(min=0)], default=0.00)
    transport_fee_description = TextAreaField('Transport Fee Description', validators=[Optional()])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save')

class SubscriptionPlanForm(FlaskForm):
    name = StringField('Plan Name', validators=[DataRequired()])
    description = TextAreaField('Description', validators=[Optional()])
    price = DecimalField('Price ($)', validators=[DataRequired(), NumberRange(min=0)])
    duration_months = IntegerField('Duration (months)', validators=[DataRequired(), NumberRange(min=1)])
    max_hours = IntegerField('Maximum Hours', validators=[DataRequired(), NumberRange(min=1)])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save')

class SubscriptionForm(FlaskForm):
    client_id = SelectField('Client', validators=[DataRequired()], coerce=int)
    plan_id = SelectField('Subscription Plan', validators=[DataRequired()], coerce=int)
    start_date = DateField('Start Date', validators=[DataRequired()], default=datetime.now().date)
    submit = SubmitField('Create Subscription')

class AppointmentForm(FlaskForm):
    # Basic appointment fields
    tutor_id = SelectField('Tutor', validators=[DataRequired()], coerce=int)
    client_id = HiddenField('Client', validators=[DataRequired()])
    client_type = HiddenField('Client Type')  # 'client' or 'dependant'
    dependant_id = HiddenField('Dependant ID', validators=[Optional()])
    tutor_service_id = SelectField('Service', validators=[DataRequired()], coerce=int)
    subscription_id = SelectField('Subscription', validators=[Optional()], coerce=int)
    is_subscription_based = BooleanField('Use Subscription', default=False)

    # Recurring appointment toggle
    is_recurring = BooleanField('Make this recurring', default=False)

    # Regular appointment fields (shown when not recurring)
    start_date = DateField('Date', validators=[Optional()], default=datetime.now().date)
    start_time = SelectField('Start Time', choices=get_time_choices, validators=[Optional()], default=get_default_appointment_time)
    end_time = TimeField('End Time', validators=[Optional()])
    status = SelectField('Status', choices=[
        ('scheduled', 'Scheduled'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no-show', 'No Show'),
        ('awaiting_confirmation', 'Awaiting Confirmation')
    ], validators=[Optional()], default='scheduled')

    # Recurring appointment fields (shown when recurring is checked)
    # Note: recurring appointments now use the same start_time field as regular appointments

    frequency = SelectField('Frequency', choices=[
        ('weekly', 'Weekly'),
        ('biweekly', 'Bi-weekly (Every 2 weeks)'),
        ('monthly', 'Monthly')
    ], validators=[Optional()])

    day_of_week = SelectField('Day of Week', choices=[
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday')
    ], coerce=int, validators=[Optional()])

    week_of_month = SelectField('Week of Month (for monthly)', choices=[
        (1, 'First'),
        (2, 'Second'),
        (3, 'Third'),
        (4, 'Fourth'),
        (5, 'Last')
    ], coerce=int, validators=[Optional()])

    pattern_start_date = DateField('Start Date', validators=[Optional()], default=datetime.now().date)

    end_type = RadioField('End', choices=[
        ('never', 'Never'),
        ('date', 'On Date'),
        ('occurrences', 'After # of Occurrences')
    ], default='never', validators=[Optional()])

    pattern_end_date = DateField('End Date', validators=[Optional()])
    pattern_occurrences = IntegerField('Number of Occurrences', validators=[Optional(), NumberRange(min=1, max=100)])

    # Common fields
    transport_fee = DecimalField('Transport Fee ($)', validators=[Optional()], default=0.00)
    apply_transport_fee = BooleanField('Apply Transport Fee', default=True)
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Save')

    def validate(self, extra_validators=None):
        # Custom validation for client_id when dealing with dependants and clients
        # Handle both prefixed format (d_123, c_123) and legacy format (123)

        client_id_value = self.client_id.data
        actual_id = None
        entity_type = None

        # Parse the client_id to determine type and extract actual ID
        if isinstance(client_id_value, str):
            if client_id_value.startswith('d_'):
                # Dependant with prefix
                try:
                    actual_id = int(client_id_value[2:])
                    entity_type = 'dependant'
                except (ValueError, TypeError):
                    self.client_id.errors = ['Invalid dependant ID format']
                    return False
            elif client_id_value.startswith('c_'):
                # Client with prefix
                try:
                    actual_id = int(client_id_value[2:])
                    entity_type = 'client'
                except (ValueError, TypeError):
                    self.client_id.errors = ['Invalid client ID format']
                    return False
            else:
                # Legacy format - determine type from client_type field
                try:
                    actual_id = int(client_id_value)
                    entity_type = getattr(self, 'client_type', None) and self.client_type.data or 'client'
                except (ValueError, TypeError):
                    self.client_id.errors = ['Invalid ID format']
                    return False
        elif isinstance(client_id_value, int):
            # Direct integer - legacy format
            actual_id = client_id_value
            entity_type = getattr(self, 'client_type', None) and self.client_type.data or 'client'
        else:
            self.client_id.errors = ['Invalid client/dependant selection']
            return False

        # Validate based on entity type
        if entity_type == 'dependant':
            # Validate dependant exists and has client relationship
            from app.models.dependant import Dependant, DependantRelationship

            dependant = Dependant.query.get(actual_id)
            if not dependant:
                self.client_id.errors = ['Selected dependant does not exist']
                return False

            # Check if dependant has a client relationship
            relationship = DependantRelationship.query.filter_by(dependant_id=actual_id).first()
            if not relationship:
                self.client_id.errors = ['Selected dependant is not associated with any client']
                return False

        elif entity_type == 'client':
            # Validate client exists
            from app.models.client import Client

            client = Client.query.get(actual_id)
            if not client:
                self.client_id.errors = ['Selected client does not exist']
                return False

        if not super(AppointmentForm, self).validate(extra_validators=extra_validators):
            return False

        # Validate based on whether this is a recurring appointment or not
        if self.is_recurring.data:
            # Validate recurring appointment fields - now using same start_time field
            if not self.start_time.data:
                self.start_time.errors = ['Start time is required for recurring appointments.']
                return False
            if not self.frequency.data:
                self.frequency.errors = ['Frequency is required for recurring appointments.']
                return False
            if self.day_of_week.data is None:
                self.day_of_week.errors = ['Day of week is required for recurring appointments.']
                return False
            if not self.pattern_start_date.data:
                self.pattern_start_date.errors = ['Start date is required for recurring appointments.']
                return False

            # Validate end type specific fields
            if self.end_type.data == 'date' and not self.pattern_end_date.data:
                self.pattern_end_date.errors = ['End date is required when "On Date" is selected.']
                return False
            if self.end_type.data == 'occurrences' and not self.pattern_occurrences.data:
                self.pattern_occurrences.errors = ['Number of occurrences is required when "After # of Occurrences" is selected.']
                return False

            # Week of month is only required for monthly frequency
            if self.frequency.data == 'monthly' and not self.week_of_month.data:
                self.week_of_month.errors = ['Week of month is required for monthly recurrence']
                return False

            # Ensure business hours for recurring template
            business_start = time(8, 0)  # 8 AM
            business_end = time(21, 0)   # 9 PM

            # Convert string time to time object for validation
            try:
                start_time_obj = datetime.strptime(self.start_time.data, '%H:%M').time()
            except (ValueError, TypeError):
                self.start_time.errors = ['Invalid time format']
                return False

            if start_time_obj < business_start:
                self.start_time.errors = ['Appointments must start between 8 AM and 9 PM EST']
                return False

            # Calculate end time based on service duration for recurring
            try:
                tutor_service = TutorService.query.get(self.tutor_service_id.data)
                service = Service.query.get(tutor_service.service_id)
                duration_minutes = service.duration_minutes

                start_datetime = datetime.combine(datetime.today(), start_time_obj)
                end_datetime = start_datetime + timedelta(minutes=duration_minutes)
                end_time = end_datetime.time()

                if end_time > business_end:
                    self.start_time.errors = ['Appointment end time must be before 9 PM EST']
                    return False

            except Exception as e:
                self.tutor_service_id.errors = ['Error calculating appointment duration']
                return False
        else:
            # Validate regular appointment fields
            if not self.start_date.data:
                self.start_date.errors = ['Date is required for regular appointments.']
                return False
            if not self.start_time.data:
                self.start_time.errors = ['Start time is required for regular appointments.']
                return False
            if not self.end_time.data:
                self.end_time.errors = ['End time is required for regular appointments.']
                return False

            # Convert form fields to datetime objects for validation
            try:
                start_time_obj = datetime.strptime(self.start_time.data, '%H:%M').time()
            except (ValueError, TypeError):
                self.start_time.errors = ['Invalid time format']
                return False

            # Convert end_time to time object if it's not already
            try:
                if isinstance(self.end_time.data, str):
                    end_time_obj = datetime.strptime(self.end_time.data, '%H:%M').time()
                else:
                    end_time_obj = self.end_time.data
            except (ValueError, TypeError):
                self.end_time.errors = ['Invalid time format']
                return False

            # Check subscription if using one
            if self.is_subscription_based.data and self.subscription_id.data:
                # Calculate appointment duration in minutes
                start_datetime = datetime.combine(self.start_date.data, start_time_obj)
                end_datetime = datetime.combine(self.start_date.data, end_time_obj)
                duration_minutes = (end_datetime - start_datetime).total_seconds() / 60

                # Check if subscription has enough hours
                from app.services.subscription_service import SubscriptionService
                if not SubscriptionService.check_subscription_available(self.subscription_id.data, duration_minutes):
                    self.subscription_id.errors = ['Not enough hours remaining in this subscription']
                    return False

            # Verify transport fee if applied
            if self.apply_transport_fee.data and not self.is_subscription_based.data:
                if self.transport_fee.data is None or self.transport_fee.data < 0:
                    self.transport_fee.errors = ['Transport fee must be 0 or greater']
                    return False

            start_datetime = datetime.combine(self.start_date.data, start_time_obj)
            end_datetime = datetime.combine(self.start_date.data, end_time_obj)

            # Ensure end time is after start time
            if end_datetime <= start_datetime:
                self.end_time.errors = ['End time must be after start time']
                return False

            # Ensure appointment is within business hours (8AM-9PM EST)
            business_start = time(8, 0)  # 8 AM
            business_end = time(21, 0)   # 9 PM

            if start_time_obj < business_start or end_time_obj > business_end:
                self.start_time.errors = ['Appointments must be between 8 AM and 9 PM EST']
                return False

        return True

# RecurringAppointmentForm has been merged into AppointmentForm
# This class is no longer needed as the unified AppointmentForm handles both regular and recurring appointments

class TimeOffResponseForm(FlaskForm):
    status = SelectField('Status', choices=[
        ('approved', 'Approve'),
        ('rejected', 'Reject')
    ], validators=[DataRequired()])
    manager_notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Submit Response')


class TecfeeEnrollmentForm(FlaskForm):
    """Form for enrolling a client in the TECFÉE program."""
    client_id = SelectField('Client', validators=[DataRequired()], coerce=int)
    pricing_type = SelectField('Pricing Option', choices=[
        ('per_session', 'Per Module ($44.99/module)'),
        ('full_package', 'Full Package ($399.00 for all 12 modules)')
    ], validators=[DataRequired()])
    start_date = DateField('Start Date', validators=[DataRequired()], default=datetime.now().date())
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Enroll Client')


class GroupSessionForm(FlaskForm):
    """Form for creating group sessions."""
    program_id = SelectField('Program', validators=[DataRequired()], coerce=int)
    module_id = SelectField('Module', validators=[DataRequired()], coerce=int)
    tutor_id = SelectField('Tutor', validators=[DataRequired()], coerce=int)
    session_date = DateField('Session Date', validators=[DataRequired()])
    session_time = SelectField('Session Time', validators=[DataRequired()], choices=get_time_choices())
    duration_minutes = IntegerField('Duration (minutes)', validators=[DataRequired(), NumberRange(min=30, max=180)], default=60)
    min_participants = IntegerField('Minimum Participants', validators=[DataRequired(), NumberRange(min=1, max=20)], default=4)
    max_participants = IntegerField('Maximum Participants', validators=[DataRequired(), NumberRange(min=1, max=20)], default=10)
    tutor_rate_per_student = DecimalField('Tutor Rate per Student ($)', validators=[DataRequired(), NumberRange(min=0)], default=15.00)
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Create Group Session')


class ProgramPricingForm(FlaskForm):
    """Form for managing program pricing."""
    program_id = SelectField('Program', validators=[DataRequired()], coerce=int)
    pricing_type = SelectField('Pricing Type', choices=[
        ('per_session', 'Per Session'),
        ('full_package', 'Full Package')
    ], validators=[DataRequired()])
    price = DecimalField('Price ($)', validators=[DataRequired(), NumberRange(min=0)])
    description = TextAreaField('Description', validators=[Optional()])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save Pricing')