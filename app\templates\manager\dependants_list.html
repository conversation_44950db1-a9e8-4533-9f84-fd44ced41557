{% extends "base.html" %}

{% block title %}Dependants - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Dependants</h2>
        <p class="text-muted">Manage all dependants in the system</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_dependant') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Dependant
        </a>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-8">
                        {{ form.search.label(class="form-label") }}
                        {{ form.search(class="form-control", placeholder="Search by name, email, phone, or grade...") }}
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-search"></i> Search
                        </button>
                        <a href="{{ url_for('manager.dependants_list') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Dependants List -->
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">All Dependants ({{ dependants|length }})</h5>
                <span class="badge bg-light text-primary">{{ dependants|length }} total</span>
            </div>
            <div class="card-body p-0">
                {% if dependants %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Grade</th>
                                    <th>Age</th>
                                    <th>Status</th>
                                    <th>Relationships</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dependant in dependants %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    {{ dependant.first_name[0] }}{{ dependant.last_name[0] }}
                                                </div>
                                                <div>
                                                    <strong>{{ dependant.first_name }} {{ dependant.last_name }}</strong>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            {% if dependant.email %}
                                                <a href="mailto:{{ dependant.email }}">{{ dependant.email }}</a>
                                            {% else %}
                                                <span class="text-muted">No email</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dependant.phone %}
                                                <a href="tel:{{ dependant.phone }}">{{ dependant.phone }}</a>
                                            {% else %}
                                                <span class="text-muted">No phone</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dependant.school_grade %}
                                                <span class="badge bg-info">{{ dependant.grade_display }}</span>
                                            {% else %}
                                                <span class="text-muted">Not specified</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dependant.date_of_birth %}
                                                {{ dependant.date_of_birth.strftime('%Y-%m-%d') }}
                                            {% else %}
                                                <span class="text-muted">Unknown</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if dependant.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ dependant.client_relationships|length }} relationship(s)</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('manager.view_dependant', id=dependant.id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ url_for('manager.edit_dependant', id=dependant.id) }}"
                                                   class="btn btn-sm btn-primary" title="Edit Dependant">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No dependants found</h5>
                        <p class="text-muted">
                            {% if form.search.data %}
                                No dependants match your search criteria.
                            {% else %}
                                There are no dependants in the system yet.
                            {% endif %}
                        </p>
                        <a href="{{ url_for('manager.new_dependant') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add First Dependant
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #007bff;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}
</style>
{% endblock %}
