{% extends 'base.html' %}

{% block title %}Manage Tutor Availability{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Manage Availability for {{ tutor.full_name }}</h1>
            <p class="lead">Set the tutor's regular availability schedule.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.view_tutor', id=tutor.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Tutor
            </a>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Availability</h5>
                </div>
                <div class="card-body">
                    {% if availabilities %}
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for availability in availabilities %}
                                <tr>
                                    <td>{{ availability.day_name }}</td>
                                    <td>{{ availability.time_range }}</td>
                                    <td>
                                        {% if availability.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('manager_tutor_profile.edit_tutor_availability', tutor_id=tutor.id, id=availability.id) }}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <form method="POST" action="{{ url_for('manager_tutor_profile.delete_tutor_availability', tutor_id=tutor.id, id=availability.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this availability slot?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="alert alert-info">
                            No availability set for this tutor yet. Use the form to add availability time slots.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add New Availability</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id) }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.day_of_week.label(class="form-label") }}
                            {{ form.day_of_week(class="form-select") }}
                        </div>
                        <div class="mb-3">
                            {{ form.start_time.label(class="form-label") }}
                            {{ form.start_time(class="form-control", type="time") }}
                            <small class="text-muted">Use 24-hour format (e.g., 14:00 for 2:00 PM)</small>
                        </div>
                        <div class="mb-3">
                            {{ form.end_time.label(class="form-label") }}
                            {{ form.end_time(class="form-control" + (" is-invalid" if form.end_time.errors else ""), type="time") }}
                            <small class="text-muted">Use 24-hour format (e.g., 16:00 for 4:00 PM)</small>
                            {% if form.end_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Add Availability</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
