# app/utils/validators.py
import re
from wtforms.validators import ValidationError

def validate_phone_number(form, field):
    """Validate that a string is a valid phone number format."""
    # Remove any non-digit characters
    phone = re.sub(r'\D', '', field.data)
    
    # Check if it's a valid US phone number (10 digits)
    if len(phone) != 10:
        raise ValidationError('Phone number must be 10 digits.')

def validate_email(form, field):
    """Additional email validation beyond wtforms.validators.Email."""
    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', field.data):
        raise ValidationError('Invalid email address.')