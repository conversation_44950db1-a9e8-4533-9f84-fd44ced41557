/**
 * Sidebar Navigation JavaScript - Apple-inspired UX with Collapsible Sections
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run for manager role
    if (!document.querySelector('.sidebar')) {
        return;
    }

    initializeSidebar();
    initializeCollapsibleSections();
    setActiveSidebarItem();
    setupKeyboardShortcuts();
    loadSidebarState();
});

/**
 * Initialize sidebar functionality
 */
function initializeSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    if (!sidebarToggle || !sidebar || !sidebarOverlay) {
        return;
    }

    // Toggle sidebar on button click
    sidebarToggle.addEventListener('click', function(e) {
        e.preventDefault();
        toggleSidebar();
    });

    // Close sidebar when clicking overlay
    sidebarOverlay.addEventListener('click', function() {
        closeSidebar();
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992) {
            // Desktop: ensure sidebar is visible and overlay is hidden
            sidebar.classList.remove('show');
            sidebarOverlay.classList.remove('show');
        }
    });

    // Close sidebar on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('show')) {
            closeSidebar();
        }
    });
}

/**
 * Toggle sidebar visibility/collapse
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 992) {
        // Mobile: toggle with overlay
        sidebar.classList.toggle('show');
        sidebarOverlay.classList.toggle('show');
    } else {
        // Desktop: toggle collapsed state
        sidebar.classList.toggle('collapsed');

        if (mainContent) {
            mainContent.classList.toggle('sidebar-collapsed');
        }

        // Save state to localStorage
        const isCollapsed = sidebar.classList.contains('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    }
}

/**
 * Close sidebar
 */
function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebarOverlay');

    sidebar.classList.remove('show');
    sidebarOverlay.classList.remove('show');
}

/**
 * Set active sidebar item based on current URL
 */
function setActiveSidebarItem() {
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar-link');

    // Remove any existing active states
    sidebarLinks.forEach(link => {
        link.classList.remove('active');
        link.removeAttribute('aria-current');
    });

    // Find and activate the matching link
    let bestMatch = null;
    let bestMatchLength = 0;

    sidebarLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/' && href !== '/manager') {
            // Find the most specific match (longest path)
            if (href.length > bestMatchLength) {
                bestMatch = link;
                bestMatchLength = href.length;
            }
        }
    });

    if (bestMatch) {
        bestMatch.classList.add('active');
        bestMatch.setAttribute('aria-current', 'page');

        // Scroll the active item into view if needed
        bestMatch.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }
}

/**
 * Initialize collapsible sections
 */
function initializeCollapsibleSections() {
    const sidebarHeaders = document.querySelectorAll('.sidebar-header');

    sidebarHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const section = this.parentElement;
            const sectionName = this.getAttribute('data-section');

            // Don't collapse if sidebar is collapsed
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('collapsed')) {
                return;
            }

            // Toggle collapsed state
            section.classList.toggle('collapsed');

            // Save state to localStorage
            const isCollapsed = section.classList.contains('collapsed');
            localStorage.setItem(`section-${sectionName}-collapsed`, isCollapsed);
        });
    });
}

/**
 * Load sidebar state from localStorage
 */
function loadSidebarState() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');

    // Load sidebar collapsed state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed && window.innerWidth > 992) {
        sidebar.classList.add('collapsed');
        if (mainContent) {
            mainContent.classList.add('sidebar-collapsed');
        }
    }

    // Load section collapsed states
    const sections = document.querySelectorAll('.sidebar-section');
    sections.forEach(section => {
        const header = section.querySelector('.sidebar-header');
        const sectionName = header ? header.getAttribute('data-section') : null;

        if (sectionName) {
            const isCollapsed = localStorage.getItem(`section-${sectionName}-collapsed`) === 'true';
            if (isCollapsed) {
                section.classList.add('collapsed');
            }
        }
    });
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search focus
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }

        // Ctrl/Cmd + B for sidebar toggle
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }

        // Ctrl/Cmd + [ for sidebar collapse (desktop only)
        if ((e.ctrlKey || e.metaKey) && e.key === '[' && window.innerWidth > 992) {
            e.preventDefault();
            const sidebar = document.getElementById('sidebar');
            if (sidebar && !sidebar.classList.contains('collapsed')) {
                toggleSidebar();
            }
        }

        // Ctrl/Cmd + ] for sidebar expand (desktop only)
        if ((e.ctrlKey || e.metaKey) && e.key === ']' && window.innerWidth > 992) {
            e.preventDefault();
            const sidebar = document.getElementById('sidebar');
            if (sidebar && sidebar.classList.contains('collapsed')) {
                toggleSidebar();
            }
        }
    });
}

/**
 * Add smooth hover effects to sidebar links
 */
function addSidebarHoverEffects() {
    const sidebarLinks = document.querySelectorAll('.sidebar-link');

    sidebarLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            // Add subtle animation on hover
            this.style.transform = 'translateX(2px)';
        });

        link.addEventListener('mouseleave', function() {
            // Reset animation
            this.style.transform = 'translateX(0)';
        });
    });
}

/**
 * Initialize sidebar animations
 */
function initializeSidebarAnimations() {
    const sidebarSections = document.querySelectorAll('.sidebar-section');

    // Stagger animation for sections
    sidebarSections.forEach((section, index) => {
        section.style.animationDelay = `${index * 0.1}s`;
    });
}

/**
 * Handle sidebar link clicks for better UX
 */
function handleSidebarLinkClicks() {
    const sidebarLinks = document.querySelectorAll('.sidebar-link');

    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            const icon = this.querySelector('i');
            if (icon && !icon.classList.contains('fa-spin')) {
                const originalClass = icon.className;
                icon.className = 'fas fa-spinner fa-spin';

                // Restore original icon after navigation
                setTimeout(() => {
                    icon.className = originalClass;
                }, 1000);
            }

            // Close sidebar on mobile after click
            if (window.innerWidth <= 992) {
                setTimeout(() => {
                    closeSidebar();
                }, 150);
            }
        });
    });
}

/**
 * Update badge counts dynamically
 */
function updateBadgeCounts() {
    // This function can be called to update notification badges
    // Implementation would depend on your backend API

    // Example: Update time-off requests badge
    fetch('/api/manager/pending-time-off-count')
        .then(response => response.json())
        .then(data => {
            const timeOffBadge = document.querySelector('a[href*="time_off_requests"] .badge');
            if (timeOffBadge && data.count > 0) {
                timeOffBadge.textContent = data.count;
                timeOffBadge.style.display = 'inline';
            } else if (timeOffBadge) {
                timeOffBadge.style.display = 'none';
            }
        })
        .catch(error => {
            console.log('Could not update badge counts:', error);
        });
}

/**
 * Initialize all sidebar functionality
 */
function initializeAllSidebarFeatures() {
    addSidebarHoverEffects();
    initializeSidebarAnimations();
    handleSidebarLinkClicks();

    // Update badge counts every 30 seconds
    updateBadgeCounts();
    setInterval(updateBadgeCounts, 30000);
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.sidebar')) {
        initializeAllSidebarFeatures();
    }
});

/**
 * Utility function to highlight sidebar section
 */
function highlightSidebarSection(sectionName) {
    const sections = document.querySelectorAll('.sidebar-section');
    sections.forEach(section => {
        const header = section.querySelector('.sidebar-header span');
        if (header && header.textContent.toLowerCase().includes(sectionName.toLowerCase())) {
            section.style.backgroundColor = 'rgba(0, 123, 255, 0.05)';
            setTimeout(() => {
                section.style.backgroundColor = '';
            }, 2000);
        }
    });
}

// Export functions for external use
window.sidebarUtils = {
    toggleSidebar,
    closeSidebar,
    setActiveSidebarItem,
    updateBadgeCounts,
    highlightSidebarSection,
    initializeCollapsibleSections,
    loadSidebarState
};
