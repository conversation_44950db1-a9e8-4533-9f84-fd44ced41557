{% extends "base.html" %}

{% block title %}Search Results{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mb-4">
        <i class="fas fa-search text-primary me-2"></i> Search Results
        {% if query %}
            <small class="text-muted">for "{{ query }}"</small>
        {% endif %}
    </h1>

    <!-- Search Form -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ url_for('manager.global_search') }}" class="row g-3">
                <div class="col-md-10">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" class="form-control" name="q" value="{{ query }}" placeholder="Search across all entities..." autofocus>
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Search</button>
                </div>
            </form>
        </div>
    </div>

    {% if not query %}
        <div class="text-center p-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5>Enter a search term</h5>
            <p class="text-muted">Search for clients, tutors, services, appointments, or invoices.</p>
        </div>
    {% elif total_count == 0 %}
        <div class="text-center p-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5>No results found</h5>
            <p class="text-muted">No matches found for "{{ query }}". Try a different search term.</p>
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> Found {{ total_count }} results for "{{ query }}"
        </div>

        <!-- Results Tabs -->
        <ul class="nav nav-tabs mb-4" id="resultsTabs" role="tablist">
            {% if results.clients %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="clients-tab" data-bs-toggle="tab" data-bs-target="#clients" type="button" role="tab">
                        Clients ({{ results.clients|length }})
                    </button>
                </li>
            {% endif %}

            {% if results.tutors %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if not results.clients %}active{% endif %}" id="tutors-tab" data-bs-toggle="tab" data-bs-target="#tutors" type="button" role="tab">
                        Tutors ({{ results.tutors|length }})
                    </button>
                </li>
            {% endif %}

            {% if results.services %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if not results.clients and not results.tutors %}active{% endif %}" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab">
                        Services ({{ results.services|length }})
                    </button>
                </li>
            {% endif %}

            {% if results.appointments %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if not results.clients and not results.tutors and not results.services %}active{% endif %}" id="appointments-tab" data-bs-toggle="tab" data-bs-target="#appointments" type="button" role="tab">
                        Appointments ({{ results.appointments|length }})
                    </button>
                </li>
            {% endif %}

            {% if results.invoices %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if not results.clients and not results.tutors and not results.services and not results.appointments %}active{% endif %}" id="invoices-tab" data-bs-toggle="tab" data-bs-target="#invoices" type="button" role="tab">
                        Invoices ({{ results.invoices|length }})
                    </button>
                </li>
            {% endif %}
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="resultsTabContent">
            <!-- Clients Tab -->
            {% if results.clients %}
                <div class="tab-pane fade show active" id="clients" role="tabpanel">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Clients</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for client in results.clients %}
                                        <tr>
                                            <td>{{ client.first_name }} {{ client.last_name }}</td>
                                            <td>{{ client.user.email }}</td>
                                            <td>{{ client.phone }}</td>
                                            <td>
                                                <span class="text-muted">View/Edit coming soon</span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Tutors Tab -->
            {% if results.tutors %}
                <div class="tab-pane fade {% if not results.clients %}show active{% endif %}" id="tutors" role="tabpanel">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Tutors</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for tutor in results.tutors %}
                                        <tr>
                                            <td>{{ tutor.first_name }} {{ tutor.last_name }}</td>
                                            <td>{{ tutor.user.email }}</td>
                                            <td>{{ tutor.phone }}</td>
                                            <td>
                                                {% if tutor.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('manager.view_tutor', id=tutor.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="{{ url_for('manager.edit_tutor', id=tutor.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Services Tab -->
            {% if results.services %}
                <div class="tab-pane fade {% if not results.clients and not results.tutors %}show active{% endif %}" id="services" role="tabpanel">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Services</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Price</th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for service in results.services %}
                                        <tr>
                                            <td>{{ service.name }}</td>
                                            <td>${{ service.default_price }}</td>
                                            <td>{{ service.duration_minutes }} min</td>
                                            <td>
                                                {% if service.is_active %}
                                                    <span class="badge bg-success">Active</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Inactive</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('manager.edit_service', id=service.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <a href="{{ url_for('manager.tutors_by_service', id=service.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-users"></i> Tutors
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Appointments Tab -->
            {% if results.appointments %}
                <div class="tab-pane fade {% if not results.clients and not results.tutors and not results.services %}show active{% endif %}" id="appointments" role="tabpanel">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Appointments</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Client</th>
                                        <th>Tutor</th>
                                        <th>Service</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in results.appointments %}
                                        <tr>
                                            <td>{{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>
                                                {% if appointment.client %}
                                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if appointment.tutor %}
                                                    {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if appointment.tutor_service and appointment.tutor_service.service %}
                                                    {{ appointment.tutor_service.service.name }}
                                                {% else %}
                                                    <span class="text-muted">N/A</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if appointment.status == 'scheduled' %}
                                                    <span class="badge bg-primary">Scheduled</span>
                                                {% elif appointment.status == 'completed' %}
                                                    <span class="badge bg-success">Completed</span>
                                                {% elif appointment.status == 'cancelled' %}
                                                    <span class="badge bg-danger">Cancelled</span>
                                                {% elif appointment.status == 'no-show' %}
                                                    <span class="badge bg-warning">No-show</span>
                                                {% elif appointment.status == 'awaiting_confirmation' %}
                                                    <span class="badge bg-info">Awaiting Confirmation</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('manager.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Invoices Tab -->
            {% if results.invoices %}
                <div class="tab-pane fade {% if not results.clients and not results.tutors and not results.services and not results.appointments %}show active{% endif %}" id="invoices" role="tabpanel">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Invoices</h5>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Date</th>
                                        <th>Client</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in results.invoices %}
                                        <tr>
                                            <td>{{ invoice.id }}</td>
                                            <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ invoice.client.first_name }} {{ invoice.client.last_name }}</td>
                                            <td>${{ invoice.total_amount }}</td>
                                            <td>
                                                {% if invoice.status == 'pending' %}
                                                    <span class="badge bg-warning">Pending</span>
                                                {% elif invoice.status == 'paid' %}
                                                    <span class="badge bg-success">Paid</span>
                                                {% elif invoice.status == 'cancelled' %}
                                                    <span class="badge bg-danger">Cancelled</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('manager.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
