<!-- app/templates/manager/subscription_detail.html -->
{% extends "base.html" %}

{% block title %}Subscription Details - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Subscription Details</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.subscriptions_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Subscriptions
        </a>

        {% if subscription.is_active %}
            <button type="button" class="btn btn-danger ms-2" data-bs-toggle="modal" data-bs-target="#cancelSubscriptionModal">
                <i class="fas fa-ban"></i> Cancel
            </button>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Subscription Information -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ subscription.plan.name }}</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Subscription Status</h6>
                    <p>
                        {% if subscription.is_active %}
                            <span class="badge bg-success p-2">Active</span>
                        {% elif subscription.status == 'cancelled' %}
                            <span class="badge bg-danger p-2">Cancelled</span>
                        {% elif subscription.is_expired %}
                            <span class="badge bg-secondary p-2">Expired</span>
                        {% endif %}
                    </p>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Client</h6>
                    <p>
                        <a href="{{ url_for('manager.view_client', id=subscription.client_id) }}">
                            {{ subscription.client.first_name }} {{ subscription.client.last_name }}
                        </a>
                    </p>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Subscription Period</h6>
                    <p>{{ subscription.start_date.strftime('%B %d, %Y') }} to {{ subscription.end_date.strftime('%B %d, %Y') }}</p>
                    {% if subscription.is_active %}
                        <p class="small text-muted">
                            {% set days_remaining = (subscription.end_date - now.date()).days %}
                            {{ days_remaining }} days remaining
                        </p>
                    {% endif %}
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Plan Details</h6>
                    <p>{{ subscription.plan.description or "No description available." }}</p>
                    <ul>
                        <li>Duration: {{ subscription.plan.duration_months }} month{% if subscription.plan.duration_months > 1 %}s{% endif %}</li>
                        <li>Maximum Hours: {{ subscription.plan.max_hours }}</li>
                        <li>Price: ${{ "%.2f"|format(subscription.plan.price) }}</li>
                    </ul>
                </div>

                {% if subscription.is_active %}
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Benefits</h6>
                        <ul class="mb-0">
                            <li>No transport fees for tutoring sessions</li>
                            <li>Priority scheduling with tutors</li>
                            <li>Consistent pricing regardless of service type</li>
                        </ul>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Hours Usage -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Hours Usage</h5>
            </div>
            <div class="card-body">
                <div class="mb-4">
                    <h6 class="text-muted mb-3">Usage Overview</h6>
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar {{ 'bg-warning' if subscription.usage_percentage > 75 else 'bg-success' }}"
                             role="progressbar"
                             style="width: {{ subscription.usage_percentage }}%"
                             aria-valuenow="{{ subscription.usage_percentage }}"
                             aria-valuemin="0"
                             aria-valuemax="100">
                            {{ "%.1f"|format(subscription.usage_percentage) }}%
                        </div>
                    </div>

                    <div class="row text-center">
                        <div class="col-4">
                            <h3 class="mb-0">{{ "%.1f"|format(subscription.hours_used) }}</h3>
                            <small class="text-muted">Hours Used</small>
                        </div>
                        <div class="col-4">
                            <h3 class="mb-0">{{ "%.1f"|format(subscription.hours_remaining) }}</h3>
                            <small class="text-muted">Hours Remaining</small>
                        </div>
                        <div class="col-4">
                            <h3 class="mb-0">{{ subscription.plan.max_hours }}</h3>
                            <small class="text-muted">Total Hours</small>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-3">Usage History</h6>
                    {% if usage_history %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Client</th>
                                        <th>Tutor</th>
                                        <th>Hours</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for usage in usage_history %}
                                        <tr>
                                            <td>{{ usage.insert_date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                {% if usage.source_appointment and usage.source_appointment.client %}
                                                    {{ usage.source_appointment.client.first_name }} {{ usage.source_appointment.client.last_name }}
                                                {% else %}
                                                    N/A
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if usage.source_appointment and usage.source_appointment.tutor %}
                                                    {{ usage.source_appointment.tutor.first_name }} {{ usage.source_appointment.tutor.last_name }}
                                                {% else %}
                                                    N/A
                                                {% endif %}
                                            </td>
                                            <td>{{ "%.1f"|format(usage.hours_used) }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">No usage recorded yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Appointments Section -->
{% if upcoming_appointments %}
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Upcoming Subscription Appointments</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Date & Time</th>
                        <th>Client</th>
                        <th>Tutor</th>
                        <th>Service</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in upcoming_appointments %}
                        <tr>
                            <td>
                                {{ appointment.start_time.strftime('%Y-%m-%d') }}<br>
                                <small>{{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}</small>
                            </td>
                            <td>
                                {% if appointment.client %}
                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                            <td>
                                {% if appointment.tutor_service and appointment.tutor_service.service %}
                                    {{ appointment.tutor_service.service.name }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <a href="{{ url_for('manager.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> View
                                </a>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Administrative Actions -->
<div class="card shadow mt-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Administrative Actions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% if subscription.is_active %}
                <div class="col-md-4">
                    <h6>Update Subscription</h6>
                    <form action="{{ url_for('manager.update_subscription', id=subscription.id) }}" method="POST" class="row g-3">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <div class="col-md-8">
                            <select name="status" class="form-select">
                                <option value="active" selected>Active</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary w-100">Update</button>
                        </div>
                    </form>
                </div>
            {% endif %}

            <div class="col-md-4">
                <h6>Update Usage</h6>
                <form action="{{ url_for('manager.update_subscription_usage', id=subscription.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-sync"></i> Refresh Usage Data
                    </button>
                </form>
                <small class="text-muted mt-1">Fix missing usage records</small>
            </div>

            <div class="col-md-4 text-end">
                {% if subscription.is_active %}
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelSubscriptionModal">
                        <i class="fas fa-ban"></i> Cancel Subscription
                    </button>
                {% else %}
                    <p class="text-muted">This subscription is no longer active.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Cancel Subscription Modal -->
{% if subscription.is_active %}
<div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this subscription?</p>
                <p class="text-danger">This action cannot be undone. The client will no longer be able to use their subscription hours after cancellation.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep Subscription</button>
                <form action="{{ url_for('manager.cancel_subscription', id=subscription.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Yes, Cancel Subscription</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}