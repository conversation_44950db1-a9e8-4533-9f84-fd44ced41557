{% extends 'base.html' %}

{% block title %}Manage Availability{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle All Day checkbox
    const allDayCheckbox = document.getElementById('all_day_checkbox');
    const startTimeSelect = document.getElementById('start_time_select');
    const endTimeSelect = document.getElementById('end_time_select');
    const timeSelectionContainer = document.getElementById('time_selection_container');

    if (allDayCheckbox && startTimeSelect && endTimeSelect) {
        // Set initial values
        if (allDayCheckbox.checked) {
            timeSelectionContainer.style.display = 'none';
            // Set to 8:00 AM - 9:00 PM
            startTimeSelect.value = '08:00';
            endTimeSelect.value = '21:00';
        }

        // Add event listener for checkbox changes
        allDayCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // Hide time selection and set to all day (8:00 AM - 9:00 PM)
                timeSelectionContainer.style.display = 'none';
                startTimeSelect.value = '08:00';
                endTimeSelect.value = '21:00';
            } else {
                // Show time selection
                timeSelectionContainer.style.display = 'block';
            }
        });
    }

    // Debug form submission
    const availabilityForm = document.querySelector('form[action*="availability"]');
    if (availabilityForm) {
        availabilityForm.addEventListener('submit', function(e) {
            console.log('Form submission detected');
            console.log('Form data:', new FormData(this));
            // Don't prevent default - let the form submit normally
        });
    }
});
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Manage Your Availability</h1>
    <p class="lead">Set your regular availability to help managers schedule your appointments.</p>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Your Current Availability</h5>
                </div>
                <div class="card-body">
                    {% if availabilities %}
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for availability in availabilities %}
                                <tr>
                                    <td>{{ availability.day_name }}</td>
                                    <td>{{ availability.time_range }}</td>
                                    <td>
                                        {% if availability.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('tutor.edit_availability', id=availability.id) }}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <form method="POST" action="{{ url_for('tutor.delete_availability', id=availability.id) }}" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this availability slot?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="alert alert-info">
                            You haven't set any availability yet. Use the form to add your available time slots.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add New Availability</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('tutor.availability') }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.day_of_week.label(class="form-label") }}
                            {{ form.day_of_week(class="form-select") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.all_day(class="form-check-input", id="all_day_checkbox") }}
                            {{ form.all_day.label(class="form-check-label") }}
                        </div>
                        <div id="time_selection_container">
                            <div class="mb-3">
                                {{ form.start_time.label(class="form-label") }}
                                {{ form.start_time(class="form-select", id="start_time_select") }}
                                <small class="text-muted">Select a time in 30-minute increments</small>
                            </div>
                            <div class="mb-3">
                                {{ form.end_time.label(class="form-label") }}
                                {{ form.end_time(class="form-select" + (" is-invalid" if form.end_time.errors else ""), id="end_time_select") }}
                                <small class="text-muted">Select a time in 30-minute increments</small>
                                {% if form.end_time.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.end_time.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Add Availability</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
