{% extends "base.html" %}

{% block title %}{{ title }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
        <p class="text-muted">
            {% if dependant %}
                Update dependant information
            {% else %}
                Create a new dependant profile
            {% endif %}
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.dependants_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Dependants
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Dependant Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.csrf_token }}

                    <!-- Basic Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">Basic Information</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.first_name.label(class="form-label") }}
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.last_name.label(class="form-label") }}
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">Contact Information <small class="text-muted">(Optional)</small></h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Optional - for dependants who need their own login</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Address Section -->
                        <div class="col-md-12 mb-3">
                            <h6 class="text-muted mb-3">Address Information</h6>
                        </div>

                        <div class="col-md-3 mb-3">
                            {{ form.civic_number.label(class="form-label") }}
                            {{ form.civic_number(class="form-control" + (" is-invalid" if form.civic_number.errors else "")) }}
                            {% if form.civic_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.civic_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-9 mb-3">
                            {{ form.street.label(class="form-label") }}
                            {{ form.street(class="form-control" + (" is-invalid" if form.street.errors else "")) }}
                            {% if form.street.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.street.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.city.label(class="form-label") }}
                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.province.label(class="form-label") }}
                            {{ form.province(class="form-select" + (" is-invalid" if form.province.errors else "")) }}
                            {% if form.province.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.province.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4 mb-3">
                            {{ form.postal_code.label(class="form-label") }}
                            {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                            {% if form.postal_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.postal_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.country.label(class="form-label") }}
                            {{ form.country(class="form-select" + (" is-invalid" if form.country.errors else "")) }}
                            {% if form.country.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.country.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">Personal Information</h6>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.date_of_birth.label(class="form-label") }}
                            {{ form.date_of_birth(class="form-control" + (" is-invalid" if form.date_of_birth.errors else "")) }}
                            {% if form.date_of_birth.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.date_of_birth.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.school_grade.label(class="form-label") }}
                            {{ form.school_grade(class="form-select" + (" is-invalid" if form.school_grade.errors else "")) }}
                            {% if form.school_grade.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.school_grade.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Select the appropriate grade level for the dependant.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            {{ form.preferred_language.label(class="form-label") }}
                            {{ form.preferred_language(class="form-select" + (" is-invalid" if form.preferred_language.errors else "")) }}
                            {% if form.preferred_language.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.preferred_language.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="text-primary mb-3">Additional Notes</h6>
                            {{ form.notes.label(class="form-label") }}
                            {{ form.notes(class="form-control", rows="4", placeholder="Any additional information about the dependant...") }}
                            {% if form.notes.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.notes.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ url_for('manager.dependants_list') }}" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                {{ form.submit(class="btn btn-primary") }}
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">Information</h6>
            </div>
            <div class="card-body">
                <h6 class="text-info">About Dependants</h6>
                <p class="small">
                    Dependants are individuals who are associated with clients but don't need full client accounts.
                    They're perfect for:
                </p>
                <ul class="small">
                    <li>Children of parent clients</li>
                    <li>Students in institutional programs</li>
                    <li>Employees in corporate training</li>
                </ul>

                <h6 class="text-info mt-3">Key Features</h6>
                <ul class="small">
                    <li><strong>No billing:</strong> Dependants don't receive invoices</li>
                    <li><strong>Optional login:</strong> Email only needed if they need system access</li>
                    <li><strong>Flexible relationships:</strong> Can be linked to multiple clients</li>
                    <li><strong>Simple management:</strong> Easy to create, edit, and organize</li>
                </ul>

                {% if dependant %}
                <div class="mt-3 pt-3 border-top">
                    <h6 class="text-warning">Actions</h6>
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('manager.view_dependant', id=dependant.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> Delete Dependant
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if dependant %}
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Delete Dependant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ dependant.first_name }} {{ dependant.last_name }}</strong>?</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone. All relationships with clients will also be removed.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('manager.delete_dependant', id=dependant.id) }}" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Dependant
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
