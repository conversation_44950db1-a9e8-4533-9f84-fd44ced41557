<!-- app/templates/client/invoices.html -->
{% extends "base.html" %}

{% block title %}{{ t('invoices.your_invoices') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('invoices.your_invoices') }}</h2>
        <p class="text-muted">{{ t('invoices.view_and_pay') }}</p>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <ul class="nav nav-tabs card-header-tabs" id="invoicesTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="true">
                    {{ t('invoices.pending') }}
                    {% if pending_invoices %}
                        <span class="badge bg-warning text-dark ms-1">{{ pending_invoices|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid" type="button" role="tab" aria-controls="paid" aria-selected="false">
                    {{ t('invoices.paid') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="false">
                    {{ t('invoices.all') }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="invoicesTabsContent">
            <!-- Pending Invoices Tab -->
            <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                {% if pending_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.due_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.status') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in pending_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'danger' if invoice.is_overdue else 'warning' }}">
                                                {{ t('invoices.overdue') if invoice.is_overdue else t('invoices.' + invoice.status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                            <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-credit-card"></i> {{ t('invoices.pay') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_pending_invoices') }}</p>
                {% endif %}
            </div>

            <!-- Paid Invoices Tab -->
            <div class="tab-pane fade" id="paid" role="tabpanel" aria-labelledby="paid-tab">
                {% if paid_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.paid_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in paid_invoices %}
                                    <tr>
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.paid_date.strftime('%Y-%m-%d') if invoice.paid_date else '-' }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_paid_invoices') }}</p>
                {% endif %}
            </div>

            <!-- All Invoices Tab -->
            <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                {% if all_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.due_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.status') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in all_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }}">
                                                {{ t('invoices.overdue') if invoice.is_overdue else t('invoices.' + invoice.status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                            {% if invoice.status == 'pending' %}
                                                <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-sm btn-success">
                                                    <i class="fas fa-credit-card"></i> {{ t('invoices.pay') }}
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_invoices') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
