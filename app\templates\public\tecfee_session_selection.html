{% extends "base.html" %}

{% block title %}Sélection des Sessions - TECFÉE - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary mb-3">Sélection des Sessions TECFÉE</h1>
                <p class="lead">Choisissez les sessions auxquelles vous souhaitez participer</p>

                <!-- Dynamic pricing info based on enrollment data -->
                {% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}
                <div class="alert alert-success">
                    <i class="fas fa-star"></i>
                    <strong>Forfait Complet:</strong> Sélectionnez exactement 12 sessions (1 de chaque module) pour 399,00$
                    <br><small>Économisez 140,88$ par rapport au prix individuel!</small>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tarification par module:</strong> 44,99$ par session sélectionnée
                </div>
                {% endif %}
            </div>

            <!-- Session Selection Form -->
            <form method="POST" action="{{ url_for('public.process_session_selection') }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Sessions Disponibles</h5>
                    </div>
                    <div class="card-body">
                        {% if available_sessions %}
                        <!-- Group sessions by module -->
                        {% set sessions_by_module = {} %}
                        {% for session in available_sessions %}
                            {% if session.module %}
                                {% set module_order = session.module.module_order %}
                                {% if module_order not in sessions_by_module %}
                                    {% set _ = sessions_by_module.update({module_order: []}) %}
                                {% endif %}
                                {% set _ = sessions_by_module[module_order].append(session) %}
                            {% endif %}
                        {% endfor %}

                        <!-- Display sessions grouped by module -->
                        {% for module_order in sessions_by_module.keys() | sort %}
                        {% set module_sessions = sessions_by_module[module_order] %}
                        {% set first_session = module_sessions[0] %}
                        <div class="card mb-3 border-secondary">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-book text-primary"></i>
                                    Module {{ module_order }}: {{ first_session.module.name }}
                                    {% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}
                                    <span class="badge bg-success ms-2">Requis pour forfait complet</span>
                                    {% endif %}
                                </h6>
                                {% if first_session.module.description %}
                                <small class="text-muted">{{ first_session.module.description }}</small>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    {% for session in module_sessions %}
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 border-light">
                                            <div class="card-body">
                                                <div class="form-check">
                                                    <input class="form-check-input session-checkbox"
                                                           type="{% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}radio{% else %}checkbox{% endif %}"
                                                           name="{% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}module_{{ module_order }}_session{% else %}selected_sessions{% endif %}"
                                                           value="{{ session.id }}"
                                                           id="session_{{ session.id }}"
                                                           data-price="{% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}399{% else %}44.99{% endif %}"
                                                           data-module="{{ module_order }}"
                                                           {% if session.current_participants >= session.max_participants %}disabled{% endif %}>
                                                    <label class="form-check-label w-100" for="session_{{ session.id }}">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <div>
                                                                <strong>{{ session.session_date.strftime('%d %B') }}</strong><br>
                                                                <small class="text-muted">{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</small>
                                                            </div>
                                                            {% if enrollment_data and enrollment_data.pricing_type != 'full_package' %}
                                                            <span class="badge bg-primary">44,99$</span>
                                                            {% endif %}
                                                        </div>

                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <small class="text-success">
                                                                <i class="fas fa-users"></i>
                                                                {{ session.current_participants }}/{{ session.max_participants }} participants
                                                            </small>
                                                            {% if session.current_participants >= session.max_participants %}
                                                            <small class="text-danger">
                                                                <i class="fas fa-exclamation-triangle"></i> Complet
                                                            </small>
                                                            {% endif %}
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Aucune session disponible</h5>
                            <p class="text-muted">Il n'y a actuellement aucune session TECFÉE disponible pour l'inscription.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Summary Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Résumé de la Sélection</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Sessions sélectionnées:</strong> <span id="selected-count">0</span></p>
                                {% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}
                                <p class="mb-1"><strong>Modules requis:</strong> 12 (1 de chaque module)</p>
                                <p class="mb-1"><strong>Type:</strong> Forfait Complet</p>
                                {% else %}
                                <p class="mb-1"><strong>Prix par session:</strong> 44,99$</p>
                                <p class="mb-1"><strong>Type:</strong> Paiement par module</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6 text-end">
                                {% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}
                                <h4 class="text-success mb-0">
                                    Total: <span id="total-amount">399,00$</span>
                                </h4>
                                <small class="text-muted">Économie de 140,88$</small>
                                {% else %}
                                <h4 class="text-primary mb-0">
                                    Total: <span id="total-amount">0,00$</span>
                                </h4>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Progress indicator for full package -->
                        {% if enrollment_data and enrollment_data.pricing_type == 'full_package' %}
                        <div class="mt-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">Progression de sélection</small>
                                <small class="text-muted"><span id="modules-selected">0</span>/12 modules</small>
                            </div>
                            <div class="progress">
                                <div class="progress-bar bg-success" role="progressbar" id="selection-progress" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="12"></div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center">
                    <button type="submit" class="btn btn-success btn-lg px-5" id="continue-btn" disabled>
                        <i class="fas fa-arrow-right"></i> Continuer vers le Paiement
                    </button>
                    <div class="mt-3">
                        <a href="{{ url_for('public.tecfee_enrollment') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Retour à l'inscription
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.session-checkbox');
    const selectedCountEl = document.getElementById('selected-count');
    const totalAmountEl = document.getElementById('total-amount');
    const continueBtn = document.getElementById('continue-btn');
    const modulesSelectedEl = document.getElementById('modules-selected');
    const progressBar = document.getElementById('selection-progress');

    // Check if this is full package pricing
    const isFullPackage = {{ 'true' if enrollment_data and enrollment_data.pricing_type == 'full_package' else 'false' }};

    function updateSummary() {
        if (isFullPackage) {
            // For full package: count unique modules selected
            const selectedModules = new Set();
            const selectedSessions = document.querySelectorAll('.session-checkbox:checked');

            selectedSessions.forEach(checkbox => {
                const module = checkbox.getAttribute('data-module');
                if (module) {
                    selectedModules.add(module);
                }
            });

            const moduleCount = selectedModules.size;
            selectedCountEl.textContent = selectedSessions.length;

            if (modulesSelectedEl) {
                modulesSelectedEl.textContent = moduleCount;
            }

            if (progressBar) {
                const progressPercent = (moduleCount / 12) * 100;
                progressBar.style.width = progressPercent + '%';
                progressBar.setAttribute('aria-valuenow', moduleCount);
            }

            // Enable button only when exactly 12 modules are selected
            continueBtn.disabled = moduleCount !== 12;

            if (moduleCount === 0) {
                continueBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Sélectionnez 1 session de chaque module';
            } else if (moduleCount < 12) {
                continueBtn.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Sélectionnez ${12 - moduleCount} module(s) de plus`;
            } else if (moduleCount === 12) {
                continueBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Continuer vers le Paiement (399,00$)';
            } else {
                continueBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Trop de modules sélectionnés';
                continueBtn.disabled = true;
            }
        } else {
            // For per-session pricing: count individual sessions
            const selectedSessions = document.querySelectorAll('.session-checkbox:checked');
            const count = selectedSessions.length;
            const total = count * 44.99;

            selectedCountEl.textContent = count;
            totalAmountEl.textContent = total.toFixed(2).replace('.', ',') + '$';

            continueBtn.disabled = count === 0;

            if (count === 0) {
                continueBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Sélectionnez au moins une session';
            } else {
                continueBtn.innerHTML = '<i class="fas fa-arrow-right"></i> Continuer vers le Paiement';
            }
        }
    }

    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSummary);
    });

    // Initial update
    updateSummary();
});
</script>
{% endblock %}
