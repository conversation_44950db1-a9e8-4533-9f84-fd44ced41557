-- TutorAide Application Database Schema
-- Generated by <PERSON> 4
-- PostgreSQL Database Schema for Tutoring Management System

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (authentication and basic user info)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('manager', 'tutor', 'client', 'parent')),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    language_preference VARCHAR(10) DEFAULT 'fr',
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table (client information)
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    client_type VARCHAR(50) DEFAULT 'individual' CHECK (client_type IN ('individual', 'institutional')),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    civic_number VARCHAR(20),
    street VARCHAR(200),
    city VARCHAR(100),
    postal_code VARCHAR(10),
    province VARCHAR(50) DEFAULT 'Quebec',
    country VARCHAR(50) DEFAULT 'Canada',
    date_of_birth DATE,
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dependants table (children/students of clients)
CREATE TABLE dependants (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    grade_level VARCHAR(50),
    school_name VARCHAR(200),
    special_needs TEXT,
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dependant relationships table
CREATE TABLE dependant_relationships (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL,
    is_primary_contact BOOLEAN DEFAULT FALSE,
    can_schedule BOOLEAN DEFAULT TRUE,
    can_receive_updates BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(client_id, dependant_id)
);

-- Tutors table
CREATE TABLE tutors (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    civic_number VARCHAR(20),
    street VARCHAR(200),
    city VARCHAR(100),
    postal_code VARCHAR(10),
    province VARCHAR(50) DEFAULT 'Quebec',
    country VARCHAR(50) DEFAULT 'Canada',
    date_of_birth DATE,
    sin VARCHAR(20),
    bank_account_number_encrypted TEXT,
    bank_institution_number VARCHAR(10),
    bank_transit_number VARCHAR(10),
    hourly_rate DECIMAL(8,2),
    bio TEXT,
    qualifications TEXT,
    availability_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    base_rate DECIMAL(8,2),
    duration_minutes INTEGER DEFAULT 60,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tutor services (many-to-many with custom rates)
CREATE TABLE tutor_services (
    id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(id) ON DELETE CASCADE,
    custom_rate DECIMAL(8,2),
    transport_fee DECIMAL(8,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, service_id)
);

-- Programs table (like TECFÉE)
CREATE TABLE programs (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    total_sessions INTEGER,
    session_duration INTEGER DEFAULT 60,
    min_participants INTEGER DEFAULT 1,
    max_participants INTEGER DEFAULT 10,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program modules
CREATE TABLE program_modules (
    id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    module_order INTEGER NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    learning_objectives TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program pricing options
CREATE TABLE program_pricing (
    id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(id) ON DELETE CASCADE,
    pricing_type VARCHAR(50) NOT NULL CHECK (pricing_type IN ('per_session', 'full_package')),
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client enrollments in programs
CREATE TABLE enrollments (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    program_id INTEGER REFERENCES programs(id) ON DELETE CASCADE,
    pricing_type VARCHAR(50) NOT NULL,
    enrollment_date DATE NOT NULL,
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'suspended')),
    created_by INTEGER REFERENCES users(id),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Module progress tracking
CREATE TABLE module_progress (
    id SERIAL PRIMARY KEY,
    enrollment_id INTEGER REFERENCES enrollments(id) ON DELETE CASCADE,
    module_id INTEGER REFERENCES program_modules(id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    completion_date DATE,
    score DECIMAL(5,2),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(enrollment_id, module_id)
);

-- Group sessions
CREATE TABLE group_sessions (
    id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(id) ON DELETE CASCADE,
    module_id INTEGER REFERENCES program_modules(id) ON DELETE SET NULL,
    tutor_id INTEGER REFERENCES tutors(id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    max_participants INTEGER DEFAULT 10,
    current_participants INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled')),
    session_notes TEXT,
    tutor_payment_rate DECIMAL(8,2) DEFAULT 15.00,
    total_tutor_payment DECIMAL(10,2) DEFAULT 0.00,
    meeting_link VARCHAR(500),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group session participants
CREATE TABLE group_session_participants (
    id SERIAL PRIMARY KEY,
    group_session_id INTEGER REFERENCES group_sessions(id) ON DELETE CASCADE,
    enrollment_id INTEGER REFERENCES enrollments(id) ON DELETE CASCADE,
    attendance_status VARCHAR(50) CHECK (attendance_status IN ('registered', 'attended', 'absent', 'cancelled')),
    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE(group_session_id, enrollment_id)
);

-- Appointments table
CREATE TABLE appointments (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(id) ON DELETE SET NULL,
    tutor_id INTEGER REFERENCES tutors(id) ON DELETE CASCADE,
    tutor_service_id INTEGER REFERENCES tutor_services(id) ON DELETE CASCADE,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation')),
    location VARCHAR(200),
    notes TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB,
    recurrence_end_date DATE,
    created_by INTEGER REFERENCES users(id),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoices table
CREATE TABLE invoices (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    payment_date TIMESTAMP,
    payment_method VARCHAR(50),
    stripe_payment_intent_id VARCHAR(255),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoice items
CREATE TABLE invoice_items (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER REFERENCES invoices(id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointments(id) ON DELETE SET NULL,
    description TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscription plans
CREATE TABLE subscription_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    max_hours INTEGER NOT NULL,
    validity_days INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client subscriptions
CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES subscription_plans(id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    hours_remaining DECIMAL(5,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    auto_renew BOOLEAN DEFAULT FALSE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscription usage tracking
CREATE TABLE subscription_usage (
    id SERIAL PRIMARY KEY,
    subscription_id INTEGER REFERENCES subscriptions(id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointments(id) ON DELETE CASCADE,
    hours_used DECIMAL(5,2) NOT NULL,
    usage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(subscription_id, appointment_id)
);

-- Tutor payments
CREATE TABLE tutor_payments (
    id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(id) ON DELETE CASCADE,
    payment_period_start DATE NOT NULL,
    payment_period_end DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'ready', 'paid', 'failed')),
    payment_date TIMESTAMP,
    stripe_payout_id VARCHAR(255),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client consent tracking
CREATE TABLE client_consent (
    id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(id) ON DELETE CASCADE,
    consent_type VARCHAR(100) NOT NULL,
    consent_given BOOLEAN NOT NULL,
    consent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    notes TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_tutors_user_id ON tutors(user_id);
CREATE INDEX idx_appointments_client_id ON appointments(client_id);
CREATE INDEX idx_appointments_tutor_id ON appointments(tutor_id);
CREATE INDEX idx_appointments_start_time ON appointments(start_time);
CREATE INDEX idx_appointments_status ON appointments(status);
CREATE INDEX idx_invoices_client_id ON invoices(client_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_group_sessions_program_id ON group_sessions(program_id);
CREATE INDEX idx_group_sessions_date ON group_sessions(session_date);
CREATE INDEX idx_enrollments_client_id ON enrollments(client_id);
CREATE INDEX idx_enrollments_program_id ON enrollments(program_id);

-- Add triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_modification_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modification_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to relevant tables
CREATE TRIGGER update_users_modification_date BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_clients_modification_date BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_tutors_modification_date BEFORE UPDATE ON tutors FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_appointments_modification_date BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_invoices_modification_date BEFORE UPDATE ON invoices FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_programs_modification_date BEFORE UPDATE ON programs FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_group_sessions_modification_date BEFORE UPDATE ON group_sessions FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_enrollments_modification_date BEFORE UPDATE ON enrollments FOR EACH ROW EXECUTE FUNCTION update_modification_date();

-- Insert default data
INSERT INTO subscription_plans (name, description, price, max_hours, validity_days) VALUES
('Basic Plan', '10 hours of tutoring valid for 30 days', 299.99, 10, 30),
('Standard Plan', '20 hours of tutoring valid for 60 days', 549.99, 20, 60),
('Premium Plan', '40 hours of tutoring valid for 90 days', 999.99, 40, 90);

-- Create TECFÉE program
INSERT INTO programs (code, name, description, total_sessions, session_duration, min_participants, max_participants) VALUES
('TECFEE', 'PARCOURS COMPLET LINGUISTIQUE TECFÉE', 'Préparation complète au test TECFÉE en 12 modules structurés', 12, 60, 4, 10);

-- Get the TECFÉE program ID for module insertion
DO $$
DECLARE
    tecfee_program_id INTEGER;
BEGIN
    SELECT id INTO tecfee_program_id FROM programs WHERE code = 'TECFEE';

    -- Insert TECFÉE modules
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives) VALUES
    (tecfee_program_id, 'Manipulation Syntaxiques et Classe de Mots', 'Manipulation des structures syntaxiques et identification des classes de mots', 1, 60, 'Maîtriser les manipulations syntaxiques et identifier correctement les classes de mots'),
    (tecfee_program_id, 'Connecteurs', 'Utilisation appropriée des connecteurs logiques et temporels', 2, 60, 'Utiliser correctement les connecteurs pour structurer le discours'),
    (tecfee_program_id, 'Pléonasmes', 'Identification et correction des pléonasmes', 3, 60, 'Reconnaître et éviter les pléonasmes dans l''expression écrite'),
    (tecfee_program_id, 'Affixes et Expressions (Partie 1)', 'Formation des mots par affixation - Partie 1', 4, 60, 'Comprendre la formation des mots par préfixes et suffixes'),
    (tecfee_program_id, 'Affixes et Expressions (Partie 2)', 'Formation des mots par affixation - Partie 2', 5, 60, 'Maîtriser l''utilisation des expressions figées'),
    (tecfee_program_id, 'Syntaxe (Partie 1)', 'Analyse syntaxique et construction de phrases - Partie 1', 6, 60, 'Analyser la structure syntaxique des phrases simples'),
    (tecfee_program_id, 'Syntaxe (Partie 2)', 'Analyse syntaxique et construction de phrases - Partie 2', 7, 60, 'Construire des phrases complexes syntaxiquement correctes'),
    (tecfee_program_id, 'Orthographe (Partie 1)', 'Règles d''orthographe et application - Partie 1', 8, 60, 'Appliquer les règles d''orthographe de base'),
    (tecfee_program_id, 'Orthographe (Partie 2)', 'Règles d''orthographe et application - Partie 2', 9, 60, 'Maîtriser l''orthographe des mots complexes'),
    (tecfee_program_id, 'Ponctuation', 'Utilisation correcte de la ponctuation', 10, 60, 'Utiliser la ponctuation pour clarifier le sens'),
    (tecfee_program_id, 'Révision Générale', 'Révision de tous les concepts abordés', 11, 60, 'Consolider les apprentissages et identifier les points à améliorer'),
    (tecfee_program_id, 'Simulation Examen', 'Examen blanc complet dans les conditions réelles du TECFÉE', 12, 120, 'Se familiariser avec le format d''examen et évaluer sa préparation');

    -- Insert TECFÉE pricing options
    INSERT INTO program_pricing (program_id, pricing_type, price, description) VALUES
    (tecfee_program_id, 'per_session', 44.99, 'Paiement par module individuel'),
    (tecfee_program_id, 'full_package', 399.00, 'Package complet - 12 modules (économisez 140,88$!)');

END $$;

-- This schema represents the current state of the TutorAide application
-- Generated by Claude Sonnet 4
