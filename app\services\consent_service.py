# app/services/consent_service.py
from datetime import datetime
from app.extensions import db
from app.models.client_consent import ClientConsent
from app.models.client import Client

class ConsentService:
    """Service for managing client consents."""

    @staticmethod
    def create_or_update_client_consent(client_id, tos_version, mandatory=True, optional=False, ip=None):
        """Create or update a client consent record."""
        # Check if client exists
        client = Client.query.get(client_id)
        if not client:
            return None, "Client not found"

        # Check if client is suspended
        if client.is_suspended:
            return None, "Account suspended"

        # Create or update consent
        consent = ClientConsent.create_or_update(
            client_id=client_id,
            tos_version=tos_version,
            mandatory=mandatory,
            optional=optional,
            ip=ip
        )

        return consent, None

    @staticmethod
    def update_client_optional_consent(client_id, optional):
        """Update only the optional consent for a client."""
        consent = ClientConsent.query.filter_by(client_id=client_id).first()

        if not consent:
            return None, "No consent record found for this client"

        # Check if client is suspended
        client = Client.query.get(client_id)
        if client and client.is_suspended:
            return None, "Account suspended"

        # Update optional consent
        consent.optional_consent = optional
        consent.optional_updated_at = datetime.utcnow()
        db.session.commit()

        return consent, None

    @staticmethod
    def has_client_accepted_mandatory_terms(client_id):
        """Check if a client has accepted the mandatory terms."""
        consent = ClientConsent.query.filter_by(client_id=client_id).first()
        return consent is not None and consent.mandatory_accepted_at is not None

    @staticmethod
    def get_client_consent(client_id):
        """Get the consent record for a client."""
        return ClientConsent.query.filter_by(client_id=client_id).first()


