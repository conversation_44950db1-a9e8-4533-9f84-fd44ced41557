<!-- app/templates/auth/login.html -->
{% extends "base.html" %}

{% block title %}Login - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Login</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>

                <!-- Google Sign-In (show if configured OR in test mode) -->
                {% if (config.GOOGLE_CLIENT_ID and config.GOOGLE_CLIENT_SECRET) or request.args.get('show_google') %}
                <!-- Divider -->
                <div class="text-center my-3">
                    <div class="d-flex align-items-center">
                        <hr class="flex-grow-1">
                        <span class="px-3 text-muted">OU</span>
                        <hr class="flex-grow-1">
                    </div>
                </div>

                <!-- Google Sign-In -->
                <div class="d-grid">
                    <a href="{{ url_for('auth.google_login') }}" class="btn btn-outline-danger">
                        <i class="fab fa-google"></i>
                        Se connecter avec Google
                    </a>
                </div>
                {% endif %}
            </div>
            <div class="card-footer text-center">
                <p class="mb-2">Forgot your password? <a href="{{ url_for('auth.reset_password_request') }}">Reset it here</a></p>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-0 small text-muted">
                            Nouveau client TECFÉE?<br>
                            <a href="{{ url_for('public.tecfee_enrollment') }}">Inscrivez-vous ici</a>
                        </p>
                    </div>
                    {% if config.GOOGLE_CLIENT_ID and config.GOOGLE_CLIENT_SECRET %}
                    <div class="col-md-6">
                        <p class="mb-0 small text-muted">
                            Nouveau utilisateur?<br>
                            <a href="{{ url_for('auth.google_signup') }}">Inscription avec Google</a>
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}