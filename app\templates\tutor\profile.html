<!-- app/templates/tutor/profile.html -->
{% extends "base.html" %}

{% block title %}My Profile - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">My Profile</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Profile Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('tutor.profile') }}">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-control" value="{{ current_user.email }}" disabled>
                        <div class="form-text">To change your email address, please contact administration.</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Name</label>
                        <input type="text" class="form-control" value="{{ tutor.first_name }} {{ tutor.last_name }}" disabled>
                        <div class="form-text">To change your name, please contact administration.</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Address Information -->
                    <h5 class="mb-3 mt-4">Address Information</h5>

                    <div class="mb-3">
                        <label for="street_address" class="form-label">Street Address</label>
                        {{ form.street_address(class="form-control" + (" is-invalid" if form.street_address.errors else "")) }}
                        {% if form.street_address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.street_address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City</label>
                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="province" class="form-label">Province/State</label>
                            {{ form.province(class="form-control" + (" is-invalid" if form.province.errors else "")) }}
                            {% if form.province.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.province.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="zip_code" class="form-label">Zip/Postal Code</label>
                            {{ form.zip_code(class="form-control" + (" is-invalid" if form.zip_code.errors else "")) }}
                            {% if form.zip_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zip_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">Country</label>
                            {{ form.country(class="form-control" + (" is-invalid" if form.country.errors else "")) }}
                            {% if form.country.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.country.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <h5 class="mb-3 mt-4">Personal Information</h5>

                    <div class="mb-3">
                        <label for="birthdate" class="form-label">Birthdate</label>
                        {{ form.birthdate(class="form-control", type="date") }}
                        {% if form.birthdate.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.birthdate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Banking Information -->
                    <h5 class="mb-3 mt-4">Banking Information (Encrypted)</h5>

                    <div class="mb-3">
                        <label for="bank_transit_number" class="form-label">Bank Transit Number</label>
                        {{ form.bank_transit_number(class="form-control" + (" is-invalid" if form.bank_transit_number.errors else "")) }}
                        {% if form.bank_transit_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_transit_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Your bank's transit number (5 digits).</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_institution_number" class="form-label">Bank Institution Number</label>
                        {{ form.bank_institution_number(class="form-control" + (" is-invalid" if form.bank_institution_number.errors else "")) }}
                        {% if form.bank_institution_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_institution_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Your bank's institution number (3 digits).</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_account_number" class="form-label">Bank Account Number</label>
                        {{ form.bank_account_number(class="form-control" + (" is-invalid" if form.bank_account_number.errors else "")) }}
                        {% if form.bank_account_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_account_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Your bank account number (7-12 digits).</div>
                    </div>

                    <!-- Professional Information -->
                    <h5 class="mb-3 mt-4">Professional Information</h5>

                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        {{ form.bio(class="form-control", rows=5) }}
                        <div class="form-text">A brief biography that will be visible to parents and students.</div>
                    </div>

                    <div class="mb-3">
                        <label for="qualifications" class="form-label">Qualifications</label>
                        {{ form.qualifications(class="form-control", rows=3) }}
                        <div class="form-text">Your education, certifications, and relevant experience.</div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Change Password</h5>
                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Leave blank to keep your current password.</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                        {% if form.confirm_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}