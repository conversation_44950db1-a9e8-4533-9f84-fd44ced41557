<!-- app/templates/tutor/client_detail.html -->
{% extends "base.html" %}

{% block title %}Client Details - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Client Details</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('tutor.client_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Clients
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <!-- Client Information Card -->
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Client Information</h5>
            </div>
            <div class="card-body">
                <h4>{{ client.first_name }} {{ client.last_name }}</h4>

                <div class="mb-3">
                    <h6 class="text-muted mb-2">Client Type</h6>
                    <p>{{ client.client_type | title }}</p>
                </div>

                {% if client.phone %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Phone</h6>
                        <p>{{ client.phone }}</p>
                    </div>
                {% endif %}

                {% if client.email %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">Email</h6>
                        <p>{{ client.email }}</p>
                    </div>
                {% endif %}

                {% if client.client_type == 'individual' %}
                    {% if client.date_of_birth %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Age</h6>
                            <p>{{ client.age }} years</p>
                        </div>
                    {% endif %}

                    {% if client.notes %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Notes</h6>
                            <p>{{ client.notes }}</p>
                        </div>
                    {% endif %}
                {% endif %}

                {% if client.client_type == 'institutional' %}
                    {% if client.institution_name %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Institution</h6>
                            <p>{{ client.institution_name }}</p>
                        </div>
                    {% endif %}

                    {% if client.contact_person %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Contact Person</h6>
                            <p>{{ client.contact_person }}</p>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
        </div>

        <!-- Program Enrollments -->
        {% if enrollments %}
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Program Enrollments</h5>
            </div>
            <div class="card-body">
                {% for enrollment in enrollments %}
                    <div class="mb-3">
                        <h6>{{ enrollment.program.name }}</h6>
                        <p class="text-muted">Status: {{ enrollment.status | title }}</p>
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-8">
        <!-- Upcoming Appointments -->
        <div class="card shadow mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Upcoming Appointments</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Service</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {{ appointment.start_time.strftime('%H:%M') }} -
                                            {{ appointment.end_time.strftime('%H:%M') }}
                                        </td>
                                        <td>
                                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                                {{ appointment.tutor_service.service.name }}
                                            {% else %}
                                                Unknown Service
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'primary' if appointment.status == 'scheduled' else 'warning' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No upcoming appointments with this client.</p>
                {% endif %}
            </div>
        </div>

        <!-- Past Appointments -->
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">Past Appointments</h5>
            </div>
            <div class="card-body">
                {% if past_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Service</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments[:10] %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {{ appointment.start_time.strftime('%H:%M') }} -
                                            {{ appointment.end_time.strftime('%H:%M') }}
                                        </td>
                                        <td>
                                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                                {{ appointment.tutor_service.service.name }}
                                            {% else %}
                                                Unknown Service
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'danger' if appointment.status == 'no-show' else 'warning' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if past_appointments|length > 10 %}
                        <p class="text-muted text-center mt-3">Showing last 10 appointments</p>
                    {% endif %}
                {% else %}
                    <p class="text-muted text-center my-4">No past appointments with this client.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
