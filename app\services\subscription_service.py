# app/services/subscription_service.py

from app.extensions import db
from app.models.subscription import SubscriptionPlan, Subscription, SubscriptionUsage
from app.models.appointment import Appointment
from app.models.client import Client
from datetime import datetime, timedelta

class SubscriptionService:

    @staticmethod
    def create_subscription(client_id, plan_id, start_date=None):
        """Create a new subscription for a client."""
        # Get the plan
        plan = SubscriptionPlan.query.get_or_404(plan_id)
        client = Client.query.get_or_404(client_id)

        if not start_date:
            start_date = datetime.now().date()

        # Calculate end date based on plan duration
        end_date = start_date + timedelta(days=30*plan.duration_months)

        # Create subscription
        subscription = Subscription(
            client_id=client_id,
            plan_id=plan_id,
            start_date=start_date,
            end_date=end_date,
            hours_used=0,
            status='active'
        )
        db.session.add(subscription)
        db.session.commit()

        # Generate an invoice for the subscription (advance payment)
        from app.services.invoice_service import InvoiceService
        InvoiceService.generate_invoice_for_subscription(subscription.id)

        return subscription

    @staticmethod
    def record_usage(appointment_id):
        """Record usage from a completed appointment against a subscription."""
        appointment = Appointment.query.get_or_404(appointment_id)

        # Only record usage for completed appointments linked to a subscription
        if not appointment.is_subscription_based or appointment.status != 'completed':
            return False

        subscription = Subscription.query.get(appointment.subscription_id)
        if not subscription or not subscription.is_active:
            return False

        # Calculate hours used
        hours_used = appointment.duration_minutes / 60

        # Check if there are enough hours remaining
        if subscription.hours_remaining < hours_used:
            return False

        # Update subscription hours used
        subscription.hours_used += hours_used

        # Create usage record
        usage = SubscriptionUsage(
            subscription_id=subscription.id,
            appointment_id=appointment.id,
            hours_used=hours_used
        )

        db.session.add(usage)
        db.session.commit()

        return True

    @staticmethod
    def check_subscription_available(subscription_id, duration_minutes):
        """Check if a subscription has enough hours available for an appointment."""
        subscription = Subscription.query.get_or_404(subscription_id)

        # Convert minutes to hours
        hours_needed = duration_minutes / 60

        # Check if active and has enough hours remaining
        return subscription.is_active and subscription.hours_remaining >= hours_needed

    @staticmethod
    def get_active_subscriptions_for_client(client_id):
        """Get all active subscriptions for a client."""
        today = datetime.now().date()
        return Subscription.query.filter(
            Subscription.client_id == client_id,
            Subscription.status == 'active',
            Subscription.start_date <= today,
            Subscription.end_date >= today
        ).all()

    @staticmethod
    def get_active_subscriptions_for_dependant(dependant_id):
        """Get all active subscriptions available to a dependant through their client relationships."""
        from app.models.dependant import DependantRelationship

        # Find all clients related to this dependant
        relationships = DependantRelationship.query.filter_by(dependant_id=dependant_id).all()

        if not relationships:
            return []

        # Get all client IDs
        client_ids = [rel.client_id for rel in relationships]

        # Get active subscriptions for all related clients
        today = datetime.now().date()
        subscriptions = Subscription.query.filter(
            Subscription.client_id.in_(client_ids),
            Subscription.status == 'active',
            Subscription.start_date <= today,
            Subscription.end_date >= today
        ).all()

        return subscriptions

    @staticmethod
    def get_active_subscriptions_for_parent(parent_id):
        """Legacy method - Get all active subscriptions for a parent (now client)."""
        # This method is kept for backward compatibility
        return SubscriptionService.get_active_subscriptions_for_client(parent_id)

    @staticmethod
    def cancel_subscription(subscription_id):
        """Cancel a subscription."""
        subscription = Subscription.query.get_or_404(subscription_id)
        subscription.status = 'cancelled'
        db.session.commit()

        return subscription

    @staticmethod
    def update_all_subscription_usage():
        """
        Retroactively update subscription usage for all completed appointments
        that haven't been recorded yet.

        This is useful for fixing subscription usage that wasn't properly recorded.
        """
        # Find all completed subscription-based appointments
        completed_appointments = Appointment.query.filter(
            Appointment.status == 'completed',
            Appointment.is_subscription_based == True,
            Appointment.subscription_id != None
        ).all()

        # Find appointments that don't have usage records
        appointments_without_usage = []
        for appointment in completed_appointments:
            # Check if there's already a usage record for this appointment
            usage = SubscriptionUsage.query.filter_by(appointment_id=appointment.id).first()
            if not usage:
                appointments_without_usage.append(appointment)

        # Record usage for each appointment
        count = 0
        for appointment in appointments_without_usage:
            if SubscriptionService.record_usage(appointment.id):
                count += 1

        return count