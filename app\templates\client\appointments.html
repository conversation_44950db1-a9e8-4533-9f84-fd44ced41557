<!-- app/templates/client/appointments.html -->
{% extends "base.html" %}

{% block title %}{{ t('appointments.your_appointments') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('appointments.your_appointments') }}</h2>
        <p class="text-muted">{{ t('appointments.view_and_manage') }}</p>
    </div>
</div>

<div class="card shadow">
    <div class="card-header bg-light">
        <ul class="nav nav-tabs card-header-tabs" id="appointmentsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab" aria-controls="upcoming" aria-selected="true">
                    {{ t('appointments.upcoming') }}
                    {% if upcoming_appointments %}
                        <span class="badge bg-primary ms-1">{{ upcoming_appointments|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="past-tab" data-bs-toggle="tab" data-bs-target="#past" type="button" role="tab" aria-controls="past" aria-selected="false">
                    {{ t('appointments.past') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="false">
                    {{ t('appointments.all') }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="appointmentsTabsContent">
            <!-- Upcoming Appointments Tab -->
            <div class="tab-pane fade show active" id="upcoming" role="tabpanel" aria-labelledby="upcoming-tab">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('appointments.date') }}</th>
                                    <th>{{ t('appointments.time') }}</th>
                                    <th>{{ t('appointments.client') }}</th>
                                    <th>{{ t('appointments.tutor') }}</th>
                                    <th>{{ t('appointments.service') }}</th>
                                    <th>{{ t('appointments.status') }}</th>
                                    <th>{{ t('appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                        <td>
                                            {% if appointment.client %}
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                        <td>{{ appointment.tutor_service.service.name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if appointment.status == 'scheduled' %}
                                                <a href="{{ url_for('client.cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('{{ t('appointments.confirm_cancel') }}');">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_upcoming_appointments') }}</p>
                {% endif %}
            </div>

            <!-- Past Appointments Tab -->
            <div class="tab-pane fade" id="past" role="tabpanel" aria-labelledby="past-tab">
                {% if past_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('appointments.date') }}</th>
                                    <th>{{ t('appointments.time') }}</th>
                                    <th>{{ t('appointments.client') }}</th>
                                    <th>{{ t('appointments.tutor') }}</th>
                                    <th>{{ t('appointments.service') }}</th>
                                    <th>{{ t('appointments.status') }}</th>
                                    <th>{{ t('appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                        <td>
                                            {% if appointment.client %}
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                        <td>{{ appointment.tutor_service.service.name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_past_appointments') }}</p>
                {% endif %}
            </div>

            <!-- All Appointments Tab -->
            <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                {% if all_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('appointments.date') }}</th>
                                    <th>{{ t('appointments.time') }}</th>
                                    <th>{{ t('appointments.client') }}</th>
                                    <th>{{ t('appointments.tutor') }}</th>
                                    <th>{{ t('appointments.service') }}</th>
                                    <th>{{ t('appointments.status') }}</th>
                                    <th>{{ t('appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in all_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                        <td>
                                            {% if appointment.client %}
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                        <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                        <td>{{ appointment.tutor_service.service.name }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                {{ appointment.status | capitalize }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if appointment.status == 'scheduled' and appointment.start_time > now %}
                                                <a href="{{ url_for('client.cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('{{ t('appointments.confirm_cancel') }}');">
                                                    <i class="fas fa-times"></i>
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_appointments') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
