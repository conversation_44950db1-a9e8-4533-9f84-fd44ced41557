# app/views/tutor.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.models.user import User
from app.models.tutor import Tutor
from app.models.appointment import Appointment
from app.models.client import Client
from app.models.service import Service, TutorService
from app.models.tutor_payment import TutorPayment
from app.models.tutor_availability import TutorAvailability
from app.models.time_off import TimeOff
from app.models.notification import Notification
from app.forms.tutor_forms import TutorProfileForm, AppointmentNotesForm, TimeOffRequestForm
from app.forms.availability_forms import TutorAvailabilityForm
from datetime import datetime, timedelta
from sqlalchemy import or_, and_

tutor = Blueprint('tutor', __name__)

# Require tutor role for all routes in this blueprint
@tutor.before_request
def check_tutor():
    if not current_user.is_authenticated or current_user.role != 'tutor':
        flash('You must be a tutor to access this area.', 'danger')
        return redirect(url_for('auth.login'))

@tutor.route('/')
@login_required
def dashboard():
    # Get the tutor profile
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get today's date
    today = datetime.now().date()

    # Get today's appointments
    from sqlalchemy.orm import joinedload
    
    todays_appointments = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.tutor_id == tutor.id,
        Appointment.start_time >= datetime.combine(today, datetime.min.time()),
        Appointment.start_time < datetime.combine(today + timedelta(days=1), datetime.min.time()),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).all()

    # Get upcoming appointments (excluding today)
    upcoming_appointments = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.tutor_id == tutor.id,
        Appointment.start_time >= datetime.combine(today + timedelta(days=1), datetime.min.time()),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).limit(10).all()

    # Get recent past appointments
    past_appointments = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.tutor_id == tutor.id,
        Appointment.start_time < datetime.combine(today, datetime.min.time()),
        Appointment.status.in_(['completed', 'no-show'])
    ).order_by(Appointment.start_time.desc()).limit(10).all()

    return render_template('tutor/dashboard.html',
                           tutor=tutor,
                           todays_appointments=todays_appointments,
                           upcoming_appointments=upcoming_appointments,
                           past_appointments=past_appointments)

@tutor.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    form = TutorProfileForm(obj=tutor)
    if form.validate_on_submit():
        # Basic information
        tutor.phone = form.phone.data
        tutor.bio = form.bio.data
        tutor.qualifications = form.qualifications.data

        # Address information
        tutor.street_address = form.street_address.data
        tutor.city = form.city.data
        tutor.province = form.province.data
        tutor.zip_code = form.zip_code.data
        tutor.country = form.country.data

        # Personal information
        tutor.birthdate = form.birthdate.data

        # Banking information (encrypted) - always update, even if empty
        tutor.set_bank_transit_number(form.bank_transit_number.data)
        tutor.set_bank_institution_number(form.bank_institution_number.data)
        tutor.set_bank_account_number(form.bank_account_number.data)

        # Password change
        if form.password.data:
            current_user.set_password(form.password.data)

        db.session.commit()
        flash('Your profile has been updated.', 'success')
        return redirect(url_for('tutor.profile'))

    # Populate encrypted banking fields if they exist and can be decrypted
    # Only populate if decryption succeeds (returns non-None value)
    transit = tutor.get_bank_transit_number()
    if transit:
        form.bank_transit_number.data = transit

    institution = tutor.get_bank_institution_number()
    if institution:
        form.bank_institution_number.data = institution

    account = tutor.get_bank_account_number()
    if account:
        form.bank_account_number.data = account

    return render_template('tutor/profile.html', form=form, tutor=tutor)

@tutor.route('/schedule')
@login_required
def schedule():
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get clients this tutor has worked with
    client_ids = db.session.query(Appointment.client_id).filter_by(tutor_id=tutor.id).distinct().all()
    client_ids = [id[0] for id in client_ids]
    clients = Client.query.filter(Client.id.in_(client_ids)).all()

    # Default view is by week, starting with current date
    view_type = request.args.get('view', 'week')
    start_date_str = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    except ValueError:
        start_date = datetime.now()

    # Get end date based on view type
    if view_type == 'day':
        end_date = start_date + timedelta(days=1)
    elif view_type == 'week':
        # Start from Monday of the current week
        monday = start_date - timedelta(days=start_date.weekday())
        start_date = monday
        end_date = start_date + timedelta(days=7)
    elif view_type == 'month':
        # Start from the 1st of the month
        start_date = start_date.replace(day=1)
        # Go to the 1st of next month
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year+1, month=1)
        else:
            end_date = start_date.replace(month=start_date.month+1)

    # Filter by client if provided
    client_id = request.args.get('client_id', type=int)

    # Get appointments for the selected period with eager loading of relationships
    from sqlalchemy.orm import joinedload
    query = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.tutor_id == tutor.id,
        Appointment.start_time >= start_date,
        Appointment.start_time < end_date
    )

    if client_id:
        query = query.filter(Appointment.client_id == client_id)

    appointments = query.all()

    # Parse appointments into a format suitable for the calendar
    calendar_data = prepare_calendar_data(appointments, start_date, end_date, view_type)

    # Determine navigation dates
    prev_date, next_date = get_navigation_dates(start_date, view_type)

    return render_template('tutor/schedule.html',
                          view_type=view_type,
                          start_date=start_date,
                          calendar_data=calendar_data,
                          prev_date=prev_date,
                          next_date=next_date,
                          clients=clients,
                          timedelta=timedelta,
                          now=datetime.now())

@tutor.route('/appointments/<int:id>')
@login_required
def view_appointment(id):
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the appointment, ensuring it belongs to this tutor
    appointment = Appointment.query.filter_by(id=id, tutor_id=tutor.id).first_or_404()

    return render_template('tutor/appointment_detail.html',
                          appointment=appointment,
                          Appointment=Appointment)  # Pass the Appointment model to the template

@tutor.route('/appointments/<int:id>/update', methods=['GET', 'POST'])
@login_required
def update_appointment(id):
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the appointment, ensuring it belongs to this tutor
    appointment = Appointment.query.filter_by(id=id, tutor_id=tutor.id).first_or_404()

    form = AppointmentNotesForm(obj=appointment)
    if form.validate_on_submit():
        appointment.status = form.status.data
        appointment.notes = form.notes.data
        db.session.commit()

        flash('Appointment updated successfully.', 'success')
        return redirect(url_for('tutor.view_appointment', id=appointment.id))

    return render_template('tutor/appointment_update.html',
                          form=form,
                          appointment=appointment)

@tutor.route('/appointments/<int:id>/take-attendance', methods=['POST'])
@login_required
def take_attendance(id):
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the appointment, ensuring it belongs to this tutor
    appointment = Appointment.query.filter_by(id=id, tutor_id=tutor.id).first_or_404()

    if appointment.status == 'scheduled':
        try:
            # Try the full status first, fallback to shorter version if database constraint fails
            try:
                appointment.status = 'awaiting_confirmation'
                db.session.commit()
                flash('Attendance marked for confirmation.', 'success')
            except Exception as db_error:
                # If the full status fails due to length/constraint, try a shorter version
                db.session.rollback()
                appointment.status = 'awaiting_confirm'  # 15 chars, fits in VARCHAR(20)
                db.session.commit()
                flash('Attendance marked for confirmation.', 'success')
                current_app.logger.warning(f"Used shortened status for appointment {id} due to database constraint: {str(db_error)}")
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error updating appointment {id} attendance: {str(e)}")
            flash('Error updating appointment. Please try again.', 'danger')
    else:
        flash('Cannot take attendance for this appointment.', 'warning')

    return redirect(url_for('tutor.view_appointment', id=appointment.id))

@tutor.route('/appointments/<int:id>/confirm-attendance', methods=['POST'])
@login_required
def confirm_attendance(id):
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the appointment, ensuring it belongs to this tutor
    appointment = Appointment.query.filter_by(id=id, tutor_id=tutor.id).first_or_404()
    confirmation = request.form.get('confirmation')

    # Handle both full and shortened status values
    if appointment.status not in ['awaiting_confirmation', 'awaiting_confirm']:
        flash('This appointment is not awaiting confirmation.', 'warning')
        return redirect(url_for('tutor.view_appointment', id=appointment.id))

    if confirmation == 'attended':
        appointment.status = 'completed'
        flash('Appointment marked as completed.', 'success')

        # Record subscription usage if this is a subscription-based appointment
        if appointment.is_subscription_based and appointment.subscription_id:
            from app.services.subscription_service import SubscriptionService
            SubscriptionService.record_usage(appointment.id)

    elif confirmation == 'cancelled_with_notice':
        appointment.status = 'cancelled'
        appointment.notes = (appointment.notes or '') + '\nCancelled with notice.'
        flash('Appointment marked as cancelled with notice.', 'success')
    elif confirmation == 'cancelled_without_notice':
        appointment.status = 'no-show'
        appointment.notes = (appointment.notes or '') + '\nCancelled without notice (no-show).'
        flash('Appointment marked as no-show.', 'success')
    else:
        flash('Invalid confirmation status.', 'danger')
        return redirect(url_for('tutor.view_appointment', id=appointment.id))

    db.session.commit()
    return redirect(url_for('tutor.view_appointment', id=appointment.id))

@tutor.route('/appointments/<int:id>/confirm-attendance-email/<token>/<confirmation>')
def confirm_attendance_email(id, token, confirmation):
    # Verify the token
    from itsdangerous import URLSafeTimedSerializer
    from flask import current_app

    s = URLSafeTimedSerializer(current_app.config['SECRET_KEY'])
    try:
        tutor_id = s.loads(token, salt='attendance-confirmation', max_age=86400)  # 24 hours
    except:
        flash('Invalid or expired token.', 'danger')
        return redirect(url_for('auth.login'))

    # Get the appointment
    appointment = Appointment.query.get_or_404(id)

    # Verify the appointment belongs to this tutor
    if appointment.tutor_id != tutor_id:
        flash('Unauthorized access.', 'danger')
        return redirect(url_for('auth.login'))

    # Process the confirmation - handle both full and shortened status values
    if appointment.status not in ['awaiting_confirmation', 'awaiting_confirm']:
        flash('This appointment is not awaiting confirmation.', 'warning')
    elif confirmation == 'attended':
        appointment.status = 'completed'

        # Record subscription usage if this is a subscription-based appointment
        if appointment.is_subscription_based and appointment.subscription_id:
            from app.services.subscription_service import SubscriptionService
            SubscriptionService.record_usage(appointment.id)

        db.session.commit()
        flash('Appointment marked as completed.', 'success')
    elif confirmation == 'cancelled_with_notice':
        appointment.status = 'cancelled'
        appointment.notes = (appointment.notes or '') + '\nCancelled with notice.'
        db.session.commit()
        flash('Appointment marked as cancelled with notice.', 'success')
    elif confirmation == 'cancelled_without_notice':
        appointment.status = 'no-show'
        appointment.notes = (appointment.notes or '') + '\nCancelled without notice (no-show).'
        db.session.commit()
        flash('Appointment marked as no-show.', 'success')
    else:
        flash('Invalid confirmation status.', 'danger')

    # Redirect to login if not logged in
    if not current_user.is_authenticated:
        return redirect(url_for('auth.login'))

    return redirect(url_for('tutor.view_appointment', id=appointment.id))

@tutor.route('/clients')
@login_required
def client_list():
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get unique clients who have had appointments with this tutor
    client_ids = db.session.query(Appointment.client_id).filter_by(tutor_id=tutor.id).distinct().all()
    client_ids = [id[0] for id in client_ids]

    # Filter by search if provided
    search = request.args.get('search', '')

    query = Client.query.filter(Client.id.in_(client_ids))

    if search:
        query = query.filter(or_(
            Client.first_name.ilike(f'%{search}%'),
            Client.last_name.ilike(f'%{search}%')
        ))

    clients = query.all()

    # Get counts for upcoming and past appointments
    upcoming_appointments = {}
    past_counts = {}

    # Get all programs for filtering
    programs = []
    try:
        from app.models.program import Program
        programs = Program.query.all()
    except:
        pass

    # Get upcoming appointments for each client
    for client in clients:
        # Upcoming appointments
        upcoming = Appointment.query.filter(
            Appointment.tutor_id == tutor.id,
            Appointment.client_id == client.id,
            Appointment.start_time >= datetime.now(),
            Appointment.status == 'scheduled'
        ).order_by(Appointment.start_time).all()

        upcoming_appointments[client.id] = upcoming

        # Past count
        past_counts[client.id] = Appointment.query.filter(
            Appointment.tutor_id == tutor.id,
            Appointment.client_id == client.id,
            Appointment.start_time < datetime.now()
        ).count()

    return render_template('tutor/client_list.html',
                          clients=clients,
                          upcoming_appointments=upcoming_appointments,
                          past_counts=past_counts,
                          programs=programs)

# Add to app/views/tutor.py

@tutor.route('/availability', methods=['GET', 'POST'])
@login_required
def availability():
    """Manage tutor availability."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get existing availability slots
    availabilities = TutorAvailability.query.filter_by(tutor_id=tutor.id).order_by(
        TutorAvailability.day_of_week, TutorAvailability.start_time
    ).all()

    # Handle form submission for new availability
    form = TutorAvailabilityForm()

    # Debug information
    if request.method == 'POST':
        print(f"Form submitted. POST data: {request.form}")
        print(f"Form valid: {form.validate()}")
        print(f"Form errors: {form.errors}")

        # Try to process the form even if validate_on_submit fails
        try:
            day_of_week = int(request.form.get('day_of_week', 0))
            all_day = 'all_day' in request.form
            is_active = 'is_active' in request.form

            # If all day is checked, set fixed times (8:00 AM to 9:00 PM)
            if all_day:
                start_time = "08:00"
                end_time = "21:00"
            else:
                start_time = request.form.get('start_time')
                end_time = request.form.get('end_time')

            if start_time and end_time:
                # Create new availability slot
                availability = TutorAvailability(
                    tutor_id=tutor.id,
                    day_of_week=day_of_week,
                    start_time=start_time,
                    end_time=end_time,
                    is_active=is_active
                )

                db.session.add(availability)
                db.session.commit()

                flash('Availability saved successfully.', 'success')
                return redirect(url_for('tutor.availability'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error processing form directly: {str(e)}', 'danger')

        flash(f'Form submitted. Valid: {form.validate()}', 'info')
        if not form.validate():
            for field, errors in form.errors.items():
                for error in errors:
                    flash(f'Error in {field}: {error}', 'danger')

    if form.validate_on_submit():
        # Check if we're editing an existing slot
        if form.id.data:
            availability = TutorAvailability.query.get_or_404(form.id.data)
            if availability.tutor_id != tutor.id:
                flash('You do not have permission to edit this availability.', 'danger')
                return redirect(url_for('tutor.availability'))
        else:
            # Create new availability slot
            availability = TutorAvailability(tutor_id=tutor.id)
            flash('Creating new availability slot', 'info')

        # Update availability data
        try:
            availability.day_of_week = form.day_of_week.data
            availability.start_time = form.start_time.data
            availability.end_time = form.end_time.data
            availability.is_active = form.is_active.data

            db.session.add(availability)
            db.session.commit()
            flash('Availability saved successfully.', 'success')
        except Exception as e:
            db.session.rollback()
            flash(f'Error saving availability: {str(e)}', 'danger')

        return redirect(url_for('tutor.availability'))

    return render_template('tutor/availability.html',
                          availabilities=availabilities,
                          form=form,
                          tutor=tutor)

@tutor.route('/availability/<int:id>/edit', methods=['GET'])
@login_required
def edit_availability(id):
    """Edit an existing availability slot."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()
    availability = TutorAvailability.query.get_or_404(id)

    # Ensure the availability belongs to this tutor
    if availability.tutor_id != tutor.id:
        flash('You do not have permission to edit this availability.', 'danger')
        return redirect(url_for('tutor.availability'))

    # Populate form with existing data
    form = TutorAvailabilityForm(obj=availability)

    return render_template('tutor/availability_form.html',
                          form=form,
                          availability=availability,
                          tutor=tutor)

@tutor.route('/availability/<int:id>/delete', methods=['POST'])
@login_required
def delete_availability(id):
    """Delete an availability slot."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()
    availability = TutorAvailability.query.get_or_404(id)

    # Ensure the availability belongs to this tutor
    if availability.tutor_id != tutor.id:
        flash('You do not have permission to delete this availability.', 'danger')
        return redirect(url_for('tutor.availability'))

    db.session.delete(availability)
    db.session.commit()

    flash('Availability slot deleted.', 'success')
    return redirect(url_for('tutor.availability'))

@tutor.route('/service-rates', methods=['GET', 'POST'])
@login_required
def service_rates():
    """Service rates are now managed by managers only."""
    flash('Service rates are now managed by managers only.', 'info')
    return redirect(url_for('tutor.dashboard'))

@tutor.route('/service-rates/<int:id>/edit', methods=['GET'])
@login_required
def edit_service_rate(id):
    """Service rates are now managed by managers only."""
    flash('Service rates are now managed by managers only.', 'info')
    return redirect(url_for('tutor.dashboard'))

@tutor.route('/service-rates/<int:id>/delete', methods=['POST'])
@login_required
def delete_service_rate(id):
    """Service rates are now managed by managers only."""
    flash('Service rates are now managed by managers only.', 'info')
    return redirect(url_for('tutor.dashboard'))

@tutor.route('/payments')
@login_required
def payments():
    """View payments for the current tutor."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get filter parameters
    status = request.args.get('status', 'all')

    # Base query
    query = TutorPayment.query.filter_by(tutor_id=tutor.id)

    # Apply filters
    if status == 'pending':
        query = query.filter_by(status='ready')
    elif status == 'paid':
        query = query.filter_by(status='paid')

    # Order by date
    payments = query.order_by(TutorPayment.insert_date.desc()).all()

    # Calculate totals
    service_total = sum(float(payment.service_amount) for payment in payments)
    transport_total = sum(float(payment.transport_amount) for payment in payments)
    grand_total = service_total + transport_total

    return render_template('tutor/payments.html',
                          payments=payments,
                          current_status=status,
                          service_total=service_total,
                          transport_total=transport_total,
                          grand_total=grand_total)

@tutor.route('/clients/<int:id>')
@login_required
def view_client(id):
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the client
    client = Client.query.get_or_404(id)

    # Make sure this tutor has appointments with this client
    appointment_check = Appointment.query.filter_by(tutor_id=tutor.id, client_id=client.id).first()
    if not appointment_check:
        flash('You do not have access to this client\'s information.', 'danger')
        return redirect(url_for('tutor.client_list'))

    # Get the client's past appointments with this tutor
    past_appointments = Appointment.query.filter(
        Appointment.tutor_id == tutor.id,
        Appointment.client_id == client.id,
        Appointment.end_time < datetime.now()
    ).order_by(Appointment.start_time.desc()).all()

    # Get the client's upcoming appointments with this tutor
    upcoming_appointments = Appointment.query.filter(
        Appointment.tutor_id == tutor.id,
        Appointment.client_id == client.id,
        Appointment.start_time >= datetime.now(),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).all()

    # Get program enrollments if applicable
    enrollments = []
    try:
        from app.models.enrollment import Enrollment
        enrollments = Enrollment.query.filter_by(client_id=client.id, status='active').all()
    except:
        pass

    return render_template('tutor/client_detail.html',
                          client=client,
                          past_appointments=past_appointments,
                          upcoming_appointments=upcoming_appointments,
                          enrollments=enrollments)

# Helper functions for calendar manipulation
def prepare_calendar_data(appointments, start_date, end_date, view_type):
    """Prepare appointment data for the calendar view."""
    calendar_data = {
        'view_type': view_type,
        'start_date': start_date.strftime('%Y-%m-%d'),
        'time_slots': [],
        'appointments': []
    }

    # Generate time slots based on view type
    if view_type == 'day' or view_type == 'week':
        # For day and week views, show hourly slots
        for hour in range(8, 22):  # 8 AM to 9 PM
            calendar_data['time_slots'].append({
                'hour': hour,
                'label': f"{hour % 12 or 12} {'AM' if hour < 12 else 'PM'}"
            })
    elif view_type == 'month':
        # For month view, show days
        current_date = start_date
        while current_date < end_date:
            calendar_data['time_slots'].append({
                'date': current_date.strftime('%Y-%m-%d'),
                'day': current_date.day,
                'weekday': current_date.strftime('%a')
            })
            current_date += timedelta(days=1)

    # Add appointment data
    for appointment in appointments:
        tutor_service = TutorService.query.get(appointment.tutor_service_id)
        service = Service.query.get(tutor_service.service_id) if tutor_service else None

        calendar_data['appointments'].append({
            'id': appointment.id,
            'client_name': appointment.appointment_subject_name,  # This handles both clients and dependants
            'student_name': appointment.appointment_subject_name,  # Alias for backward compatibility
            'service_name': service.name if service else "Unknown",
            'start_time': appointment.start_time.strftime('%Y-%m-%d %H:%M'),
            'end_time': appointment.end_time.strftime('%Y-%m-%d %H:%M'),
            'status': appointment.status
        })

    return calendar_data

def get_navigation_dates(current_date, view_type):
    """Generate previous and next dates for navigation."""
    if view_type == 'day':
        prev_date = current_date - timedelta(days=1)
        next_date = current_date + timedelta(days=1)
    elif view_type == 'week':
        prev_date = current_date - timedelta(days=7)
        next_date = current_date + timedelta(days=7)
    elif view_type == 'month':
        # Go to the same day in the previous/next month
        if current_date.month == 1:
            prev_date = current_date.replace(year=current_date.year-1, month=12)
        else:
            prev_date = current_date.replace(month=current_date.month-1)

        if current_date.month == 12:
            next_date = current_date.replace(year=current_date.year+1, month=1)
        else:
            next_date = current_date.replace(month=current_date.month+1)
    else:
        # Default to daily navigation
        prev_date = current_date - timedelta(days=1)
        next_date = current_date + timedelta(days=1)

    return prev_date, next_date

@tutor.route('/time-off', methods=['GET', 'POST'])
@login_required
def time_off_requests():
    """Manage time-off requests."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()

    # Get existing time-off requests
    requests = TimeOff.query.filter_by(tutor_id=tutor.id).order_by(TimeOff.start_date.desc()).all()

    # Handle form submission for new time-off request
    form = TimeOffRequestForm()
    if form.validate_on_submit():
        # Create new time-off request
        time_off = TimeOff(
            tutor_id=tutor.id,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            reason=form.reason.data,
            status='pending'
        )

        db.session.add(time_off)
        db.session.commit()

        # Create notification for managers
        managers = User.query.filter_by(role='manager').all()
        for manager in managers:
            Notification.create_notification(
                user_id=manager.id,
                message=f"New time-off request from {tutor.first_name} {tutor.last_name} for {time_off.start_date.strftime('%Y-%m-%d')} to {time_off.end_date.strftime('%Y-%m-%d')}",
                category='time_off',
                related_id=time_off.id
            )

        flash('Your time-off request has been submitted.', 'success')
        return redirect(url_for('tutor.time_off_requests'))

    return render_template('tutor/time_off.html',
                          requests=requests,
                          form=form,
                          tutor=tutor)

@tutor.route('/time-off/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_time_off_request(id):
    """Cancel a pending time-off request."""
    tutor = Tutor.query.filter_by(user_id=current_user.id).first_or_404()
    time_off = TimeOff.query.get_or_404(id)

    # Ensure the time-off request belongs to this tutor
    if time_off.tutor_id != tutor.id:
        flash('You do not have permission to cancel this time-off request.', 'danger')
        return redirect(url_for('tutor.time_off_requests'))

    # Only allow cancellation of pending requests
    if not time_off.is_pending:
        flash('Only pending time-off requests can be cancelled.', 'warning')
        return redirect(url_for('tutor.time_off_requests'))

    # Delete the time-off request
    db.session.delete(time_off)
    db.session.commit()

    # Create notification for managers
    managers = User.query.filter_by(role='manager').all()
    for manager in managers:
        Notification.create_notification(
            user_id=manager.id,
            message=f"Time-off request from {tutor.first_name} {tutor.last_name} for {time_off.start_date.strftime('%Y-%m-%d')} to {time_off.end_date.strftime('%Y-%m-%d')} has been cancelled",
            category='time_off',
            related_id=None
        )

    flash('Your time-off request has been cancelled.', 'success')
    return redirect(url_for('tutor.time_off_requests'))