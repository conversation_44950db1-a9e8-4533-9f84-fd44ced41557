# app/models/tutor.py
from datetime import datetime
from app.extensions import db
from cryptography.fernet import <PERSON><PERSON><PERSON>
from flask import current_app

class Tutor(db.Model):
    __tablename__ = 'tutors'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=False)
    bio = db.Column(db.Text, nullable=True)
    qualifications = db.Column(db.Text, nullable=True)

    # Address fields
    street_address = db.Column(db.Text, nullable=True)
    city = db.Column(db.String(100), nullable=True)
    province = db.Column(db.String(100), nullable=True)
    zip_code = db.Column(db.String(20), nullable=True)
    country = db.Column(db.String(100), nullable=True)

    # Personal information
    birthdate = db.Column(db.Date, nullable=True)

    # Banking information (encrypted)
    bank_transit_number_encrypted = db.Column(db.String(255), nullable=True)
    bank_institution_number_encrypted = db.Column(db.String(255), nullable=True)
    bank_account_number_encrypted = db.Column(db.String(255), nullable=True)

    is_active = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tutor_services = db.relationship('TutorService', lazy='dynamic', cascade='all, delete-orphan')
    appointments = db.relationship('Appointment', lazy='dynamic')

    def __repr__(self):
        return f'<Tutor {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def address(self):
        """Return a formatted address string."""
        parts = []
        if self.street_address:
            parts.append(self.street_address)

        city_parts = []
        if self.city:
            city_parts.append(self.city)
        if self.province:
            city_parts.append(self.province)
        if self.zip_code:
            city_parts.append(self.zip_code)

        if city_parts:
            parts.append(", ".join(city_parts))

        if self.country:
            parts.append(self.country)

        return "\n".join(parts) if parts else ""

    def _get_encryption_key(self):
        """Get the encryption key from app config."""
        key = current_app.config.get('ENCRYPTION_KEY')
        if not key:
            raise ValueError("Encryption key not configured")

        # If the key is the default development key, use a consistent fallback
        if key == 'YourDefaultDevKeyHere-ChangeInProduction-32Bytes':
            # Use a consistent development key instead of generating random ones
            key = '5H_gMR5wm4WwSvqj4VWNhOyIT9zef9n6Tk89scFQmOE='
            current_app.logger.warning("Using fallback encryption key for development. "
                                     "Set ENCRYPTION_KEY environment variable for production!")

        return key

    def set_bank_transit_number(self, value):
        """Encrypt and set the bank transit number."""
        if not value:
            self.bank_transit_number_encrypted = None
            return

        key = self._get_encryption_key()
        f = Fernet(key.encode() if isinstance(key, str) else key)
        self.bank_transit_number_encrypted = f.encrypt(value.encode()).decode()

    def get_bank_transit_number(self):
        """Decrypt and return the bank transit number."""
        if not self.bank_transit_number_encrypted:
            return None

        try:
            key = self._get_encryption_key()
            f = Fernet(key.encode() if isinstance(key, str) else key)
            return f.decrypt(self.bank_transit_number_encrypted.encode()).decode()
        except Exception as e:
            current_app.logger.warning(f"Failed to decrypt bank transit number for tutor {self.id}: {str(e)}")
            # Return None if decryption fails (data encrypted with different key)
            return None

    def set_bank_institution_number(self, value):
        """Encrypt and set the bank institution number."""
        if not value:
            self.bank_institution_number_encrypted = None
            return

        key = self._get_encryption_key()
        f = Fernet(key.encode() if isinstance(key, str) else key)
        self.bank_institution_number_encrypted = f.encrypt(value.encode()).decode()

    def get_bank_institution_number(self):
        """Decrypt and return the bank institution number."""
        if not self.bank_institution_number_encrypted:
            return None

        try:
            key = self._get_encryption_key()
            f = Fernet(key.encode() if isinstance(key, str) else key)
            return f.decrypt(self.bank_institution_number_encrypted.encode()).decode()
        except Exception as e:
            current_app.logger.warning(f"Failed to decrypt bank institution number for tutor {self.id}: {str(e)}")
            # Return None if decryption fails (data encrypted with different key)
            return None

    def set_bank_account_number(self, value):
        """Encrypt and set the bank account number."""
        if not value:
            self.bank_account_number_encrypted = None
            return

        key = self._get_encryption_key()
        f = Fernet(key.encode() if isinstance(key, str) else key)
        self.bank_account_number_encrypted = f.encrypt(value.encode()).decode()

    def get_bank_account_number(self):
        """Decrypt and return the bank account number."""
        if not self.bank_account_number_encrypted:
            return None

        try:
            key = self._get_encryption_key()
            f = Fernet(key.encode() if isinstance(key, str) else key)
            return f.decrypt(self.bank_account_number_encrypted.encode()).decode()
        except Exception as e:
            current_app.logger.warning(f"Failed to decrypt bank account number for tutor {self.id}: {str(e)}")
            # Return None if decryption fails (data encrypted with different key)
            return None