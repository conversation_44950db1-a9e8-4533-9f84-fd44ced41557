# app/i18n.py
import json
import os
from flask import request, session, g

def get_locale():
    """Get the locale for the current request."""
    # Try to get the locale from the session
    if 'language' in session:
        return session['language']

    # Try to get the locale from the request
    locale = request.args.get('lang')
    if locale:
        session['language'] = locale
        return locale

    # Default to English
    return 'en'

def init_babel(app):
    """Initialize i18n with the Flask app."""
    # No initialization needed for our simple implementation
    pass

def load_translations(locale='en'):
    """Load translations from JSON files."""
    try:
        file_path = os.path.join(os.path.dirname(__file__), f'locales/{locale}/common.json')
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        # Fallback to English if the requested locale is not available
        if locale != 'en':
            return load_translations('en')
        return {}

def t(key, locale=None):
    """Translate a key."""
    if not locale:
        locale = get_locale()

    # Load translations if not already loaded
    if not hasattr(g, 'translations') or g.locale != locale:
        g.translations = load_translations(locale)
        g.locale = locale

    # Split the key by dots to navigate the nested dictionary
    parts = key.split('.')
    value = g.translations

    for part in parts:
        if isinstance(value, dict) and part in value:
            value = value[part]
        else:
            # Key not found, return the key itself
            return key

    return value if isinstance(value, str) else key
