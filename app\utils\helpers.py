# app/utils/helpers.py
from datetime import datetime, time
from flask import current_app, session
import pytz

def format_currency(amount):
    """Format a numeric value as USD currency."""
    return "${:.2f}".format(float(amount))

def format_datetime(dt, locale_code=None):
    """Format a datetime object for display with locale support."""
    if not dt:
        return ""

    if locale_code is None:
        locale_code = session.get('language', 'en')

    if locale_code == 'fr':
        # French format: DD/MM/YYYY HH:MM
        return dt.strftime("%d/%m/%Y %H:%M")
    else:
        # English format: YYYY-MM-DD HH:MM AM/PM
        return dt.strftime("%Y-%m-%d %I:%M %p")

def format_date(date, locale_code=None):
    """Format a date object for display with locale support."""
    if not date:
        return ""

    if locale_code is None:
        locale_code = session.get('language', 'en')

    if locale_code == 'fr':
        # French format: DD/MM/YYYY
        return date.strftime("%d/%m/%Y")
    else:
        # English format: YYYY-MM-DD
        return date.strftime("%Y-%m-%d")

def format_time(time_obj, locale_code=None):
    """Format a time object for display with locale support."""
    if not time_obj:
        return ""

    if locale_code is None:
        locale_code = session.get('language', 'en')

    if locale_code == 'fr':
        # French format: 24-hour time
        return time_obj.strftime("%H:%M")
    else:
        # English format: 12-hour time with AM/PM
        return time_obj.strftime("%I:%M %p")

def format_long_date(date, locale_code=None):
    """Format a date object for long display with locale support."""
    if not date:
        return ""

    if locale_code is None:
        locale_code = session.get('language', 'en')

    if locale_code == 'fr':
        # French format: Lundi 15 janvier 2024
        months_fr = [
            'janvier', 'février', 'mars', 'avril', 'mai', 'juin',
            'juillet', 'août', 'septembre', 'octobre', 'novembre', 'décembre'
        ]
        days_fr = [
            'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi', 'dimanche'
        ]
        day_name = days_fr[date.weekday()]
        month_name = months_fr[date.month - 1]
        return f"{day_name.capitalize()} {date.day} {month_name} {date.year}"
    else:
        # English format: Monday, January 15, 2024
        return date.strftime("%A, %B %d, %Y")

def validate_business_hours(start_time, end_time):
    """Validate that appointment times are within business hours (8 AM to 9 PM EST)."""
    if not start_time or not end_time:
        return False
    
    # Convert to EST timezone
    tz = pytz.timezone(current_app.config.get('TIMEZONE', 'America/New_York'))
    
    if isinstance(start_time, datetime):
        start_time = start_time.astimezone(tz).time()
    if isinstance(end_time, datetime):
        end_time = end_time.astimezone(tz).time()
    
    # Business hours: 8 AM to 9 PM
    business_start = time(8, 0)
    business_end = time(21, 0)
    
    # Check if times are within business hours
    if start_time < business_start or end_time > business_end:
        return False
    
    # Check if end time is after start time
    if start_time >= end_time:
        return False
    
    return True

def get_est_now():
    """Get current datetime in EST timezone."""
    tz = pytz.timezone(current_app.config.get('TIMEZONE', 'America/New_York'))
    return datetime.now(tz)