<!-- app/templates/manager/tutors_by_service.html -->
{% extends "base.html" %}

{% block title %}Tu<PERSON> for {{ service.name }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Tutors for {{ service.name }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.services_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Services
        </a>
        <a href="{{ url_for('manager.new_tutor_service') }}?service_id={{ service.id }}" class="btn btn-primary ms-2">
            <i class="fas fa-plus"></i> Assign Tutor
        </a>
    </div>
</div>

<!-- Service Details -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">Service Details</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6 class="text-muted mb-1">Name</h6>
                <p>{{ service.name }}</p>
            </div>
            <div class="col-md-3">
                <h6 class="text-muted mb-1">Default Price</h6>
                <p>${{ "%.2f"|format(service.default_price) }}</p>
            </div>
            <div class="col-md-3">
                <h6 class="text-muted mb-1">Duration</h6>
                <p>{{ service.duration_minutes }} minutes</p>
            </div>
        </div>
        
        {% if service.description %}
            <div class="row mt-3">
                <div class="col-12">
                    <h6 class="text-muted mb-1">Description</h6>
                    <p>{{ service.description }}</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Tutors Table -->
<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">Assigned Tutors</h5>
    </div>
    <div class="card-body">
        {% if tutors %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Tutor Rate</th>
                            <th>Client Rate</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tutor in tutors %}
                            {% set tutor_service = tutor.tutor_services.filter_by(service_id=service.id).first() %}
                            <tr>
                                <td>{{ tutor.first_name }} {{ tutor.last_name }}</td>
                                <td>{{ tutor.user.email }}</td>
                                <td>{{ tutor.phone }}</td>
                                <td>${{ "%.2f"|format(tutor_service.tutor_rate) }}</td>
                                <td>${{ "%.2f"|format(tutor_service.client_rate) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if tutor_service.is_active else 'danger' }}">
                                        {{ 'Active' if tutor_service.is_active else 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.edit_tutor_service', id=tutor_service.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit Rate
                                    </a>
                                    <a href="{{ url_for('manager.view_tutor', id=tutor.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View Tutor
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>No Tutors Assigned</h5>
                <p class="text-muted">No tutors have been assigned to this service yet.</p>
                <a href="{{ url_for('manager.new_tutor_service') }}?service_id={{ service.id }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> Assign Tutor
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}