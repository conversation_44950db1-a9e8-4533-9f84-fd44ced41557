# app/__init__.py
import os
from flask import Flask, render_template, request, jsonify
from flask_migrate import Migrate
from flask_login import <PERSON><PERSON><PERSON>anager
from flask_mail import Mail
from flask_wtf.csrf import CSRFProtect
from datetime import datetime
from dotenv import load_dotenv

from app.extensions import db, migrate, login_manager, mail, csrf
from app.i18n import init_babel, t, load_translations

@login_manager.user_loader
def load_user(user_id):
    from app.models.user import User
    try:
        return User.query.get(int(user_id))
    except Exception as e:
        # Handle database connection issues or missing tables gracefully
        print(f"Error loading user {user_id}: {e}")
        return None

def create_app(config_name=None):
    app = Flask(__name__)

    # Load environment variables from .env file
    load_dotenv()

    # Load configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'development')

    app.config.from_object(f'app.config.{config_name.capitalize()}Config')

    # Initialize extensions with app
    db.init_app(app)
    migrate.init_app(app, db)
    login_manager.init_app(app)
    mail.init_app(app)

    # Initialize CSRF protection
    csrf.init_app(app)

    @app.after_request
    def after_request(response):
        # Add CORS headers for API routes if needed
        if request.path.startswith('/api/'):
            response.headers.add('Access-Control-Allow-Origin', '*')
            response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken')
            response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response

    # Initialize i18n
    init_babel(app)

    # Add custom Jinja2 filters
    @app.template_filter('nl2br')
    def nl2br_filter(s):
        if s is None:
            return ""
        return s.replace('\n', '<br>')

    # Add locale-aware date formatting filters
    @app.template_filter('format_date')
    def format_date_filter(date, locale_code=None):
        """Safely format a date value with locale support."""
        from app.utils.helpers import format_date
        return format_date(date, locale_code)

    @app.template_filter('format_datetime')
    def format_datetime_filter(dt, locale_code=None):
        """Safely format a datetime value with locale support."""
        from app.utils.helpers import format_datetime
        return format_datetime(dt, locale_code)

    @app.template_filter('format_time')
    def format_time_filter(time_obj, locale_code=None):
        """Safely format a time value with locale support."""
        from app.utils.helpers import format_time
        return format_time(time_obj, locale_code)

    @app.template_filter('format_long_date')
    def format_long_date_filter(date, locale_code=None):
        """Safely format a date value in long format with locale support."""
        from app.utils.helpers import format_long_date
        return format_long_date(date, locale_code)

    # Set up login view
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'

    # Register blueprints
    from app.views.main import main as main_blueprint
    app.register_blueprint(main_blueprint)

    from app.views.auth import auth as auth_blueprint
    app.register_blueprint(auth_blueprint)

    from app.views.manager import manager as manager_blueprint
    app.register_blueprint(manager_blueprint, url_prefix='/manager')

    from app.views.client import client as client_blueprint
    app.register_blueprint(client_blueprint, url_prefix='/client')

    from app.views.tutor import tutor as tutor_blueprint
    app.register_blueprint(tutor_blueprint, url_prefix='/tutor')

    from app.views.api import api as api_blueprint
    app.register_blueprint(api_blueprint, url_prefix='/api')

    # Exempt API blueprint from CSRF protection
    csrf.exempt(api_blueprint)

    from app.views.manager_tutor_profile import manager_tutor_profile as manager_tutor_profile_blueprint
    app.register_blueprint(manager_tutor_profile_blueprint, url_prefix='/manager/tutor-profile')

    from app.views.public import public_bp as public_blueprint
    app.register_blueprint(public_blueprint, url_prefix='/public')

    # Register error handlers
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('404.html'), 404

    @app.errorhandler(500)
    def internal_server_error(e):
        return render_template('500.html'), 500

    # CSRF error handler
    from flask_wtf.csrf import CSRFError
    @app.errorhandler(CSRFError)
    def handle_csrf_error(e):
        app.logger.warning(f'CSRF error: {e.description} - Path: {request.path} - Method: {request.method} - User: {getattr(request, "user", "Anonymous")} - Headers: {dict(request.headers)}')
        return render_template('csrf_error.html', reason=e.description), 400

    # Register CLI commands
    from app.commands.appointment_commands import (
        update_past_appointments_command,
        remind_pending_confirmations_command,
        update_subscription_usage_command,
        check_expired_subscriptions_command
    )
    from app.commands.participant_count_commands import (
        fix_participant_counts_command,
        check_participant_counts_command
    )
    app.cli.add_command(update_past_appointments_command)
    app.cli.add_command(remind_pending_confirmations_command)
    app.cli.add_command(update_subscription_usage_command)
    app.cli.add_command(check_expired_subscriptions_command)
    app.cli.add_command(fix_participant_counts_command)
    app.cli.add_command(check_participant_counts_command)

    # Create a single context processor for all common data
    @app.context_processor
    def inject_common_data():
        from app.i18n import get_locale
        return {
            'app_name': "TutorAide Inc.",
            'now': datetime.now(),
            'hasattr': hasattr,
            't': t,  # Add translation function to all templates
            'load_translations': load_translations,  # Add load_translations function to all templates
            'current_locale': get_locale()  # Add current locale to all templates
        }

    return app