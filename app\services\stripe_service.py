# app/services/stripe_service.py
import stripe
from flask import current_app
from app.extensions import db
from app.models.client import Client
from app.models.invoice import Invoice
from app.models.subscription import Subscription

class StripeService:
    @staticmethod
    def init_stripe():
        """Initialize the Stripe API with the secret key."""
        stripe.api_key = current_app.config['STRIPE_SECRET_KEY']

    @staticmethod
    def create_customer(client):
        """Create a Stripe customer for a client."""
        StripeService.init_stripe()

        try:
            # Create a new customer in Stripe
            customer = stripe.Customer.create(
                email=client.user.email if client.user else client.email,
                name=f"{client.first_name} {client.last_name}",
                phone=client.phone,
                metadata={
                    'client_id': client.id
                }
            )

            # Update client record with Stripe customer ID
            client.stripe_customer_id = customer.id
            db.session.commit()

            return customer

        except stripe.error.StripeError as e:
            current_app.logger.error(f"Stripe error: {str(e)}")
            return None

    @staticmethod
    def get_or_create_customer(client):
        """Get existing customer or create a new one."""
        if client.stripe_customer_id:
            try:
                StripeService.init_stripe()
                customer = stripe.Customer.retrieve(client.stripe_customer_id)
                return customer
            except stripe.error.StripeError:
                # If there's an error, try creating a new customer
                return StripeService.create_customer(client)
        else:
            return StripeService.create_customer(client)

    @staticmethod
    def create_payment_intent(invoice):
        """Create a payment intent for an invoice."""
        StripeService.init_stripe()

        client = Client.query.get(invoice.client_id)
        if not client:
            return None

        try:
            # Ensure client has a Stripe customer ID
            customer = StripeService.get_or_create_customer(client)
            if not customer:
                return None

            # Create payment intent
            amount_cents = int(float(invoice.total_amount) * 100)  # Convert to cents
            intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                customer=customer.id,
                metadata={
                    'invoice_id': invoice.id
                },
                description=f"Invoice #{invoice.id} - Tutoring Services"
            )

            # Update invoice with payment intent ID
            invoice.stripe_payment_intent_id = intent.id
            db.session.commit()

            return intent

        except stripe.error.StripeError as e:
            current_app.logger.error(f"Stripe error: {str(e)}")
            return None

    @staticmethod
    def handle_webhook_event(event):
        """Process a Stripe webhook event with race condition protection."""
        from sqlalchemy import text
        from app.services.invoice_service import InvoiceService

        StripeService.init_stripe()

        event_type = event['type']

        if event_type == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            payment_intent_id = payment_intent.get('id')

            if payment_intent_id:
                # Use the InvoiceService's race-condition-safe payment processing
                success, invoice = InvoiceService.process_payment_success(payment_intent_id)

                if success:
                    current_app.logger.info(f"Webhook processed payment for invoice {invoice.id}")
                    return True
                else:
                    current_app.logger.warning(f"Webhook failed to process payment for intent {payment_intent_id}")
                    return False

        # Other event types can be handled here

        return False

    # Add to app/services/stripe_service.py

    @staticmethod
    def create_subscription(subscription_id):
        """Create a Stripe subscription for a subscription plan."""
        StripeService.init_stripe()

        subscription = Subscription.query.get_or_404(subscription_id)
        plan = subscription.plan
        client = Client.query.get(subscription.client_id)

        if not client:
            return None

        try:
            # Ensure client has a Stripe customer ID
            customer = StripeService.get_or_create_customer(client)
            if not customer:
                return None

            # Create a Stripe product for this plan if it doesn't exist
            product = stripe.Product.create(
                name=plan.name,
                description=plan.description or f"{plan.duration_months} month subscription, {plan.max_hours} hours"
            )

            # Create a price for this product
            price = stripe.Price.create(
                product=product.id,
                unit_amount=int(float(plan.price) * 100),  # Convert to cents
                currency='usd',
                recurring={
                    'interval': 'month',
                    'interval_count': plan.duration_months
                }
            )

            # Create the subscription
            stripe_subscription = stripe.Subscription.create(
                customer=customer.id,
                items=[
                    {
                        'price': price.id,
                        'quantity': 1
                    }
                ],
                metadata={
                    'subscription_id': subscription.id
                }
            )

            # Update our subscription with Stripe ID
            subscription.stripe_subscription_id = stripe_subscription.id
            db.session.commit()

            return stripe_subscription

        except stripe.error.StripeError as e:
            current_app.logger.error(f"Stripe error: {str(e)}")
            return None

    @staticmethod
    def format_canadian_routing_number(transit_number, institution_number):
        """Format Canadian bank routing number for Stripe."""
        # Canadian routing format: 0 + institution (3 digits) + transit (5 digits)
        # Ensure proper padding
        institution = str(institution_number).zfill(3)
        transit = str(transit_number).zfill(5)
        return f"0{institution}{transit}"

    @staticmethod
    def create_bank_account_token(account_number, routing_number, account_holder_name):
        """Create a bank account token for Canadian EFT."""
        StripeService.init_stripe()

        try:
            token = stripe.Token.create(
                bank_account={
                    'country': 'CA',
                    'currency': 'cad',
                    'account_holder_name': account_holder_name,
                    'account_holder_type': 'individual',
                    'routing_number': routing_number,
                    'account_number': account_number,
                }
            )
            return token
        except stripe.error.StripeError as e:
            current_app.logger.error(f"Stripe bank account token error: {str(e)}")
            return None

    @staticmethod
    def create_payout(amount, bank_account_token, description, metadata=None):
        """Create a simulated Stripe payout for testing purposes."""
        StripeService.init_stripe()

        try:
            # Convert amount to cents
            amount_cents = int(float(amount) * 100)

            current_app.logger.info(f"Creating test payout: amount={amount_cents} cents, token={bank_account_token}")

            # In test mode, we'll simulate a payout by creating a mock payout object
            # Real Stripe payouts require Connect accounts and external accounts setup

            # For testing purposes, create a mock payout response
            mock_payout = {
                'id': f'po_test_{bank_account_token[-8:]}_{amount_cents}',
                'object': 'payout',
                'amount': amount_cents,
                'currency': 'cad',
                'description': description,
                'status': 'paid',
                'metadata': metadata or {},
                'created': **********,  # Mock timestamp (Jan 1, 2022)
            }

            # Create a simple object to mimic Stripe's response
            class MockPayout:
                def __init__(self, data):
                    for key, value in data.items():
                        setattr(self, key, value)

            payout = MockPayout(mock_payout)

            current_app.logger.info(f"Created test payout: {payout.id}")
            current_app.logger.info("NOTE: This is a test payout simulation. No real money was transferred.")

            return payout

        except Exception as e:
            current_app.logger.error(f"Test payout error: {str(e)}")
            return None