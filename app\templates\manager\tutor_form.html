<!-- app/templates/manager/tutor_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.tutors_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Tutors
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if tutor %}action="{{ url_for('manager.edit_tutor', id=tutor.id) }}"{% else %}action="{{ url_for('manager.new_tutor') }}"{% endif %}>
                    {{ form.csrf_token }}

                    <h5 class="mb-3">Account Information</h5>
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">
                                {% if tutor %}New Password (leave blank to keep current){% else %}Password{% endif %}
                            </label>
                            {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                            {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                            {% if form.confirm_password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.confirm_password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Personal Information</h5>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">First Name</label>
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">Last Name</label>
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Address Information</h5>

                    <div class="mb-3">
                        <label for="street_address" class="form-label">Street Address</label>
                        {{ form.street_address(class="form-control" + (" is-invalid" if form.street_address.errors else "")) }}
                        {% if form.street_address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.street_address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">City</label>
                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="province" class="form-label">Province/State</label>
                            {{ form.province(class="form-control" + (" is-invalid" if form.province.errors else "")) }}
                            {% if form.province.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.province.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="zip_code" class="form-label">Zip/Postal Code</label>
                            {{ form.zip_code(class="form-control" + (" is-invalid" if form.zip_code.errors else "")) }}
                            {% if form.zip_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zip_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">Country</label>
                            {{ form.country(class="form-control" + (" is-invalid" if form.country.errors else "")) }}
                            {% if form.country.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.country.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Personal Information</h5>

                    <div class="mb-3">
                        <label for="birthdate" class="form-label">Birthdate</label>
                        {{ form.birthdate(class="form-control", type="date") }}
                        {% if form.birthdate.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.birthdate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Banking Information (Encrypted)</h5>

                    <div class="mb-3">
                        <label for="bank_transit_number" class="form-label">Bank Transit Number</label>
                        {{ form.bank_transit_number(class="form-control" + (" is-invalid" if form.bank_transit_number.errors else "")) }}
                        {% if form.bank_transit_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_transit_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Bank transit number (5 digits).</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_institution_number" class="form-label">Bank Institution Number</label>
                        {{ form.bank_institution_number(class="form-control" + (" is-invalid" if form.bank_institution_number.errors else "")) }}
                        {% if form.bank_institution_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_institution_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Bank institution number (3 digits).</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_account_number" class="form-label">Bank Account Number</label>
                        {{ form.bank_account_number(class="form-control" + (" is-invalid" if form.bank_account_number.errors else "")) }}
                        {% if form.bank_account_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_account_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">Bank account number (7-12 digits).</div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">Professional Information</h5>

                    <div class="mb-3">
                        <label for="qualifications" class="form-label">Qualifications</label>
                        {{ form.qualifications(class="form-control", rows=3) }}
                        <div class="form-text">Education, certifications, and experience relevant to tutoring.</div>
                    </div>

                    <div class="mb-3">
                        <label for="bio" class="form-label">Bio</label>
                        {{ form.bio(class="form-control", rows=5) }}
                        <div class="form-text">A brief biography that will be visible to clients.</div>
                    </div>

                    <div class="mb-3 form-check">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.tutors_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}