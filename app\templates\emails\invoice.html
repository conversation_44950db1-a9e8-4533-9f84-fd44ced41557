<!-- app/templates/emails/invoice.html -->
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #dddddd;
            border-radius: 5px;
        }
        .header {
            background-color: #0d6efd;
            padding: 10px 20px;
            color: white;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
        }
        .invoice-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            font-size: 12px;
            color: #777777;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #dddddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Invoice #{{ invoice.id }}</h2>
        </div>
        <div class="content">
            <p>Dear {{ invoice.client.first_name }} {{ invoice.client.last_name }},</p>

            <p>An invoice has been generated for tutoring services provided.</p>

            <div class="invoice-details">
                <p><strong>Invoice #:</strong> {{ invoice.id }}</p>
                <p><strong>Date:</strong> {{ invoice.invoice_date.strftime('%Y-%m-%d') }}</p>
                <p><strong>Due Date:</strong> {{ invoice.due_date.strftime('%Y-%m-%d') }}</p>
                <p><strong>Amount Due:</strong> ${{ "%.2f"|format(invoice.total_amount) }}</p>
            </div>

            {% if message %}
                <p>{{ message }}</p>
            {% endif %}

            <p>You can view the full invoice and make a payment through your client portal:</p>

            <p>
                <a href="{{ invoice_url }}" class="button">View Invoice</a>
            </p>

            <p>Summary of charges:</p>

            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Service</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                        <tr>
                            <td>{{ item.appointment.start_time.strftime('%Y-%m-%d') }}</td>
                            <td>{{ item.description }}</td>
                            <td>${{ "%.2f"|format(item.amount) }}</td>
                        </tr>
                    {% endfor %}
                    <tr>
                        <td colspan="2" style="text-align: right;"><strong>Total:</strong></td>
                        <td><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                    </tr>
                </tbody>
            </table>

            <p>Thank you for choosing our tutoring services!</p>

            <p>Best regards,<br>
            TutorAide Inc.</p>
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply directly to this email.</p>
        </div>
    </div>
</body>
</html>