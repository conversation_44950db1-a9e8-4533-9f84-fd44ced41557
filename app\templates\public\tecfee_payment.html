{% extends "base.html" %}

{% block title %}Paiement TECFÉE - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary mb-3">Finaliser Votre Inscription TECFÉE</h1>
                <p class="lead">Confirmez votre sélection et procédez au paiement</p>
            </div>

            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart"></i>
                        Résumé de Votre Commande
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="fw-bold">{{ program.name }}</h6>
                            <p class="text-muted mb-2">{{ program.description }}</p>
                            
                            {% if enrollment_data.pricing_type == 'full_package' %}
                                <div class="alert alert-success">
                                    <i class="fas fa-star"></i>
                                    <strong>Forfait Complet:</strong> 12 sessions sélectionnées (1 de chaque module)
                                    <br><small>Économisez 140,88$ par rapport au prix individuel!</small>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Tarification par module:</strong> 
                                    {{ enrollment_data.selected_sessions|length if enrollment_data.selected_sessions else 0 }} session(s) sélectionnée(s)
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="h4 text-success fw-bold">
                                {{ "%.2f"|format(enrollment_data.total_amount if enrollment_data.total_amount else enrollment_data.price) }}$
                            </div>
                            <small class="text-muted">CAD, taxes incluses</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card"></i>
                        Informations de Paiement
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Simulated Payment Form (for demo purposes) -->
                    <form method="POST" action="{{ url_for('public.process_tecfee_payment') }}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        
                        <div class="alert alert-warning mb-4">
                            <i class="fas fa-info-circle"></i>
                            <strong>Mode Démonstration:</strong> Ce formulaire simule un paiement réussi. 
                            Dans un environnement de production, l'intégration Stripe serait utilisée pour le traitement sécurisé des paiements.
                        </div>

                        <!-- Simulated Payment Fields -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Numéro de carte</label>
                                <input type="text" class="form-control" placeholder="**** **** **** 1234" readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Expiration</label>
                                <input type="text" class="form-control" placeholder="MM/AA" readonly>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">CVC</label>
                                <input type="text" class="form-control" placeholder="123" readonly>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Nom sur la carte</label>
                                <input type="text" class="form-control" placeholder="Nom du titulaire" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Code postal de facturation</label>
                                <input type="text" class="form-control" placeholder="H1A 1A1" readonly>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">conditions générales</a> 
                                et la <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">politique de confidentialité</a>
                            </label>
                        </div>

                        <!-- Payment Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-lock"></i>
                                Confirmer le Paiement - {{ "%.2f"|format(enrollment_data.total_amount if enrollment_data.total_amount else enrollment_data.price) }}$
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Notice -->
            <div class="text-center mt-4">
                <small class="text-muted">
                    <i class="fas fa-shield-alt"></i>
                    Vos informations de paiement sont sécurisées et cryptées
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Conditions Générales</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Inscription et Paiement</h6>
                <p>L'inscription au programme TECFÉE est confirmée après réception du paiement complet.</p>

                <h6>2. Sessions de Groupe</h6>
                <p>Les sessions de groupe nécessitent un minimum de 4 participants. Si ce minimum n'est pas atteint, TutorAide Inc. se réserve le droit de reporter ou d'annuler la session.</p>

                <h6>3. Politique d'Annulation</h6>
                <p>Les annulations doivent être effectuées au moins 24 heures avant la session pour être éligibles à un remboursement.</p>

                <h6>4. Matériel de Cours</h6>
                <p>Tout le matériel de cours est fourni numériquement et reste la propriété de TutorAide Inc.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Politique de Confidentialité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Collecte d'Informations</h6>
                <p>Nous collectons uniquement les informations nécessaires pour fournir nos services éducatifs.</p>

                <h6>Utilisation des Données</h6>
                <p>Vos données personnelles sont utilisées exclusivement pour la gestion de votre inscription et la communication liée au programme.</p>

                <h6>Protection des Données</h6>
                <p>Nous utilisons des mesures de sécurité appropriées pour protéger vos informations personnelles.</p>

                <h6>Partage d'Informations</h6>
                <p>Nous ne partageons pas vos informations personnelles avec des tiers sans votre consentement explicite.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const termsCheckbox = document.getElementById('terms');
    const submitButton = form.querySelector('button[type="submit"]');

    function updateSubmitButton() {
        submitButton.disabled = !termsCheckbox.checked;
    }

    termsCheckbox.addEventListener('change', updateSubmitButton);
    updateSubmitButton(); // Initial state

    // Form submission
    form.addEventListener('submit', function(e) {
        if (!termsCheckbox.checked) {
            e.preventDefault();
            alert('Veuillez accepter les conditions générales pour continuer.');
            return;
        }

        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Traitement en cours...';
    });
});
</script>
{% endblock %}
