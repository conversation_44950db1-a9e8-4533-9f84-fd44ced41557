# app/views/auth.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from app.models.user import User
from app.forms.auth_forms import LoginForm, RequestResetForm, ResetPasswordForm, RegistrationForm
from app.extensions import db
from app.utils.email import send_password_reset_email
from urllib.parse import urlparse
from datetime import datetime
from app.i18n import t

auth = Blueprint('auth', __name__)

@auth.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect_to_dashboard()

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()

        if user is None or not user.check_password(form.password.data):
            flash('Invalid email or password', 'danger')
            return render_template('auth/login.html', form=form)

        if not user.is_active:
            flash('This account has been deactivated. Please contact administration.', 'danger')
            return render_template('auth/login.html', form=form)

        # Check if email is verified for TECFÉE clients
        if user.role in ['client', 'parent'] and not user.email_verified:
            flash('Please verify your email address before logging in. Check your email for the verification link.', 'warning')
            return render_template('auth/login.html', form=form)

        login_user(user, remember=form.remember_me.data)

        # Update last login time
        user.last_login = datetime.utcnow()
        db.session.commit()

        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = redirect_to_dashboard()

        flash(t('flash_messages.login_success'), 'success')
        return redirect(next_page)

    return render_template('auth/login.html', form=form)

@auth.route('/logout')
def logout():
    logout_user()
    flash(t('flash_messages.logout_success'), 'info')
    return redirect(url_for('auth.login'))


@auth.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('manager.dashboard'))

    form = RegistrationForm()
    if form.validate_on_submit():
        # This is a basic registration - for TECFÉE prospects, they should use the prospect flow
        flash('Registration is currently only available through our program enrollment process.', 'info')
        return redirect(url_for('public.tecfee_enrollment'))

    return render_template('auth/register.html', form=form)

@auth.route('/reset_password_request', methods=['GET', 'POST'])
def reset_password_request():
    if current_user.is_authenticated:
        return redirect_to_dashboard()

    form = RequestResetForm()
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user:
            token = user.generate_reset_token()
            db.session.commit()
            send_password_reset_email(user, token)

        flash(t('flash_messages.password_reset_sent'), 'info')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password_request.html', form=form)

@auth.route('/reset_password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    if current_user.is_authenticated:
        return redirect_to_dashboard()

    # Find user by reset token
    user = User.query.filter_by(reset_token=token).first()
    if not user or not user.verify_reset_token(token):
        flash(t('flash_messages.invalid_token'), 'warning')
        return redirect(url_for('auth.reset_password_request'))

    form = ResetPasswordForm()
    if form.validate_on_submit():
        user.set_password(form.password.data)
        user.clear_reset_token()
        db.session.commit()
        flash(t('flash_messages.password_reset_success'), 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/reset_password.html', form=form)

@auth.route('/verify_email/<token>')
def verify_email(token):
    """Verify email address using verification token."""
    if current_user.is_authenticated and current_user.email_verified:
        flash('Your email is already verified.', 'info')
        return redirect_to_dashboard()

    # Find user by verification token
    user = User.query.filter_by(verification_token=token).first()
    if not user or not user.verify_email_token(token):
        flash('Invalid or expired verification link. Please request a new verification email.', 'warning')
        return redirect(url_for('auth.login'))

    # Verify the email
    user.confirm_email()
    db.session.commit()

    # Log the user in automatically after verification
    login_user(user)

    # Send welcome email
    from app.services.email_service import EmailService
    EmailService.send_welcome_email(user)

    flash('Your email has been verified successfully! You can now proceed to select your TECFÉE sessions.', 'success')

    # Redirect to TECFÉE session selection
    return redirect(url_for('public.tecfee_session_selection'))

@auth.route('/email-verification-pending')
def email_verification_pending():
    """Show email verification pending page."""
    return render_template('auth/email_verification_pending.html')

@auth.route('/google-signup')
def google_signup():
    """Show Google Sign-Up options for different user types."""
    from app.services.google_auth_service import GoogleAuthService

    # Check if Google authentication is available
    if not GoogleAuthService.is_available():
        flash('Google Sign-In is currently not available. Please use traditional registration.', 'warning')
        return redirect(url_for('auth.login'))

    return render_template('auth/google_signup_options.html')

@auth.route('/test-google-buttons')
def test_google_buttons():
    """Test page for Google Sign-In button styling (development only)."""
    return render_template('test_google_buttons.html')

@auth.route('/simple-google-test')
def simple_google_test():
    """Simple test page for Google Sign-In buttons (always works)."""
    return render_template('simple_google_test.html')

@auth.route('/debug-google-config')
def debug_google_config():
    """Debug Google OAuth configuration (development only)."""
    from app.services.google_auth_service import GoogleAuthService
    from flask import current_app

    config_info = {
        'google_client_id': current_app.config.get('GOOGLE_CLIENT_ID', 'Not set'),
        'google_client_secret': 'Set' if current_app.config.get('GOOGLE_CLIENT_SECRET') else 'Not set',
        'google_auth_available': GoogleAuthService.is_available(),
        'libraries_available': 'Yes' if hasattr(GoogleAuthService, '_google_available') else 'Checking...'
    }

    return f"""
    <h1>Google OAuth Configuration Debug</h1>
    <ul>
        <li><strong>Google Client ID:</strong> {config_info['google_client_id']}</li>
        <li><strong>Google Client Secret:</strong> {config_info['google_client_secret']}</li>
        <li><strong>Google Auth Available:</strong> {config_info['google_auth_available']}</li>
        <li><strong>Libraries Available:</strong> {config_info['libraries_available']}</li>
    </ul>
    <p><a href="/auth/login">Back to Login</a></p>
    """

@auth.route('/google-login')
def google_login():
    """Initiate Google OAuth login."""
    from app.services.google_auth_service import GoogleAuthService

    # Check if Google authentication is available
    if not GoogleAuthService.is_available():
        flash('Google Sign-In is currently not available. Please use email/password login.', 'warning')
        return redirect(url_for('auth.login'))

    # Store the intended destination in session
    next_page = request.args.get('next', '')
    if next_page:
        session['google_auth_next'] = next_page

    # Store if this is for TECFÉE registration
    tecfee_registration = request.args.get('tecfee', False)
    if tecfee_registration:
        session['google_auth_tecfee'] = True

    # Store user type for account creation (if specified)
    user_type = request.args.get('user_type', '')
    if user_type:
        session['google_auth_user_type'] = user_type

    authorization_url = GoogleAuthService.get_authorization_url()
    if not authorization_url:
        flash('Failed to initiate Google Sign-In. Please try again or use email/password login.', 'error')
        return redirect(url_for('auth.login'))

    return redirect(authorization_url)

@auth.route('/google-callback')
def google_callback():
    """Handle Google OAuth callback."""
    from app.services.google_auth_service import GoogleAuthService
    from app.services.user_service import UserService

    # Get authorization code and state from callback
    authorization_code = request.args.get('code')
    state = request.args.get('state')

    if not authorization_code:
        flash('Google authentication was cancelled.', 'warning')
        return redirect(url_for('auth.login'))

    # Handle the callback
    success, user_info, message = GoogleAuthService.handle_callback(authorization_code, state)

    if not success:
        flash(f'Google authentication failed: {message}', 'error')
        return redirect(url_for('auth.login'))

    # Check if user already exists
    existing_user = User.query.filter_by(email=user_info['email']).first()

    if existing_user:
        # Update existing user with Google info if not already set
        if not existing_user.google_id:
            existing_user.google_id = user_info['google_id']
            existing_user.auth_provider = 'google'
            existing_user.email_verified = True  # Google emails are verified
            db.session.commit()

        # Log in the existing user
        login_user(existing_user)
        existing_user.last_login = datetime.utcnow()
        db.session.commit()

        flash('Successfully logged in with Google!', 'success')

        # Handle redirect
        next_page = session.pop('google_auth_next', None)
        if next_page:
            return redirect(next_page)

        return redirect(redirect_to_dashboard())

    else:
        # Check if this is for TECFÉE registration
        is_tecfee_registration = session.pop('google_auth_tecfee', False)
        user_type = session.pop('google_auth_user_type', '')

        if is_tecfee_registration:
            # Create new client account for TECFÉE
            success, client, message = UserService.create_google_client(
                email=user_info['email'],
                first_name=user_info['first_name'],
                last_name=user_info['last_name'],
                google_id=user_info['google_id']
            )

            if success:
                # Log in the new user
                login_user(client.user)
                flash('Account created successfully with Google! Please complete your profile.', 'success')

                # Redirect to TECFÉE profile completion
                return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))
            else:
                flash(f'Failed to create account: {message}', 'error')
                return redirect(url_for('public.tecfee_enrollment'))

        elif user_type in ['manager', 'tutor', 'client']:
            # Create account for specific user type
            success, user_obj, message = UserService.create_google_user(
                email=user_info['email'],
                first_name=user_info['first_name'],
                last_name=user_info['last_name'],
                google_id=user_info['google_id'],
                user_type=user_type
            )

            if success:
                # Log in the new user
                login_user(user_obj)
                flash(f'Account created successfully with Google! Welcome to TutorAide.', 'success')

                # Redirect to appropriate dashboard
                return redirect(redirect_to_dashboard())
            else:
                flash(f'Failed to create account: {message}', 'error')
                return redirect(url_for('auth.login'))
        else:
            # Regular Google login attempt - no account found
            flash('No account found with this Google email. Please contact support to create an account.', 'warning')
            return redirect(url_for('auth.login'))

def redirect_to_dashboard():
    """Redirect users to their appropriate dashboard based on role."""
    if current_user.role == 'manager':
        return url_for('manager.dashboard')
    elif current_user.role == 'tutor':
        return url_for('tutor.dashboard')
    elif current_user.role in ['parent', 'client']:
        return url_for('client.dashboard')

    # Default fallback
    return url_for('auth.login')

# app/utils/email.py
from flask import current_app, render_template
from flask_mail import Message
from app import mail
from threading import Thread

def send_async_email(app, msg):
    with app.app_context():
        mail.send(msg)

def send_email(subject, recipients, text_body, html_body, sender=None):
    msg = Message(subject, recipients=recipients, sender=sender)
    msg.body = text_body
    msg.html = html_body

    # Send email asynchronously
    Thread(
        target=send_async_email,
        args=(current_app._get_current_object(), msg)
    ).start()

def send_password_reset_email(user, token):
    reset_url = url_for('auth.reset_password', token=token, _external=True)

    send_email(
        subject='Password Reset Request',
        recipients=[user.email],
        text_body=render_template('emails/reset_password.txt', user=user, reset_url=reset_url),
        html_body=render_template('emails/reset_password.html', user=user, reset_url=reset_url)
    )