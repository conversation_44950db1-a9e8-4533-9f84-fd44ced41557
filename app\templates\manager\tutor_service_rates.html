{% extends 'base.html' %}

{% block title %}Manage Tutor Service Rates{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Manage Service Rates for {{ tutor.full_name }}</h1>
            <p class="lead">Set the tutor's rates for each service they provide.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.view_tutor', id=tutor.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Tutor
            </a>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Current Service Rates</h5>
                </div>
                <div class="card-body">
                    {% if rates %}
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Tutor Rate</th>
                                    <th>Client Rate</th>
                                    <th>Transport Fee</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rate in rates %}
                                <tr>
                                    <td>{{ rate.service.name }}</td>
                                    <td>${{ "%.2f"|format(rate.tutor_rate) }}</td>
                                    <td>${{ "%.2f"|format(rate.client_rate) }}</td>
                                    <td>
                                        {% if rate.transport_fee and rate.transport_fee > 0 %}
                                            ${{ "%.2f"|format(rate.transport_fee) }}
                                            {% if rate.transport_fee_description %}
                                                <small class="d-block text-muted">{{ rate.transport_fee_description }}</small>
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rate.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('manager_tutor_profile.edit_tutor_service_rate', tutor_id=tutor.id, id=rate.id) }}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <form method="POST" action="{{ url_for('manager_tutor_profile.delete_tutor_service_rate', tutor_id=tutor.id, id=rate.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this service rate?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="alert alert-info">
                            No service rates set for this tutor yet. Use the form to add rates for services they provide.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add New Service Rate</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id) }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.service_id.label(class="form-label") }}
                            {{ form.service_id(class="form-select") }}
                        </div>
                        <div class="mb-3">
                            {{ form.tutor_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.client_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.client_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.transport_fee(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee_description.label(class="form-label") }}
                            {{ form.transport_fee_description(class="form-control") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Add Service Rate</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
