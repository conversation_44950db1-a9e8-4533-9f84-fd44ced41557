{% extends "base.html" %}

{% block title %}Create TECFÉE Group Session - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-calendar-plus text-primary"></i>
                Create TECFÉE Group Session
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">TECFÉE</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_group_sessions') }}">Group Sessions</a></li>
                    <li class="breadcrumb-item active">New Session</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Sessions
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Session Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Session Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Program (Hidden/Read-only) -->
                        {{ form.program_id() }}
                        <div class="mb-4">
                            <label class="form-label">Program</label>
                            <div class="form-control-plaintext bg-light p-2 rounded">
                                <strong>{{ program.name }}</strong><br>
                                <small class="text-muted">{{ program.description }}</small>
                            </div>
                        </div>

                        <!-- Module Selection -->
                        <div class="mb-4">
                            <label for="{{ form.module_id.id }}" class="form-label">
                                {{ form.module_id.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.module_id(class="form-select") }}
                            {% if form.module_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.module_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Select which module this group session will cover.
                            </div>
                        </div>

                        <!-- Tutor Selection -->
                        <div class="mb-4">
                            <label for="{{ form.tutor_id.id }}" class="form-label">
                                {{ form.tutor_id.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.tutor_id(class="form-select") }}
                            {% if form.tutor_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tutor_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Choose the tutor who will conduct this session.
                            </div>
                        </div>

                        <!-- Date and Time -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.session_date.id }}" class="form-label">
                                    {{ form.session_date.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.session_date(class="form-control") }}
                                {% if form.session_date.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.session_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.session_time.id }}" class="form-label">
                                    {{ form.session_time.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.session_time(class="form-control") }}
                                {% if form.session_time.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.session_time.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Available times: 8:00 AM - 8:30 PM (30-minute increments)
                                </div>
                            </div>
                        </div>

                        <!-- Duration -->
                        <div class="mb-4">
                            <label for="{{ form.duration_minutes.id }}" class="form-label">
                                {{ form.duration_minutes.label.text }} <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                {{ form.duration_minutes(class="form-control") }}
                                <span class="input-group-text">minutes</span>
                            </div>
                            {% if form.duration_minutes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.duration_minutes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Typical TECFÉE sessions are 60 minutes long.
                            </div>
                        </div>

                        <!-- Participant Limits -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.min_participants.id }}" class="form-label">
                                    {{ form.min_participants.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.min_participants(class="form-control") }}
                                {% if form.min_participants.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.min_participants.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Minimum students needed to run the session.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.max_participants.id }}" class="form-label">
                                    {{ form.max_participants.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.max_participants(class="form-control") }}
                                {% if form.max_participants.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.max_participants.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    Maximum students allowed in the session.
                                </div>
                            </div>
                        </div>

                        <!-- Tutor Rate -->
                        <div class="mb-4">
                            <label for="{{ form.tutor_rate_per_student.id }}" class="form-label">
                                {{ form.tutor_rate_per_student.label.text }} <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate_per_student(class="form-control") }}
                                <span class="input-group-text">per student</span>
                            </div>
                            {% if form.tutor_rate_per_student.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tutor_rate_per_student.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Amount the tutor will be paid per student attending the session.
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="{{ form.notes.id }}" class="form-label">
                                {{ form.notes.label.text }}
                            </label>
                            {{ form.notes(class="form-control", rows="3", placeholder="Optional notes about this session...") }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Session Information Sidebar -->
        <div class="col-lg-4">
            <!-- Session Guidelines -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Session Guidelines</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">Group Session Requirements</h6>
                        <ul class="mb-0">
                            <li>Minimum 4 students to proceed</li>
                            <li>Maximum 10 students per session</li>
                            <li>Sessions are conducted online</li>
                            <li>Standard duration is 60 minutes</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Payment Structure</h6>
                        <p class="mb-0">
                            Tutors are paid <strong>$15 per student</strong> who attends the session.
                            Payment is calculated based on actual attendance.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Payment Calculator -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">Payment Calculator</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-success" id="min-payment">$60.00</strong><br>
                                <small class="text-muted">Minimum Payment</small><br>
                                <small class="text-muted">(4 students)</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-primary" id="max-payment">$150.00</strong><br>
                                <small class="text-muted">Maximum Payment</small><br>
                                <small class="text-muted">(10 students)</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <label class="form-label">Estimated Payment:</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="text" class="form-control" id="estimated-payment" readonly value="105.00">
                        </div>
                        <small class="text-muted">Based on 7 students (average)</small>
                    </div>
                </div>
            </div>

            <!-- Module Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">TECFÉE Modules</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for module in program.modules %}
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Module {{ module.module_number }}</strong><br>
                                    <small class="text-muted">{{ module.name }}</small>
                                </div>
                                <span class="badge bg-secondary">{{ module.duration_minutes }}min</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const sessionDateInput = document.getElementById('{{ form.session_date.id }}');
    if (sessionDateInput) {
        const today = new Date().toISOString().split('T')[0];
        sessionDateInput.setAttribute('min', today);
    }

    // Update payment calculator when participant limits change
    const minParticipantsInput = document.getElementById('{{ form.min_participants.id }}');
    const maxParticipantsInput = document.getElementById('{{ form.max_participants.id }}');
    const tutorRateInput = document.getElementById('{{ form.tutor_rate_per_student.id }}');

    function updatePaymentCalculator() {
        const minParticipants = parseInt(minParticipantsInput.value) || 4;
        const maxParticipants = parseInt(maxParticipantsInput.value) || 10;
        const tutorRate = parseFloat(tutorRateInput.value) || 15.00;

        const minPayment = minParticipants * tutorRate;
        const maxPayment = maxParticipants * tutorRate;
        const avgParticipants = Math.round((minParticipants + maxParticipants) / 2);
        const estimatedPayment = avgParticipants * tutorRate;

        document.getElementById('min-payment').textContent = `$${minPayment.toFixed(2)}`;
        document.getElementById('max-payment').textContent = `$${maxPayment.toFixed(2)}`;
        document.getElementById('estimated-payment').value = estimatedPayment.toFixed(2);

        // Update the small text
        const estimatedText = document.querySelector('#estimated-payment').parentElement.nextElementSibling;
        estimatedText.textContent = `Based on ${avgParticipants} students (average)`;
    }

    if (minParticipantsInput) minParticipantsInput.addEventListener('input', updatePaymentCalculator);
    if (maxParticipantsInput) maxParticipantsInput.addEventListener('input', updatePaymentCalculator);
    if (tutorRateInput) tutorRateInput.addEventListener('input', updatePaymentCalculator);

    // Validate participant limits
    function validateParticipantLimits() {
        const minVal = parseInt(minParticipantsInput.value);
        const maxVal = parseInt(maxParticipantsInput.value);

        if (minVal && maxVal && minVal > maxVal) {
            maxParticipantsInput.setCustomValidity('Maximum must be greater than or equal to minimum');
        } else {
            maxParticipantsInput.setCustomValidity('');
        }
    }

    if (minParticipantsInput) minParticipantsInput.addEventListener('input', validateParticipantLimits);
    if (maxParticipantsInput) maxParticipantsInput.addEventListener('input', validateParticipantLimits);

    // Initialize calculator
    updatePaymentCalculator();
});
</script>
{% endblock %}
