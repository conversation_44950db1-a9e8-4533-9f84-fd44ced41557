# app/tasks/subscription_tasks.py
import logging
from app.services.invoice_service import InvoiceService
from app.models.subscription import Subscription
from datetime import datetime

logger = logging.getLogger(__name__)

def check_expired_subscriptions():
    """
    Check for subscriptions that have expired and create notifications for managers.
    This should be run daily.
    """
    try:
        count = InvoiceService.check_for_expired_subscriptions()
        logger.info(f"Created {count} notifications for expired subscriptions")
        return count
    except Exception as e:
        logger.error(f"Error checking expired subscriptions: {str(e)}")
        return 0
