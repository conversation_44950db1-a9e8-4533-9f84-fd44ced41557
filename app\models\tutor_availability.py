# app/models/tutor_availability.py
from datetime import datetime
from app.extensions import db

class TutorAvailability(db.Model):
    __tablename__ = 'tutor_availabilities'
    
    id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('tutors.id', ondelete='CASCADE'), nullable=False)
    day_of_week = db.Column(db.Integer, nullable=False)  # 0=Monday, 1=Tuesday, etc.
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    tutor = db.relationship('Tutor', backref='availabilities')
    
    def __repr__(self):
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        day_name = days[self.day_of_week]
        return f'<TutorAvailability {day_name} {self.start_time.strftime("%H:%M")}-{self.end_time.strftime("%H:%M")}>'
    
    @property
    def day_name(self):
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        return days[self.day_of_week]
    
    @property
    def time_range(self):
        return f"{self.start_time.strftime('%I:%M %p')} - {self.end_time.strftime('%I:%M %p')}"
