{% extends "base.html" %}

{% block title %}Inscription TECFÉE - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary mb-3">Inscription au Programme TECFÉE</h1>
                <p class="lead">Choisissez votre option de paiement et commencez votre préparation</p>
            </div>

            {% if not current_user.is_authenticated %}
            <!-- Client Registration Form -->
            <div class="alert alert-info text-center mb-4">
                <h4 class="alert-heading">Inscription au Programme TECFÉE</h4>
                <p>Créez votre compte et inscrivez-vous au programme TECFÉE en une seule étape.</p>
                <p><small>Vous avez déjà un compte? <a href="{{ url_for('auth.login') }}">Connectez-vous ici</a></small></p>
            </div>

            <!-- Google Sign-In Option (only show if configured) -->
            {% if config.GOOGLE_CLIENT_ID and config.GOOGLE_CLIENT_SECRET %}
            <div class="card border-primary mb-4">
                <div class="card-header bg-primary text-white text-center">
                    <h5 class="mb-0">
                        <i class="fab fa-google"></i>
                        Inscription Rapide avec Google
                    </h5>
                </div>
                <div class="card-body text-center">
                    <p class="mb-3">Créez votre compte instantanément avec votre compte Google</p>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <p class="small mt-2">Inscription en 1 clic</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-success">
                                <i class="fas fa-shield-alt fa-2x"></i>
                                <p class="small mt-2">Sécurisé par Google</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-success">
                                <i class="fas fa-envelope-check fa-2x"></i>
                                <p class="small mt-2">Email pré-vérifié</p>
                            </div>
                        </div>
                    </div>
                    <a href="{{ url_for('auth.google_login', tecfee=1) }}" class="google-signin-btn google-signin-btn-lg">
                        <svg class="google-logo" viewBox="0 0 24 24">
                            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                        </svg>
                        Continuer avec Google
                    </a>
                    <p class="small text-muted mt-2">
                        Aucun mot de passe requis • Inscription instantanée
                    </p>
                </div>
            </div>

            <!-- Divider -->
            <div class="text-center mb-4">
                <div class="d-flex align-items-center">
                    <hr class="flex-grow-1">
                    <span class="px-3 text-muted">OU</span>
                    <hr class="flex-grow-1">
                </div>
            </div>
            {% endif %}

            <!-- Client Registration Form -->
            <form method="POST" action="{{ url_for('public.tecfee_client_register') }}">
                {{ client_form.hidden_tag() }}

                <!-- Personal Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Informations Personnelles</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.first_name.id }}" class="form-label">
                                    {{ client_form.first_name.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.first_name(class="form-control") }}
                                {% if client_form.first_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.last_name.id }}" class="form-label">
                                    {{ client_form.last_name.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.last_name(class="form-control") }}
                                {% if client_form.last_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.email.id }}" class="form-label">
                                    {{ client_form.email.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.email(class="form-control") }}
                                {% if client_form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.phone.id }}" class="form-label">
                                    {{ client_form.phone.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.phone(class="form-control") }}
                                {% if client_form.phone.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.password.id }}" class="form-label">
                                    {{ client_form.password.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.password(class="form-control") }}
                                {% if client_form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ client_form.confirm_password.id }}" class="form-label">
                                    {{ client_form.confirm_password.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.confirm_password(class="form-control") }}
                                {% if client_form.confirm_password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.confirm_password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Adresse</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="{{ client_form.civic_number.id }}" class="form-label">
                                    {{ client_form.civic_number.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.civic_number(class="form-control") }}
                                {% if client_form.civic_number.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.civic_number.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-9 mb-3">
                                <label for="{{ client_form.street.id }}" class="form-label">
                                    {{ client_form.street.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.street(class="form-control") }}
                                {% if client_form.street.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.street.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ client_form.city.id }}" class="form-label">
                                    {{ client_form.city.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.city(class="form-control") }}
                                {% if client_form.city.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.city.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ client_form.province.id }}" class="form-label">
                                    {{ client_form.province.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.province(class="form-select") }}
                                {% if client_form.province.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.province.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ client_form.postal_code.id }}" class="form-label">
                                    {{ client_form.postal_code.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.postal_code(class="form-control") }}
                                {% if client_form.postal_code.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.postal_code.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ client_form.country.id }}" class="form-label">
                                    {{ client_form.country.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ client_form.country(class="form-select") }}
                                {% if client_form.country.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in client_form.country.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Pricing Options -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Options de Paiement</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            {% for pricing in pricing_options %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100 border-secondary">
                                    <div class="card-body">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="pricing_preference"
                                                   id="client_pricing_{{ pricing.pricing_type }}" value="{{ pricing.pricing_type }}"
                                                   {% if selected_pricing and selected_pricing.pricing_type == pricing.pricing_type %}checked{% endif %}
                                                   {% if not selected_pricing and loop.first %}checked{% endif %}>
                                            <label class="form-check-label w-100" for="client_pricing_{{ pricing.pricing_type }}">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h5 class="mb-0">
                                                        {% if pricing.pricing_type == 'per_session' %}
                                                        Module Individuel
                                                        {% else %}
                                                        Package Complet
                                                        {% endif %}
                                                    </h5>
                                                    <span class="badge bg-primary fs-6">${{ "%.2f"|format(pricing.price) }}</span>
                                                </div>
                                                <p class="text-muted mb-0">{{ pricing.description }}</p>

                                                {% if pricing.pricing_type == 'per_session' %}
                                                <small class="text-muted">
                                                    <i class="fas fa-info-circle"></i> Payez au fur et à mesure
                                                </small>
                                                {% else %}
                                                <small class="text-success">
                                                    <i class="fas fa-star"></i> Économisez $140 avec le package complet
                                                </small>
                                                {% endif %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% if client_form.pricing_preference.errors %}
                            <div class="text-danger mt-1">
                                {% for error in client_form.pricing_preference.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Contact Preferences -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Préférences de Contact</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="{{ client_form.preferred_contact_method.id }}" class="form-label">
                                {{ client_form.preferred_contact_method.label.text }}
                            </label>
                            {{ client_form.preferred_contact_method(class="form-select") }}
                        </div>

                        <div class="mb-3">
                            <label for="{{ client_form.notes.id }}" class="form-label">
                                {{ client_form.notes.label.text }}
                            </label>
                            {{ client_form.notes(class="form-control", rows="3") }}
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Conditions d'Inscription</h6>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="prospect_terms_accepted" required>
                            <label class="form-check-label" for="prospect_terms_accepted">
                                J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">conditions générales</a> et la <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">politique de confidentialité</a>
                            </label>
                        </div>

                        <div class="alert alert-info">
                            <h6 class="alert-heading">Important:</h6>
                            <ul class="mb-0">
                                <li>Les sessions de groupe nécessitent un minimum de 4 participants pour avoir lieu</li>
                                <li>Maximum de 10 participants par session</li>
                                <li>Les sessions sont dispensées en ligne via notre plateforme</li>
                                <li>Le paiement est requis avant le début du programme</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    {{ client_form.submit(class="btn btn-success btn-lg px-5") }}
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-lock"></i> Paiement sécurisé via Stripe
                        </small>
                    </div>
                </div>
            </form>

            {% elif current_user.role != 'client' %}
            <!-- Not a Client -->
            <div class="alert alert-warning text-center">
                <h4 class="alert-heading">Accès Restreint</h4>
                <p>Seuls les clients peuvent s'inscrire aux programmes.</p>
            </div>

            {% else %}
                <!-- Enrollment Form for Existing Clients -->
                <form method="POST" action="{{ url_for('public.process_tecfee_enrollment') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <!-- Pricing Options -->
                    <div class="row mb-4">
                        {% for pricing in pricing_options %}
                        <div class="col-md-6 mb-3">
                            <div class="card h-100 {% if selected_pricing and selected_pricing.id == pricing.id %}border-success{% else %}border-secondary{% endif %}">
                                <div class="card-body">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="pricing_type"
                                               id="pricing_{{ pricing.pricing_type }}" value="{{ pricing.pricing_type }}"
                                               {% if selected_pricing and selected_pricing.id == pricing.id %}checked{% endif %}
                                               {% if not selected_pricing and loop.first %}checked{% endif %}>
                                        <label class="form-check-label w-100" for="pricing_{{ pricing.pricing_type }}">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h5 class="mb-0">
                                                    {% if pricing.pricing_type == 'per_session' %}
                                                    Module Individuel
                                                    {% else %}
                                                    Package Complet
                                                    {% endif %}
                                                </h5>
                                                <span class="badge bg-primary fs-6">${{ "%.2f"|format(pricing.price) }}</span>
                                            </div>
                                            <p class="text-muted mb-0">{{ pricing.description }}</p>

                                            {% if pricing.pricing_type == 'per_session' %}
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle"></i> Payez au fur et à mesure
                                            </small>
                                            {% else %}
                                            <small class="text-success">
                                                <i class="fas fa-star"></i> Économisez $140 avec le package complet
                                            </small>
                                            {% endif %}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Program Details -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Détails du Programme</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Contenu:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> 12 modules structurés</li>
                                        <li><i class="fas fa-check text-success"></i> Préparation complète au TECFÉE</li>
                                        <li><i class="fas fa-check text-success"></i> Simulation d'examen finale (2h)</li>
                                        <li><i class="fas fa-check text-success"></i> Matériel de cours inclus</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Format:</h6>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-users text-primary"></i> Sessions de groupe (4-10 étudiants)</li>
                                        <li><i class="fas fa-laptop text-primary"></i> En ligne et en direct</li>
                                        <li><i class="fas fa-clock text-primary"></i> 1 heure par session</li>
                                        <li><i class="fas fa-calendar text-primary"></i> Durée: 12 semaines</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Conditions d'Inscription</h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="terms_accepted" required>
                                <label class="form-check-label" for="terms_accepted">
                                    J'accepte les <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">conditions générales</a> et la <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">politique de confidentialité</a>
                                </label>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">Important:</h6>
                                <ul class="mb-0">
                                    <li>Les sessions de groupe nécessitent un minimum de 4 participants pour avoir lieu</li>
                                    <li>Maximum de 10 participants par session</li>
                                    <li>Les sessions sont dispensées en ligne via notre plateforme</li>
                                    <li>Le paiement est requis avant le début du programme</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-success btn-lg px-5">
                            <i class="fas fa-credit-card"></i> Procéder au Paiement
                        </button>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-lock"></i> Paiement sécurisé via Stripe
                            </small>
                        </div>
                    </div>
                </form>
            {% endif %}

            <!-- Back to Program -->
            <div class="text-center mt-4">
                <a href="{{ url_for('public.tecfee') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Retour au Programme
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Conditions Générales</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Inscription et Paiement</h6>
                <p>L'inscription au programme TECFÉE est confirmée après réception du paiement complet.</p>

                <h6>2. Sessions de Groupe</h6>
                <p>Les sessions de groupe nécessitent un minimum de 4 participants. Si ce minimum n'est pas atteint, TutorAide Inc. se réserve le droit de reporter ou d'annuler la session.</p>

                <h6>3. Remboursement</h6>
                <p>Les remboursements sont possibles jusqu'à 48 heures avant le début de la première session.</p>

                <h6>4. Assiduité</h6>
                <p>La présence aux sessions est fortement recommandée pour maximiser les bénéfices du programme.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Politique de Confidentialité</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Collecte d'Informations</h6>
                <p>Nous collectons uniquement les informations nécessaires pour fournir nos services éducatifs.</p>

                <h6>Utilisation des Données</h6>
                <p>Vos données personnelles sont utilisées exclusivement pour la gestion de votre inscription et la communication liée au programme.</p>

                <h6>Protection des Données</h6>
                <p>Nous mettons en place des mesures de sécurité appropriées pour protéger vos informations personnelles.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle pricing option selection
    const pricingRadios = document.querySelectorAll('input[name="pricing_type"]');
    const cards = document.querySelectorAll('.card');

    pricingRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // Reset all cards
            cards.forEach(card => {
                card.classList.remove('border-success');
                card.classList.add('border-secondary');
            });

            // Highlight selected card
            const selectedCard = this.closest('.card');
            selectedCard.classList.remove('border-secondary');
            selectedCard.classList.add('border-success');
        });
    });
});
</script>
{% endblock %}
