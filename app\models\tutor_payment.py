from datetime import datetime
from app.extensions import db

class TutorPayment(db.Model):
    __tablename__ = 'tutor_payments'

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('tutors.id'), nullable=False)
    appointment_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('appointments.id'), nullable=False)
    service_amount = db.Column(db.Numeric(10, 2), nullable=False)  # Tutor's pay for the service
    transport_amount = db.Column(db.Numeric(10, 2), nullable=False, default=0)  # Transport fee
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)  # Total payment
    status = db.Column(db.String(20), default='pending')  # pending, paid
    payment_date = db.Column(db.DateTime, nullable=True)
    stripe_payout_id = db.Column(db.String(255), nullable=True)  # Stripe payout reference ID
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    tutor = db.relationship('Tutor')
    appointment = db.relationship('Appointment', uselist=False)

    def __repr__(self):
        return f'<TutorPayment {self.id} tutor={self.tutor_id} amount=${self.total_amount}>'