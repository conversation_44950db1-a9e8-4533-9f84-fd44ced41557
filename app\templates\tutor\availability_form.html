{% extends 'base.html' %}

{% block title %}Edit Availability{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle All Day checkbox
    const allDayCheckbox = document.getElementById('all_day_checkbox');
    const startTimeSelect = document.getElementById('start_time_select');
    const endTimeSelect = document.getElementById('end_time_select');
    const timeSelectionContainer = document.getElementById('time_selection_container');

    if (allDayCheckbox && startTimeSelect && endTimeSelect) {
        // Set initial values
        if (allDayCheckbox.checked) {
            timeSelectionContainer.style.display = 'none';
            // Set to 8:00 AM - 9:00 PM
            startTimeSelect.value = '08:00';
            endTimeSelect.value = '21:00';
        }

        // Add event listener for checkbox changes
        allDayCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // Hide time selection and set to all day (8:00 AM - 9:00 PM)
                timeSelectionContainer.style.display = 'none';
                startTimeSelect.value = '08:00';
                endTimeSelect.value = '21:00';
            } else {
                // Show time selection
                timeSelectionContainer.style.display = 'block';
            }
        });
    }

    // Debug form submission
    const availabilityForm = document.querySelector('form[action*="availability"]');
    if (availabilityForm) {
        availabilityForm.addEventListener('submit', function(e) {
            console.log('Edit form submission detected');
            console.log('Form data:', new FormData(this));
            // Don't prevent default - let the form submit normally
        });
    }
});
</script>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Edit Availability</h1>
    <p class="lead">Update your availability slot.</p>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Availability</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('tutor.availability') }}">
                        {{ form.csrf_token }}
                        {{ form.id }}
                        <div class="mb-3">
                            {{ form.day_of_week.label(class="form-label") }}
                            {{ form.day_of_week(class="form-select") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.all_day(class="form-check-input", id="all_day_checkbox") }}
                            {{ form.all_day.label(class="form-check-label") }}
                        </div>
                        <div id="time_selection_container">
                            <div class="mb-3">
                                {{ form.start_time.label(class="form-label") }}
                                {{ form.start_time(class="form-select", id="start_time_select") }}
                                <small class="text-muted">Select a time in 30-minute increments</small>
                            </div>
                            <div class="mb-3">
                                {{ form.end_time.label(class="form-label") }}
                                {{ form.end_time(class="form-select" + (" is-invalid" if form.end_time.errors else ""), id="end_time_select") }}
                                <small class="text-muted">Select a time in 30-minute increments</small>
                                {% if form.end_time.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.end_time.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Update Availability</button>
                        <a href="{{ url_for('tutor.availability') }}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
