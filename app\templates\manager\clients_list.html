<!-- app/templates/manager/clients_list.html -->
{% extends "base.html" %}

{% block title %}Clients - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Clients</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_client') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Client
        </a>
    </div>
</div>

<!-- Filter Controls -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.clients_list') }}" class="row g-3">
            <div class="col-md-3">
                <label for="client_type" class="form-label">Client Type</label>
                <select name="client_type" id="client_type" class="form-select">
                    <option value="">All Types</option>
                    <option value="individual" {% if request.args.get('client_type') == 'individual' %}selected{% endif %}>Individual</option>
                    <option value="institutional" {% if request.args.get('client_type') == 'institutional' %}selected{% endif %}>Institutional</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="">All Statuses</option>
                    <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>Active</option>
                    <option value="suspended" {% if request.args.get('status') == 'suspended' %}selected{% endif %}>Suspended</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">Search</label>
                <input type="text" name="search" id="search" class="form-control" placeholder="Name, email, phone..." value="{{ request.args.get('search', '') }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i> Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Clients Table -->
<div class="card shadow">
    <div class="card-body">
        {% if clients %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Type</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Dependants</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                            <tr>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if client.client_type == 'individual' else 'info' }}">
                                        {{ client.client_type|capitalize }}
                                    </span>
                                </td>
                                <td>
                                    {% if client.user %}
                                        {{ client.user.email }}
                                    {% else %}
                                        {{ client.email }}
                                    {% endif %}
                                </td>
                                <td>{{ client.phone }}</td>
                                <td>
                                    {% set dependants_count = client.get_dependants_count() %}
                                    {% if dependants_count > 0 %}
                                        <span class="badge bg-primary rounded-pill">
                                            {{ dependants_count }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if client.is_suspended %}
                                        <span class="badge bg-danger">Suspended</span>
                                    {% else %}
                                        <span class="badge bg-success">Active</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.edit_client', id=client.id) }}" class="btn btn-sm btn-primary" title="Edit client">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-sm btn-info" title="View client details">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <div class="dropdown d-inline">
                                        <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ client.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                            More
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ client.id }}">
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-calendar"></i> Appointments (Coming Soon)
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-file-invoice-dollar"></i> Invoices (Coming Soon)
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-ticket-alt"></i> Subscriptions (Coming Soon)
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-graduation-cap"></i> Programs (Coming Soon)
                                                </span>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-cog"></i> Management features coming soon
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination will be added when needed -->
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="mb-3">No clients found</h4>
                <p class="mb-4">Try adjusting your search criteria or add a new client.</p>
                <a href="{{ url_for('manager.new_client') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Client
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Client management modals will be added here when functionality is implemented -->
{% endblock %}

{% block scripts %}
<script>
    // Client management functionality will be added here
    console.log('Client management page loaded');
</script>
{% endblock %}
