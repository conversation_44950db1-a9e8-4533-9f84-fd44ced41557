# app/commands/appointment_commands.py
import click
from flask.cli import with_appcontext
from app.tasks.appointment_tasks import update_past_appointments, remind_pending_confirmations
from app.tasks.subscription_tasks import check_expired_subscriptions
from app.services.subscription_service import SubscriptionService

@click.command('update-past-appointments')
@with_appcontext
def update_past_appointments_command():
    """Update status of past appointments to awaiting confirmation."""
    count = update_past_appointments()
    click.echo(f"Updated {count} past appointments to 'awaiting_confirmation' status")

@click.command('remind-pending-confirmations')
@with_appcontext
def remind_pending_confirmations_command():
    """Send reminder emails for appointments awaiting confirmation."""
    count = remind_pending_confirmations()
    click.echo(f"Sent {count} reminder emails for pending confirmations")

@click.command('update-subscription-usage')
@with_appcontext
def update_subscription_usage_command():
    """Update subscription usage for completed appointments that haven't been recorded."""
    count = SubscriptionService.update_all_subscription_usage()
    click.echo(f"Updated subscription usage for {count} completed appointments")

@click.command('check-expired-subscriptions')
@with_appcontext
def check_expired_subscriptions_command():
    """Check for expired subscriptions and create notifications."""
    count = check_expired_subscriptions()
    click.echo(f"Created {count} notifications for expired subscriptions")
