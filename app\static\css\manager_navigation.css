/* Enhanced Navigation Styles for Manager Interface */

/* Use CSS variables for consistency */
:root {
    --manager-navbar-text: var(--navbar-text, #212529);
    --manager-navbar-hover: var(--navbar-text-hover, #007bff);
    --manager-navbar-shadow: var(--navbar-shadow, 0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Main Navigation Styling */
.navbar-light.bg-white {
    box-shadow: var(--manager-navbar-shadow);
}

/* Ensure text visibility on white background */
.navbar-light .navbar-nav .nav-link,
.navbar-light .navbar-nav .dropdown-toggle {
    color: var(--manager-navbar-text) !important;
    font-weight: 500;
}

.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .dropdown-toggle:hover,
.navbar-light .navbar-nav .dropdown-toggle:focus {
    color: var(--manager-navbar-hover) !important;
}

/* Add spacing between nav items */
.navbar-nav .nav-item {
    margin-right: 0.25rem;
}

/* Enhance dropdown menus */
.dropdown-menu {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    border: none;
    min-width: 240px;
}

/* Style dropdown headers */
.dropdown-header {
    font-weight: 600;
    color: #495057;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Style dropdown items */
.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.dropdown-item i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
    color: #6c757d;
}

.dropdown-item:hover i,
.dropdown-item:focus i {
    color: #fff;
}

/* Hover effects for dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #007bff;
    color: white;
}

/* Style dividers */
.dropdown-divider {
    margin: 0.25rem 0;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.25em 0.5em;
}

/* Quick Access Bar */
.quick-actions .btn {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.quick-actions .btn i {
    margin-right: 0.25rem;
}

/* Breadcrumb styling */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #007bff;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Global search styling */
.navbar-search-form {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
}

.navbar .input-group {
    height: 32px;
    margin-top: 0;
}

.navbar .search-input {
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    transition: all 0.3s ease;
    width: 180px;
    height: 32px;
    font-size: 0.9rem;
    border-radius: 4px 0 0 4px;
    padding: 0.25rem 0.75rem;
}

.navbar .search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.navbar .search-input:focus {
    background-color: white;
    color: #212529;
    width: 220px;
    box-shadow: 0 0 0 0.15rem rgba(255, 255, 255, 0.25);
}

.navbar .btn-outline-light {
    border-color: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    height: 32px;
    padding: 0 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 4px 4px 0;
}

.navbar .btn-outline-light:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Search animation when shortcut is activated */
.search-active .search-input {
    animation: search-pulse 0.5s ease;
}

@keyframes search-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 0 0 0.15rem rgba(255, 255, 255, 0.5); }
    100% { transform: scale(1); }
}

/* Responsive adjustments for search */
@media (max-width: 768px) {
    .navbar-search-form {
        width: 100%;
        margin: 0.5rem 0;
        order: 1;
    }

    .navbar-search-group {
        width: 100%;
    }

    .navbar .search-input,
    .navbar .search-input:focus {
        width: 100%;
    }
}

/* Notification indicator */
.nav-link .badge {
    font-size: 0.65rem;
}

/* Color coding for different sections */
/* Appointments - Blue */
#appointmentsDropdown i {
    color: #007bff;
}

/* People - Green */
#peopleDropdown i {
    color: #28a745;
}

/* Services - Purple */
#servicesDropdown i {
    color: #6f42c1;
}

/* Programs - Orange */
#programsDropdown i {
    color: #fd7e14;
}

/* Finances - Yellow */
#financesDropdown i {
    color: #ffc107;
}

/* Reports - Teal */
#reportsDropdown i {
    color: #20c997;
}

/* Notifications - Red */
.nav-item .nav-link i.fa-bell {
    color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .navbar-nav .nav-item {
        margin-right: 0;
    }

    .dropdown-menu {
        border: none;
        box-shadow: none;
        padding-left: 1rem;
        min-width: auto;
    }

    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .breadcrumb-wrapper {
        display: none;
    }
}

/* Highlight active navigation item */
.nav-item.active .nav-link,
.nav-item .nav-link.active {
    font-weight: 600;
    position: relative;
}

.nav-item.active .nav-link:after,
.nav-item .nav-link.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0.5rem;
    right: 0.5rem;
    height: 3px;
    background-color: var(--manager-navbar-hover);
    border-radius: 3px 3px 0 0;
}

/* User dropdown styling */
#userDropdown i {
    font-size: 1.2rem;
    margin-right: 0.25rem;
}

/* Language selector styling */
.language-btn {
    font-size: 0.8rem;
}

.language-btn.active {
    font-weight: 600;
}

/* Enhance mobile navigation */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
    }

    .navbar-toggler:focus {
        box-shadow: none;
    }

    .navbar-collapse {
        max-height: 80vh;
        overflow-y: auto;
    }
}
