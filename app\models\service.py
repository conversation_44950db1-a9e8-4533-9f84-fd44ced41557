# app/models/service.py
from datetime import datetime
from app.extensions import db

class Service(db.Model):
    __tablename__ = 'services'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    default_price = db.Column(db.Numeric(10, 2), nullable=False)
    duration_minutes = db.Column(db.Integer, nullable=False)
    is_active = db.Column(db.<PERSON>ole<PERSON>, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    tutor_services = db.relationship('TutorService', backref='service', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Service {self.name}>'

class TutorService(db.Model):
    __tablename__ = 'tutor_services'

    id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.id'), nullable=False)
    tutor_rate = db.Column(db.Numeric(10, 2), nullable=False)  # Rate paid to tutor
    client_rate = db.Column(db.Numeric(10, 2), nullable=False)  # Rate charged to client
    transport_fee = db.Column(db.Numeric(10, 2), nullable=False, default=0.00)  # Transport fee for this service
    transport_fee_description = db.Column(db.Text, nullable=True)  # Description of transport fee
    is_active = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Appointments linked to this tutor-service combination
    appointments = db.relationship('Appointment', lazy='dynamic')

    __table_args__ = (db.UniqueConstraint('tutor_id', 'service_id', name='_tutor_service_uc'),)

    def __repr__(self):
        return f'<TutorService tutor_id={self.tutor_id} service_id={self.service_id}>'

    @property
    def display_name(self):
        """Return a display name that includes transport fee info."""
        service_name = self.service.name if self.service else "Unknown Service"
        base_display = f"{service_name} (${self.client_rate}/hr)"

        if self.transport_fee and float(self.transport_fee) > 0:
            return f"{base_display} + ${self.transport_fee} transport"
        return base_display