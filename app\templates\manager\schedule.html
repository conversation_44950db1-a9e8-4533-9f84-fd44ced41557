<!-- app/templates/manager/schedule.html -->
{% extends "base.html" %}

{% block title %}Schedule - Tutoring Appointment System{% endblock %}

{% block styles %}
<style>
    .calendar-container {
        overflow-x: auto;
    }
    .calendar-header {
        position: sticky;
        top: 0;
        background-color: #fff;
        z-index: 100;
    }
    .time-column {
        width: 80px;
        min-width: 80px;
    }
    .tutor-column {
        width: 200px;
        min-width: 200px;
    }
    .appointment {
        position: absolute;
        width: calc(100% - 10px);
        border-radius: 4px;
        padding: 5px;
        overflow: hidden;
        cursor: pointer;
        color: white;
    }
    .appointment.status-scheduled {
        background-color: #0d6efd;
    }
    .appointment.status-completed {
        background-color: #198754;
    }
    .appointment.status-cancelled {
        background-color: #dc3545;
        text-decoration: line-through;
    }
    .appointment.status-no-show {
        background-color: #fd7e14;
    }
    .appointment.status-awaiting_confirmation {
        background-color: #6f42c1;
        border: 2px solid #ffc107;
    }
    /* Group session styles - pastel colors */
    .appointment.group-session {
        background-color: #e8f5e8;  /* Light pastel green */
        color: #2d5a2d;
        border: 2px solid #90c695;
        font-weight: 500;
    }
    .appointment.group-session.status-scheduled {
        background-color: #e8f5e8;  /* Light pastel green */
        color: #2d5a2d;
    }
    .appointment.group-session.status-completed {
        background-color: #d4edda;  /* Slightly darker green */
        color: #155724;
    }
    .appointment.group-session.status-cancelled {
        background-color: #f8d7da;  /* Light pastel red */
        color: #721c24;
        text-decoration: line-through;
    }
    .appointment.group-session.status-confirmed {
        background-color: #cce7ff;  /* Light pastel blue */
        color: #004085;
    }
    .time-slot {
        height: 60px;
        border-top: 1px solid #ddd;
        position: relative;
    }
    .day-header {
        height: 30px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
    }
    .current-time-indicator {
        position: absolute;
        height: 2px;
        background-color: red;
        width: 100%;
        z-index: 50;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Appointment Schedule</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_appointment') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Appointment
        </a>
    </div>
</div>

<!-- Legend -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body py-2">
                <div class="d-flex align-items-center justify-content-center flex-wrap">
                    <span class="me-3"><strong>Legend:</strong></span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #0d6efd; color: white;">Regular Appointments</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #e8f5e8; color: #2d5a2d; border: 1px solid #90c695;">TECFÉE Group Sessions</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #198754; color: white;">Completed</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #dc3545; color: white;">Cancelled</span>
                    <span class="badge px-3 py-2" style="background-color: #6f42c1; color: white; border: 2px solid #ffc107;">Awaiting Confirmation</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Controls -->
<div class="row mb-4">
    <div class="col-md-4">
        <!-- View Type Selector -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('manager.schedule', view='day', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'day' else '' }}">Day</a>
            <a href="{{ url_for('manager.schedule', view='week', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'week' else '' }}">Week</a>
            <a href="{{ url_for('manager.schedule', view='month', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'month' else '' }}">Month</a>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <!-- Date Navigation -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('manager.schedule', view=view_type, start_date=prev_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i>
            </a>
            <button class="btn btn-outline-secondary" id="current-date-btn">
                {% if view_type == 'day' %}
                    {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'week' %}
                    Week of {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'month' %}
                    {{ start_date.strftime('%B %Y') }}
                {% endif %}
            </button>
            <a href="{{ url_for('manager.schedule', view=view_type, start_date=next_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <!-- Tutor Filter -->
        <select class="form-select" id="tutor-filter">
            <option value="">All Tutors</option>
            {% for tutor in tutors %}
                <option value="{{ tutor.id }}">{{ tutor.full_name }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<!-- Calendar View -->
<div class="card shadow">
    <div class="card-body p-0">
        <div class="calendar-container" id="calendar-container">
            {% if view_type == 'day' or view_type == 'week' %}
                <!-- Day/Week View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="time-column"></th>
                            {% if view_type == 'day' %}
                                {% for tutor in tutors %}
                                    <th class="tutor-column">{{ tutor.full_name }}</th>
                                {% endfor %}
                            {% elif view_type == 'week' %}
                                {% for i in range(7) %}
                                    {% set day = start_date + timedelta(days=i) %}
                                    <th class="text-center">
                                        {{ day.strftime('%a') }}<br>
                                        {{ day.strftime('%m/%d') }}
                                    </th>
                                {% endfor %}
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for hour in range(8, 22) %}
                            <tr>
                                <td class="time-column">
                                    {{ '{0}{1}'.format(hour % 12 if hour % 12 else 12, 'AM' if hour < 12 else 'PM') }}
                                </td>
                                {% if view_type == 'day' %}
                                    {% for tutor in tutors %}
                                        <td class="time-slot" data-tutor-id="{{ tutor.id }}" data-hour="{{ hour }}"></td>
                                    {% endfor %}
                                {% elif view_type == 'week' %}
                                    {% for i in range(7) %}
                                        <td class="time-slot" data-day="{{ i }}" data-hour="{{ hour }}"></td>
                                    {% endfor %}
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% elif view_type == 'month' %}
                <!-- Month View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="text-center">Sunday</th>
                            <th class="text-center">Monday</th>
                            <th class="text-center">Tuesday</th>
                            <th class="text-center">Wednesday</th>
                            <th class="text-center">Thursday</th>
                            <th class="text-center">Friday</th>
                            <th class="text-center">Saturday</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set first_day = start_date %}
                        {% set last_day = next_date - timedelta(days=1) %}
                        {% set first_day_weekday = first_day.weekday() %}
                        {% set days_in_month = (last_day - first_day).days + 1 %}
                        {% set total_weeks = (days_in_month + first_day_weekday + 6) // 7 %}

                        {% for week in range(total_weeks) %}
                            <tr style="height: 120px;">
                                {% for weekday in range(7) %}
                                    {% set day_offset = week * 7 + weekday - first_day_weekday %}
                                    {% set current_day = first_day + timedelta(days=day_offset) %}

                                    {% if current_day.month == first_day.month %}
                                        <td class="position-relative" data-date="{{ current_day.strftime('%Y-%m-%d') }}">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="fw-bold">{{ current_day.day }}</span>
                                                <a href="{{ url_for('manager.schedule', view='day', start_date=current_day.strftime('%Y-%m-%d')) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-calendar-day"></i>
                                                </a>
                                            </div>
                                            <div class="appointment-container" style="height: 90px; overflow-y: auto;"></div>
                                        </td>
                                    {% else %}
                                        <td class="bg-light text-muted">
                                            <span class="fw-light">{{ current_day.day }}</span>
                                        </td>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Appointment Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div id="appointmentDetails" style="display: none;">
                    <div class="mb-3">
                        <label class="fw-bold">Student:</label>
                        <span id="appointmentStudent"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">Tutor:</label>
                        <span id="appointmentTutor"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">Service:</label>
                        <span id="appointmentService"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">Date & Time:</label>
                        <span id="appointmentDateTime"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">Status:</label>
                        <span id="appointmentStatus"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">Notes:</label>
                        <p id="appointmentNotes" class="text-muted"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer flex-column">
                <!-- Status-specific action buttons -->
                <div class="w-100 mb-2" id="statusActionButtons">
                    <!-- Buttons will be dynamically populated based on appointment status -->
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <div class="btn-group">
                        <a href="#" id="editAppointmentBtn" class="btn btn-primary">Edit</a>
                        <a href="#" id="viewAppointmentBtn" class="btn btn-info">View</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Date Picker Modal -->
<div class="modal fade" id="datePickerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Select Date</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="date" id="datePicker" class="form-control">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="goToDateBtn">Go to Date</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from backend
        const calendarData = {{ calendar_data | tojson }};
        const viewType = '{{ view_type }}';

        // Populate calendar with appointments
        renderCalendar(calendarData);

        // Show appointment modal when clicking on an appointment
        document.querySelectorAll('.appointment').forEach(function(appointment) {
            appointment.addEventListener('click', function() {
                const appointmentId = this.dataset.id;
                const appointmentType = this.dataset.type;

                if (appointmentType === 'group_session') {
                    // Redirect to group session details page
                    const groupSessionId = appointmentId.replace('group_', '');
                    window.location.href = `/manager/tecfee/group-sessions/${groupSessionId}`;
                } else {
                    showAppointmentDetails(appointmentId);
                }
            });
        });

        // Date picker functionality
        document.getElementById('current-date-btn').addEventListener('click', function() {
            const datePickerModal = new bootstrap.Modal(document.getElementById('datePickerModal'));
            datePickerModal.show();
        });

        document.getElementById('goToDateBtn').addEventListener('click', function() {
            const date = document.getElementById('datePicker').value;
            if (date) {
                window.location.href = "{{ url_for('manager.schedule', view=view_type) }}" + "&start_date=" + date;
            }
        });

        // Tutor filter functionality
        document.getElementById('tutor-filter').addEventListener('change', function() {
            const tutorId = this.value;
            filterAppointmentsByTutor(tutorId);
        });

        // Mark current time on the calendar
        if (viewType === 'day') {
            markCurrentTime();
        }
    });

    function renderCalendar(data) {
        const appointments = data.appointments;
        const viewType = data.view_type;

        // Debug logging
        console.log('Calendar data:', data);
        console.log('Appointments:', appointments);

        if (viewType === 'day' || viewType === 'week') {
            appointments.forEach(function(appointment) {
                // Debug each appointment
                console.log('Rendering appointment:', appointment);
                console.log('Student name:', appointment.student_name);
                console.log('Client name:', appointment.client_name);
                const startTime = new Date(appointment.start_time);
                const endTime = new Date(appointment.end_time);

                // Calculate position and height
                const startHour = startTime.getHours() + startTime.getMinutes() / 60;
                const endHour = endTime.getHours() + endTime.getMinutes() / 60;
                const duration = endHour - startHour;

                // Create appointment element
                const appointmentEl = document.createElement('div');
                const isGroupSession = appointment.type === 'group_session';
                appointmentEl.className = `appointment status-${appointment.status}${isGroupSession ? ' group-session' : ''}`;
                appointmentEl.dataset.id = appointment.id;
                appointmentEl.dataset.type = appointment.type || 'appointment';

                if (isGroupSession) {
                    appointmentEl.innerHTML = `
                        <div class="fw-bold">${appointment.client_name}</div>
                        <div class="small">${appointment.student_name}</div>
                        <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                    `;
                } else {
                    appointmentEl.innerHTML = `
                        <div class="fw-bold">${appointment.student_name}</div>
                        <div class="small">${appointment.service_name}</div>
                        <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                    `;
                }

                if (viewType === 'day') {
                    // Place in tutor column
                    const tutorCells = document.querySelectorAll(`td[data-tutor-id="${appointment.tutor_id}"]`);
                    let targetCell = null;

                    for (let i = 0; i < tutorCells.length; i++) {
                        const cellHour = parseInt(tutorCells[i].dataset.hour);
                        if (cellHour === Math.floor(startHour)) {
                            targetCell = tutorCells[i];
                            break;
                        }
                    }

                    if (targetCell) {
                        appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                        appointmentEl.style.height = `${duration * 60}px`;
                        targetCell.appendChild(appointmentEl);
                    }
                } else if (viewType === 'week') {
                    // Place in day column
                    const dayOfWeek = startTime.getDay();
                    const dayCells = document.querySelectorAll(`td[data-day="${dayOfWeek}"]`);
                    let targetCell = null;

                    for (let i = 0; i < dayCells.length; i++) {
                        const cellHour = parseInt(dayCells[i].dataset.hour);
                        if (cellHour === Math.floor(startHour)) {
                            targetCell = dayCells[i];
                            break;
                        }
                    }

                    if (targetCell) {
                        appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                        appointmentEl.style.height = `${duration * 60}px`;
                        targetCell.appendChild(appointmentEl);
                    }
                }
            });
        } else if (viewType === 'month') {
            appointments.forEach(function(appointment) {
                const startTime = new Date(appointment.start_time);
                const dateStr = startTime.toISOString().split('T')[0];
                const cell = document.querySelector(`td[data-date="${dateStr}"]`);

                if (cell) {
                    const container = cell.querySelector('.appointment-container');
                    const appointmentEl = document.createElement('div');
                    const isGroupSession = appointment.type === 'group_session';
                    appointmentEl.className = `p-1 mb-1 rounded status-${appointment.status}${isGroupSession ? ' group-session' : ''}`;
                    appointmentEl.dataset.id = appointment.id;
                    appointmentEl.dataset.type = appointment.type || 'appointment';

                    if (isGroupSession) {
                        appointmentEl.innerHTML = `
                            <small>${formatTime(startTime)}: ${appointment.client_name}</small>
                        `;
                    } else {
                        appointmentEl.innerHTML = `
                            <small>${formatTime(startTime)}: ${appointment.student_name}</small>
                        `;
                    }
                    container.appendChild(appointmentEl);
                }
            });
        }
    }

    function showAppointmentDetails(appointmentId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
        modal.show();

        // Hide details, show spinner
        document.getElementById('appointmentDetails').style.display = 'none';
        document.querySelector('#appointmentModal .spinner-border').style.display = 'block';

        // Set up edit link
        document.getElementById('editAppointmentBtn').href = `/manager/appointments/${appointmentId}/edit`;

        // Set up view link
        document.getElementById('viewAppointmentBtn').href = `/manager/appointments/${appointmentId}`;

        // Status action buttons will be set up after fetching appointment data

        // Fetch appointment details
        fetch(`/api/appointment/${appointmentId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('appointmentStudent').textContent = data.student.name;
                document.getElementById('appointmentTutor').textContent = data.tutor.name;
                document.getElementById('appointmentService').textContent = data.service.name;
                document.getElementById('appointmentDateTime').textContent =
                    `${formatDateTime(new Date(data.start_time))} - ${formatTime(new Date(data.end_time))}`;

                // Format status
                const statusEl = document.getElementById('appointmentStatus');
                statusEl.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                statusEl.className = '';
                statusEl.classList.add(`text-${getStatusColor(data.status)}`);

                // Notes
                document.getElementById('appointmentNotes').textContent = data.notes || 'No notes available';

                // Hide spinner, show details
                document.querySelector('#appointmentModal .spinner-border').style.display = 'none';
                document.getElementById('appointmentDetails').style.display = 'block';

                // Populate status action buttons based on appointment status
                populateStatusActionButtons(data.status, appointmentId);
            })
            .catch(error => {
                console.error('Error fetching appointment details:', error);
                document.querySelector('#appointmentModal .spinner-border').style.display = 'none';
                document.getElementById('appointmentDetails').innerHTML = '<div class="alert alert-danger">Error loading appointment details.</div>';
            });
    }

    function populateStatusActionButtons(status, appointmentId) {
        const container = document.getElementById('statusActionButtons');
        container.innerHTML = ''; // Clear existing buttons

        if (status === 'scheduled') {
            // Scheduled appointments: Mark Awaiting Confirmation + Cancel
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-warning btn-lg me-2 mb-2" onclick="quickStatusChange('${appointmentId}', 'awaiting_confirmation')">
                        <i class="fas fa-clock"></i> Mark Awaiting Confirmation
                    </button>
                    <button type="button" class="btn btn-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> Cancel
                    </button>
                </div>
            `;
        } else if (status === 'awaiting_confirmation' || status === 'awaiting_confirm') {
            // Awaiting confirmation: Mark Completed + Cancel
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-success btn-lg me-2 mb-2" onclick="quickStatusChange('${appointmentId}', 'completed')">
                        <i class="fas fa-check-circle"></i> Mark Completed
                    </button>
                    <button type="button" class="btn btn-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> Cancel
                    </button>
                </div>
            `;
        } else if (status === 'completed') {
            // Completed appointments: Cancel only
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-outline-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> Cancel
                    </button>
                </div>
            `;
        } else if (status === 'cancelled' || status === 'no-show') {
            // Cancelled/No-show appointments: No action buttons
            container.innerHTML = `
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle"></i> This appointment has been ${status === 'no-show' ? 'marked as no-show' : 'cancelled'}.
                </div>
            `;
        }
    }

    function quickStatusChange(appointmentId, newStatus) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/manager/appointments/${appointmentId}/quick-status-change`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = getCsrfToken();
        form.appendChild(csrfInput);

        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        form.appendChild(statusInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }



    function filterAppointmentsByTutor(tutorId) {
        if (tutorId) {
            // Hide all appointments not from this tutor
            document.querySelectorAll('.appointment').forEach(function(appointment) {
                const appointmentData = calendarData.appointments.find(a => a.id == appointment.dataset.id);
                if (appointmentData && appointmentData.tutor_id != tutorId) {
                    appointment.style.display = 'none';
                } else {
                    appointment.style.display = 'block';
                }
            });
        } else {
            // Show all appointments
            document.querySelectorAll('.appointment').forEach(function(appointment) {
                appointment.style.display = 'block';
            });
        }
    }

    function markCurrentTime() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinutes = now.getMinutes();

        // Only mark time if within business hours (8AM-9PM)
        if (currentHour >= 8 && currentHour < 21) {
            const hourCells = document.querySelectorAll(`td[data-hour="${currentHour}"]`);

            hourCells.forEach(function(cell) {
                const indicator = document.createElement('div');
                indicator.className = 'current-time-indicator';
                indicator.style.top = `${currentMinutes}px`;
                cell.appendChild(indicator);
            });
        }
    }

    function formatTime(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    function formatDateTime(date) {
        return date.toLocaleDateString() + ' ' + formatTime(date);
    }

    function getStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'scheduled': return 'primary';
            case 'cancelled': return 'danger';
            case 'no-show': return 'warning';
            case 'awaiting_confirmation': return 'info';
            default: return 'secondary';
        }
    }

    function getCsrfToken() {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (!csrfMeta) {
            console.error('CSRF token meta tag not found');
            return '';
        }
        const token = csrfMeta.getAttribute('content');
        if (!token) {
            console.error('CSRF token content is empty');
            return '';
        }
        return token;
    }
</script>
{% endblock %}