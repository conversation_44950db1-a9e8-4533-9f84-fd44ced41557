{% extends "base.html" %}

{% block title %}Mes Sessions TECFÉE - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-users text-primary"></i>
                Mes Sessions TECFÉE
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('client.dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active">Sessions TECFÉE</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('client.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Retour au tableau de bord
            </a>
        </div>
    </div>

    <!-- Enrollment Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap"></i>
                        Inscription au Programme TECFÉE
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Programme:</strong> {{ program.name }}</p>
                            <p class="mb-1"><strong>Type de tarification:</strong> 
                                {% if enrollment.pricing_type == 'per_session' %}
                                    Par module (44,99$ par session)
                                {% else %}
                                    Forfait complet (399,00$ pour tous les modules)
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>Date d'inscription:</strong> {{ enrollment.enrollment_date.strftime('%d/%m/%Y') }}</p>
                            <p class="mb-1"><strong>Statut:</strong> 
                                <span class="badge bg-success">{{ enrollment.status | title }}</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Sessions -->
    {% if upcoming_sessions %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt text-success"></i>
                        Prochaines Sessions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for session_data in upcoming_sessions %}
                        {% set session = session_data[0] %}
                        {% set participant = session_data[1] %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-success h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            {% if session.module %}
                                            Module {{ session.module.module_order }}: {{ session.module.name }}
                                            {% else %}
                                            Session TECFÉE
                                            {% endif %}
                                        </h6>
                                        <span class="badge bg-{{ 'success' if session.status == 'confirmed' else 'warning' }}">
                                            {{ session.status | title }}
                                        </span>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar"></i> {{ session.session_date.strftime('%A, %d %B %Y') }}
                                        </small><br>
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> {{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}
                                        </small>
                                    </div>

                                    {% if session.meeting_link %}
                                    <div class="mb-2">
                                        <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-video"></i> Rejoindre la session
                                        </a>
                                    </div>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-users"></i> 
                                            {{ session.current_participants }}/{{ session.max_participants }} participants
                                        </small>
                                        {% if participant.attendance_status %}
                                        <small class="badge bg-info">
                                            {{ participant.attendance_status | title }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- All Sessions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>
                        Toutes mes Sessions
                    </h5>
                </div>
                <div class="card-body">
                    {% if all_sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Module</th>
                                    <th>Date & Heure</th>
                                    <th>Statut</th>
                                    <th>Présence</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session_data in all_sessions %}
                                {% set session = session_data[0] %}
                                {% set participant = session_data[1] %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if session.module %}
                                            <div class="badge bg-primary me-2">{{ session.module.module_order }}</div>
                                            <div>
                                                <strong>{{ session.module.name }}</strong><br>
                                                <small class="text-muted">{{ session.module.duration_minutes }} minutes</small>
                                            </div>
                                            {% else %}
                                            <div>
                                                <strong>Session TECFÉE</strong><br>
                                                <small class="text-muted">60 minutes</small>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ session.session_date.strftime('%d/%m/%Y') }}</strong><br>
                                        <small class="text-muted">{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if session.status == 'confirmed' else 'warning' if session.status == 'scheduled' else 'secondary' }}">
                                            {{ session.status | title }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if participant.attendance_status %}
                                        <span class="badge bg-{{ 'success' if participant.attendance_status == 'attended' else 'danger' if participant.attendance_status == 'absent' else 'secondary' }}">
                                            {% if participant.attendance_status == 'attended' %}
                                                Présent
                                            {% elif participant.attendance_status == 'absent' %}
                                                Absent
                                            {% else %}
                                                {{ participant.attendance_status | title }}
                                            {% endif %}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if session.meeting_link and session.session_date >= today() and session.status in ['scheduled', 'confirmed'] %}
                                        <a href="{{ session.meeting_link }}" target="_blank" class="btn btn-sm btn-primary">
                                            <i class="fas fa-video"></i> Rejoindre
                                        </a>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune session trouvée</h5>
                        <p class="text-muted">Vous n'êtes inscrit à aucune session pour le moment.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
