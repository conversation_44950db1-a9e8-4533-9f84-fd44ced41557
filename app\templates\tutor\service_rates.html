{% extends 'base.html' %}

{% block title %}Manage Service Rates{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Manage Your Service Rates</h1>
    <p class="lead">Set your rates for each service you provide.</p>

    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> Note: Your tutor rate (salary) is set by management. You can only set the client rate and transport fees.
    </div>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Your Current Service Rates</h5>
                </div>
                <div class="card-body">
                    {% if rates %}
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Your Rate</th>
                                    <th>Client Rate</th>
                                    <th>Transport Fee</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rate in rates %}
                                <tr>
                                    <td>{{ rate.service.name }}</td>
                                    <td>
                                        {% if rate.tutor_rate > 0 %}
                                            ${{ rate.tutor_rate }}
                                        {% else %}
                                            <span class="text-muted">Set by management</span>
                                        {% endif %}
                                    </td>
                                    <td>${{ rate.client_rate }}</td>
                                    <td>
                                        {% if rate.transport_fee and rate.transport_fee > 0 %}
                                            ${{ rate.transport_fee }}
                                            {% if rate.transport_fee_description %}
                                                <small class="d-block text-muted">{{ rate.transport_fee_description }}</small>
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rate.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('tutor.edit_service_rate', id=rate.id) }}" class="btn btn-sm btn-outline-primary">Edit</a>
                                        <form method="POST" action="{{ url_for('tutor.delete_service_rate', id=rate.id) }}" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this service rate?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="alert alert-info">
                            You haven't set any service rates yet. Use the form to add your rates for services you provide.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Add New Service Rate</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('tutor.service_rates') }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.service_id.label(class="form-label") }}
                            {{ form.service_id(class="form-select") }}
                        </div>
                        <div class="mb-3">
                            {{ form.tutor_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate(class="form-control", readonly=true, value="Set by management") }}
                            </div>
                            <div class="form-text">Tutor rate is set by management.</div>
                        </div>
                        <div class="mb-3">
                            {{ form.client_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.client_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.transport_fee(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee_description.label(class="form-label") }}
                            {{ form.transport_fee_description(class="form-control") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Add Service Rate</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
