<!-- app/templates/manager/dashboard.html -->
{% extends "base.html" %}

{% block title %}Manager Dashboard - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">Manager Dashboard</h2>
        <p class="text-muted">Welcome to the tutoring appointment management system.</p>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-white bg-success h-100">
            <div class="card-body">
                <h5 class="card-title">Clients</h5>
                <p class="card-text display-4">{{ clients_count }}</p>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Total Clients</small>
                <a href="{{ url_for('manager.clients_list') }}" class="btn btn-sm btn-light">View All</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-info h-100">
            <div class="card-body">
                <h5 class="card-title">Tutors</h5>
                <p class="card-text display-4">{{ tutors_count }}</p>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Active Tutors</small>
                <a href="{{ url_for('manager.tutors_list') }}" class="btn btn-sm btn-light">View All</a>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card text-white bg-warning h-100">
            <div class="card-body">
                <h5 class="card-title">Schedule</h5>
                <p class="card-text">
                    <i class="fas fa-calendar-alt fa-3x"></i>
                </p>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <small>Appointment Schedule</small>
                <a href="{{ url_for('manager.schedule') }}" class="btn btn-sm btn-light">View Schedule</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Upcoming Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0">Upcoming Appointments</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Client</th>
                                    <th>Tutor</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            {% set tutor = appointment.tutor %}
                                            {{ tutor.first_name }} {{ tutor.last_name }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.schedule') }}" class="btn btn-outline-primary">View All Appointments</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No upcoming appointments in the next 7 days.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Unpaid Invoices -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0">Unpaid Invoices</h5>
            </div>
            <div class="card-body">
                {% if unpaid_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Invoice #</th>
                                    <th>Client</th>
                                    <th>Amount</th>
                                    <th>Due Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in unpaid_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>
                                            {% set client = invoice.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <a href="{{ url_for('manager.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.invoices_list') }}" class="btn btn-outline-primary">View All Invoices</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No unpaid invoices.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Past Appointments Needing Update -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">Past Appointments Needing Update</h5>
            </div>
            <div class="card-body">
                {% if past_appointments_needing_update %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Client</th>
                                    <th>Tutor</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments_needing_update %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            {% set tutor = appointment.tutor %}
                                            {{ tutor.first_name }} {{ tutor.last_name }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Update
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No past appointments need updating.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pending Time-Off Requests -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Pending Time-Off Requests</h5>
            </div>
            <div class="card-body">
                {% if pending_time_off_requests %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Tutor</th>
                                    <th>Dates</th>
                                    <th>Duration</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_time_off_requests %}
                                    <tr>
                                        <td>{{ request.tutor.first_name }} {{ request.tutor.last_name }}</td>
                                        <td>{{ request.start_date.strftime('%Y-%m-%d') }} to {{ request.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ request.duration_days }} day{% if request.duration_days != 1 %}s{% endif %}</td>
                                        <td>
                                            <a href="{{ url_for('manager.view_time_off_request', id=request.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> Review
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.time_off_requests') }}" class="btn btn-outline-primary">View All Requests</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No pending time-off requests.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_appointment') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-calendar-plus mb-2 fa-2x"></i>
                            <span>New Appointment</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_client') }}" class="btn btn-lg btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-user-plus mb-2 fa-2x"></i>
                            <span>New Client</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-lg btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-chalkboard-teacher mb-2 fa-2x"></i>
                            <span>New Tutor</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.generate_invoices') }}" class="btn btn-lg btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-file-invoice-dollar mb-2 fa-2x"></i>
                            <span>Generate Invoices</span>
                        </a>
                    </div>
                </div>

                <!-- Add Subscription Actions -->
                <div class="row text-center mt-3">
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('manager.new_subscription_plan') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-tag mb-2 fa-2x"></i>
                            <span>Create Subscription Plan</span>
                        </a>
                    </div>
                    <div class="col-md-6 mb-3">
                        <a href="{{ url_for('manager.new_subscription') }}" class="btn btn-lg btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-calendar-check mb-2 fa-2x"></i>
                            <span>Assign Subscription to Client</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}