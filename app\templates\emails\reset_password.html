<!-- app/templates/emails/reset_password.html -->
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #dddddd;
            border-radius: 5px;
        }
        .header {
            background-color: #0d6efd;
            padding: 10px 20px;
            color: white;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
        }
        .button {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            font-size: 12px;
            color: #777777;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #dddddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Password Reset Request</h2>
        </div>
        <div class="content">
            <p>Dear {{ user.email }},</p>
            
            <p>You recently requested to reset your password for your Tutoring Appointment System account. Click the button below to reset it:</p>
            
            <p>
                <a href="{{ reset_url }}" class="button">Reset Your Password</a>
            </p>
            
            <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
            
            <p>This password reset link is only valid for 24 hours.</p>
            
            <p>Best regards,<br>
            Tutoring Appointment System Team</p>
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply directly to this email.</p>
        </div>
    </div>
</body>
</html>