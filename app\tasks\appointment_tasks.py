# app/tasks/appointment_tasks.py
from app.services.appointment_service import AppointmentService
from app.extensions import db
from app.models.appointment import Appointment
from app.utils.email import send_attendance_confirmation_email
from flask import current_app
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

def generate_recurring_appointments():
    """Generate upcoming recurring appointments.

    This task should be scheduled to run daily to ensure future appointments
    are generated in advance.
    """
    logger.info("Starting recurring appointment generation task")

    # Generate appointments for the next 30 days
    try:
        appointments_created = AppointmentService.generate_future_appointments(days=30)
        logger.info(f"Successfully generated {appointments_created} recurring appointments")
    except Exception as e:
        logger.error(f"Error generating recurring appointments: {str(e)}")

    logger.info("Completed recurring appointment generation task")

    return appointments_created

def update_past_appointments():
    """
    Update the status of appointments that have passed but are still marked as 'scheduled'.
    This should be run periodically (e.g., hourly) to ensure appointments are properly marked
    for attendance confirmation.
    """
    try:
        # Find appointments that ended in the past but are still marked as scheduled
        now = datetime.now()
        past_appointments = Appointment.query.filter(
            Appointment.end_time < now,
            Appointment.status == 'scheduled'
        ).all()

        count = 0
        for appointment in past_appointments:
            # Update status to awaiting confirmation
            appointment.status = 'awaiting_confirmation'

            # Send email to tutor for confirmation
            try:
                send_attendance_confirmation_email(appointment)
            except Exception as e:
                logger.error(f"Failed to send attendance confirmation email for appointment {appointment.id}: {str(e)}")

            count += 1

        if count > 0:
            db.session.commit()
            logger.info(f"Updated {count} past appointments to 'awaiting_confirmation' status")

        return count
    except Exception as e:
        logger.error(f"Error updating past appointments: {str(e)}")
        return 0

def remind_pending_confirmations():
    """
    Send reminder emails for appointments that are still awaiting confirmation
    after 24 hours. This should be run daily.
    """
    try:
        # Find appointments that are awaiting confirmation for more than 24 hours
        now = datetime.now()
        cutoff_time = now - timedelta(hours=24)

        pending_appointments = Appointment.query.filter(
            Appointment.end_time < cutoff_time,
            Appointment.status == 'awaiting_confirmation'
        ).all()

        count = 0
        for appointment in pending_appointments:
            # Send reminder email to tutor
            try:
                send_attendance_confirmation_email(appointment)
                count += 1
            except Exception as e:
                logger.error(f"Failed to send reminder email for appointment {appointment.id}: {str(e)}")

        logger.info(f"Sent {count} reminder emails for pending confirmations")
        return count
    except Exception as e:
        logger.error(f"Error sending confirmation reminders: {str(e)}")
        return 0