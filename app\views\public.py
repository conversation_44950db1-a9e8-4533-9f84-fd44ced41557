# app/views/public.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_required, current_user
from datetime import datetime
from app.extensions import db
from app.models.client import Client
from app.models.program import Program, ProgramPricing, Enrollment
from app.services.group_session_service import GroupSessionService
from app.services.user_service import UserService
from app.services.invoice_service import InvoiceService
from app.forms.prospect_forms import TecfeeClientRegistrationForm

public_bp = Blueprint('public', __name__)

@public_bp.route('/')
def index():
    """Redirect to main website."""
    return redirect('https://www.tutoraide.ca')

@public_bp.route('/tecfee')
def tecfee():
    """TECFÉE program landing page."""
    # Get TECFÉE program and pricing
    tecfee_program = GroupSessionService.get_tecfee_program()
    pricing_options = []

    if tecfee_program:
        pricing_options = GroupSessionService.get_program_pricing(tecfee_program.id)

    return render_template('public/tecfee.html',
                         program=tecfee_program,
                         pricing_options=pricing_options)

@public_bp.route('/tecfee/enrollment')
@public_bp.route('/tecfee/enrollment/<pricing>')
def tecfee_enrollment(pricing=None):
    """TECFÉE enrollment page for prospects and clients."""
    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('public.index'))

    # Get pricing options
    pricing_options = GroupSessionService.get_program_pricing(tecfee_program.id)

    # Set default pricing if specified in URL
    selected_pricing = None
    if pricing:
        selected_pricing = next((p for p in pricing_options if p.pricing_type == pricing), None)

    # Create client registration form for non-authenticated users
    client_form = TecfeeClientRegistrationForm()
    if pricing:
        client_form.pricing_preference.data = pricing

    return render_template('public/tecfee_enrollment.html',
                         program=tecfee_program,
                         pricing_options=pricing_options,
                         selected_pricing=selected_pricing,
                         client_form=client_form)

@public_bp.route('/tecfee/client-register', methods=['POST'])
def tecfee_client_register():
    """Handle direct client registration for TECFÉE program."""
    form = TecfeeClientRegistrationForm()

    if form.validate_on_submit():
        try:
            # Create client account
            success, client, message = UserService.create_client(
                email=form.email.data,
                password=form.password.data,
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                phone=form.phone.data,
                client_type='individual'
            )

            if not success:
                flash(f'Erreur lors de la création du compte: {message}', 'error')
                return redirect(url_for('public.tecfee_enrollment'))

            # Update client with structured address fields
            client.civic_number = form.civic_number.data
            client.street = form.street.data
            client.city = form.city.data
            client.postal_code = form.postal_code.data
            client.province = form.province.data
            client.country = form.country.data

            # Add notes if provided
            if form.notes.data:
                client.notes = form.notes.data

            db.session.commit()

            # Get TECFÉE program
            tecfee_program = GroupSessionService.get_tecfee_program()
            if not tecfee_program:
                flash('Programme TECFÉE non trouvé.', 'error')
                return redirect(url_for('public.index'))

            # Get pricing
            pricing = ProgramPricing.query.filter_by(
                program_id=tecfee_program.id,
                pricing_type=form.pricing_preference.data,
                is_active=True
            ).first()

            if not pricing:
                flash('Option de tarification invalide.', 'error')
                return redirect(url_for('public.tecfee_enrollment'))

            # Create enrollment
            enrollment, enrollment_message = GroupSessionService.enroll_client_in_tecfee(
                client_id=client.id,
                pricing_type=form.pricing_preference.data
            )

            if not enrollment:
                flash(f'Erreur lors de l\'inscription: {enrollment_message}', 'error')
                return redirect(url_for('public.tecfee_enrollment'))

            # Generate invoice for the new client (initial amount, will be updated after session selection)
            initial_amount = float(pricing.price) if form.pricing_preference.data == 'full_package' else 44.99
            invoice = InvoiceService.generate_invoice_for_tecfee_enrollment(
                client_id=client.id,
                pricing_type=form.pricing_preference.data,
                amount=initial_amount
            )

            if not invoice:
                flash('Erreur lors de la génération de la facture.', 'error')
                return redirect(url_for('public.tecfee_enrollment'))

            # Store enrollment details in session for payment processing
            session['tecfee_enrollment'] = {
                'client_id': client.id,
                'program_id': tecfee_program.id,
                'pricing_type': form.pricing_preference.data,
                'price': float(pricing.price),
                'invoice_id': invoice.id
            }

            flash('Compte créé avec succès! Veuillez vérifier votre courriel pour continuer.', 'success')

            # Redirect to email verification pending page instead of session selection
            # User must verify email before proceeding to session selection
            return render_template('auth/email_verification_pending.html')

        except Exception as e:
            db.session.rollback()
            flash(f'Erreur lors de l\'inscription: {str(e)}', 'error')
            return redirect(url_for('public.tecfee_enrollment'))
    else:
        # Form validation failed
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'Erreur dans {field}: {error}', 'error')

    # Redirect back to enrollment page with errors
    return redirect(url_for('public.tecfee_enrollment'))


@public_bp.route('/tecfee/session-selection')
def tecfee_session_selection():
    """TECFÉE session selection page for per-session pricing."""
    # Get enrollment details from session
    enrollment_data = session.get('tecfee_enrollment')
    if not enrollment_data:
        flash('Aucune donnée d\'inscription trouvée. Veuillez recommencer le processus d\'inscription.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Both pricing types can access this page now
    # Per-session: select any number of sessions
    # Full package: must select exactly 12 sessions (1 of each module)

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('Programme TECFÉE non trouvé.', 'error')
        return redirect(url_for('public.index'))

    # Get available sessions
    available_sessions = GroupSessionService.get_available_sessions_for_program(tecfee_program.id)

    return render_template('public/tecfee_session_selection.html',
                         program=tecfee_program,
                         available_sessions=available_sessions,
                         enrollment_data=enrollment_data)


@public_bp.route('/tecfee/session-selection/process', methods=['POST'])
def process_session_selection():
    """Process selected sessions for per-session pricing."""
    # Get enrollment details from session
    enrollment_data = session.get('tecfee_enrollment')
    if not enrollment_data:
        flash('Aucune donnée d\'inscription trouvée.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Get selected sessions based on pricing type
    pricing_type = enrollment_data.get('pricing_type')
    selected_session_ids = []

    if pricing_type == 'full_package':
        # For full package, collect sessions from radio buttons (one per module)
        for i in range(1, 13):  # Modules 1-12
            module_session = request.form.get(f'module_{i}_session')
            if module_session:
                selected_session_ids.append(module_session)

        # Validate exactly 12 sessions selected (one per module)
        if len(selected_session_ids) != 12:
            flash(f'Vous devez sélectionner exactement 12 sessions (1 de chaque module). Vous en avez sélectionné {len(selected_session_ids)}.', 'error')
            return redirect(url_for('public.tecfee_session_selection'))
    else:
        # For per-session, get from checkboxes
        selected_session_ids = request.form.getlist('selected_sessions')
        if not selected_session_ids:
            flash('Veuillez sélectionner au moins une session.', 'error')
            return redirect(url_for('public.tecfee_session_selection'))

    try:
        # Convert to integers and validate sessions exist
        session_ids = [int(sid) for sid in selected_session_ids]

        # Get TECFÉE program
        tecfee_program = GroupSessionService.get_tecfee_program()
        available_sessions = GroupSessionService.get_available_sessions_for_program(tecfee_program.id)
        available_session_ids = [s.id for s in available_sessions]

        # Validate all selected sessions are available
        invalid_sessions = [sid for sid in session_ids if sid not in available_session_ids]
        if invalid_sessions:
            flash('Certaines sessions sélectionnées ne sont plus disponibles.', 'error')
            return redirect(url_for('public.tecfee_session_selection'))

        # For full package, validate one session per module
        if pricing_type == 'full_package':
            # Get modules for selected sessions
            selected_modules = set()
            for session_id in session_ids:
                session_obj = next((s for s in available_sessions if s.id == session_id), None)
                if session_obj and session_obj.module:
                    selected_modules.add(session_obj.module.module_order)

            if len(selected_modules) != 12:
                flash('Vous devez sélectionner exactement 1 session de chaque module (12 modules au total).', 'error')
                return redirect(url_for('public.tecfee_session_selection'))

            # Set fixed price for full package
            total_amount = 399.00
            flash(f'12 sessions sélectionnées (forfait complet). Total: {total_amount:.2f}$', 'success')
        else:
            # Calculate total amount for per-session
            total_amount = len(session_ids) * 44.99
            flash(f'{len(session_ids)} session(s) sélectionnée(s). Total: {total_amount:.2f}$', 'success')

        # Store selected sessions in session data
        enrollment_data['selected_sessions'] = session_ids
        enrollment_data['total_amount'] = total_amount
        enrollment_data['price'] = total_amount  # Update price for selected sessions
        session['tecfee_enrollment'] = enrollment_data

        return redirect(url_for('public.tecfee_payment'))

    except (ValueError, TypeError) as e:
        flash('Erreur dans la sélection des sessions.', 'error')
        return redirect(url_for('public.tecfee_session_selection'))


@public_bp.route('/tecfee/enroll', methods=['POST'])
@login_required
def process_tecfee_enrollment():
    """Process TECFÉE enrollment."""
    if current_user.role != 'client':
        flash('Only clients can enroll in programs.', 'error')
        return redirect(url_for('public.tecfee'))

    # Get client
    client = Client.query.filter_by(user_id=current_user.id).first()
    if not client:
        flash('Client profile not found.', 'error')
        return redirect(url_for('public.tecfee'))

    pricing_type = request.form.get('pricing_type')
    if not pricing_type:
        flash('Please select a pricing option.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('public.index'))

    # Check if client is already enrolled
    existing_enrollment = Enrollment.query.filter_by(
        client_id=client.id,
        program_id=tecfee_program.id,
        status='active'
    ).first()

    if existing_enrollment:
        flash('You are already enrolled in the TECFÉE program.', 'warning')
        return redirect(url_for('client.dashboard'))

    # Get pricing
    pricing = ProgramPricing.query.filter_by(
        program_id=tecfee_program.id,
        pricing_type=pricing_type,
        is_active=True
    ).first()

    if not pricing:
        flash('Invalid pricing option selected.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Store enrollment details in session for payment processing
    session['tecfee_enrollment'] = {
        'client_id': client.id,
        'program_id': tecfee_program.id,
        'pricing_type': pricing_type,
        'price': float(pricing.price)
    }

    # Redirect to session selection for both pricing types
    return redirect(url_for('public.tecfee_session_selection'))

@public_bp.route('/tecfee/google-profile-completion/<int:client_id>')
@login_required
def tecfee_google_profile_completion(client_id):
    """Complete Google user profile for TECFÉE enrollment."""
    # Verify user owns this client
    if current_user.role != 'client':
        flash('Access denied.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()
    if not client:
        flash('Client profile not found.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Verify this is a Google user
    if current_user.auth_provider != 'google':
        flash('This page is only for Google sign-in users.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    return render_template('public/tecfee_google_profile_completion.html', client=client)

@public_bp.route('/tecfee/google-profile-completion/process', methods=['POST'])
@login_required
def process_google_profile_completion():
    """Process Google profile completion and proceed to session selection."""
    if current_user.role != 'client':
        flash('Access denied.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    client_id = request.form.get('client_id')
    client = Client.query.filter_by(id=client_id, user_id=current_user.id).first()

    if not client:
        flash('Client profile not found.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    try:
        # Update client profile with form data
        client.phone = request.form.get('phone', '')
        client.civic_number = request.form.get('civic_number', '')
        client.street = request.form.get('street', '')
        client.city = request.form.get('city', '')
        client.postal_code = request.form.get('postal_code', '')
        client.province = request.form.get('province', 'Quebec')
        client.country = request.form.get('country', 'Canada')

        # Optional fields
        date_of_birth = request.form.get('date_of_birth')
        if date_of_birth:
            from datetime import datetime
            client.date_of_birth = datetime.strptime(date_of_birth, '%Y-%m-%d').date()

        notes = request.form.get('notes', '')
        if notes:
            client.notes = notes

        # Get pricing type
        pricing_type = request.form.get('pricing_type', 'per_session')

        # Get TECFÉE program
        tecfee_program = GroupSessionService.get_tecfee_program()
        if not tecfee_program:
            flash('Programme TECFÉE non trouvé.', 'error')
            return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))

        # Get pricing
        pricing = ProgramPricing.query.filter_by(
            program_id=tecfee_program.id,
            pricing_type=pricing_type,
            is_active=True
        ).first()

        if not pricing:
            flash('Option de tarification invalide.', 'error')
            return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))

        # Create enrollment
        enrollment, enrollment_message = GroupSessionService.enroll_client_in_tecfee(
            client_id=client.id,
            pricing_type=pricing_type
        )

        if not enrollment:
            flash(f'Erreur lors de l\'inscription: {enrollment_message}', 'error')
            return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))

        # Generate invoice
        initial_amount = float(pricing.price) if pricing_type == 'full_package' else 44.99
        invoice = InvoiceService.generate_invoice_for_tecfee_enrollment(
            client_id=client.id,
            pricing_type=pricing_type,
            amount=initial_amount
        )

        if not invoice:
            flash('Erreur lors de la génération de la facture.', 'error')
            return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))

        # Store enrollment details in session
        session['tecfee_enrollment'] = {
            'client_id': client.id,
            'program_id': tecfee_program.id,
            'pricing_type': pricing_type,
            'price': float(pricing.price),
            'invoice_id': invoice.id
        }

        # Commit all changes
        db.session.commit()

        flash('Profil complété avec succès! Sélectionnez maintenant vos sessions.', 'success')
        return redirect(url_for('public.tecfee_session_selection'))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de la mise à jour du profil: {str(e)}', 'error')
        return redirect(url_for('public.tecfee_google_profile_completion', client_id=client.id))

@public_bp.route('/tecfee/payment')
@login_required
def tecfee_payment():
    """TECFÉE payment page."""
    if current_user.role != 'client':
        flash('Access denied.', 'error')
        return redirect(url_for('public.index'))

    # Get enrollment details from session
    enrollment_data = session.get('tecfee_enrollment')
    if not enrollment_data:
        flash('No enrollment data found. Please start the enrollment process again.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    # Get program and pricing details
    tecfee_program = Program.query.get(enrollment_data['program_id'])
    pricing = ProgramPricing.query.filter_by(
        program_id=enrollment_data['program_id'],
        pricing_type=enrollment_data['pricing_type']
    ).first()

    if not tecfee_program or not pricing:
        flash('Invalid enrollment data.', 'error')
        session.pop('tecfee_enrollment', None)
        return redirect(url_for('public.tecfee_enrollment'))

    return render_template('public/tecfee_payment.html',
                         program=tecfee_program,
                         pricing=pricing,
                         enrollment_data=enrollment_data)




@public_bp.route('/tecfee/payment/process', methods=['POST'])
def process_tecfee_payment():
    """Process TECFÉE payment for new clients (no login required)."""
    # Get enrollment details from session
    enrollment_data = session.get('tecfee_enrollment')
    if not enrollment_data:
        flash('Aucune donnée d\'inscription trouvée.', 'error')
        return redirect(url_for('public.tecfee_enrollment'))

    try:
        # For now, we'll simulate successful payment
        # In a real implementation, you would process the payment with Stripe

        # Check if enrollment already exists (client was already created)
        client_id = enrollment_data['client_id']
        tecfee_program = GroupSessionService.get_tecfee_program()

        existing_enrollment = Enrollment.query.filter_by(
            client_id=client_id,
            program_id=tecfee_program.id,
            status='active'
        ).first()

        if not existing_enrollment:
            # Create enrollment if it doesn't exist
            enrollment, message = GroupSessionService.enroll_client_in_tecfee(
                client_id=client_id,
                pricing_type=enrollment_data['pricing_type']
            )

            if not enrollment:
                flash(f'Échec de l\'inscription: {message}', 'error')
                return redirect(url_for('public.tecfee_payment'))
        else:
            enrollment = existing_enrollment

        # If per-session pricing with selected sessions, enroll in specific sessions
        if enrollment_data.get('pricing_type') == 'per_session' and enrollment_data.get('selected_sessions'):
            selected_session_ids = enrollment_data['selected_sessions']

            # Add client to selected sessions
            for session_id in selected_session_ids:
                success, session_message = GroupSessionService.enroll_client_in_session(
                    client_id=client_id,
                    session_id=session_id,
                    pricing_type='per_session'
                )
                if not success:
                    # Log the error but continue with other sessions
                    flash(f'Erreur lors de l\'inscription à une session: {session_message}', 'warning')

        # Update and mark invoice as paid (simulate payment)
        if enrollment_data.get('invoice_id'):
            from app.models.invoice import Invoice, InvoiceItem
            invoice = Invoice.query.get(enrollment_data['invoice_id'])
            if invoice:
                # Update invoice amount based on final selection
                final_amount = enrollment_data.get('total_amount', enrollment_data.get('price'))
                invoice.total_amount = final_amount

                # Update invoice item
                invoice_item = InvoiceItem.query.filter_by(invoice_id=invoice.id).first()
                if invoice_item:
                    if enrollment_data.get('pricing_type') == 'full_package':
                        invoice_item.description = f"TECFÉE Program - Full Package (12 modules) - Selected sessions"
                    else:
                        session_count = len(enrollment_data.get('selected_sessions', []))
                        invoice_item.description = f"TECFÉE Program - {session_count} selected sessions"
                    invoice_item.amount = final_amount

                invoice.status = 'paid'
                invoice.payment_date = datetime.now()
                db.session.commit()

        # Clear session data
        session.pop('tecfee_enrollment', None)

        # Redirect to success page
        flash('Inscription et paiement réussis! Vous pouvez maintenant vous connecter à votre compte.', 'success')
        return render_template('public/tecfee_enrollment_success.html',
                             client_id=client_id,
                             enrollment=enrollment)

    except Exception as e:
        db.session.rollback()
        flash(f'Une erreur s\'est produite lors du traitement: {str(e)}', 'error')
        return redirect(url_for('public.tecfee_payment'))

@public_bp.route('/tecfee/sessions')
def tecfee_sessions():
    """View available TECFÉE group sessions."""
    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('public.index'))

    # Get available sessions
    available_sessions = GroupSessionService.get_available_sessions_for_program(tecfee_program.id)

    return render_template('public/tecfee_sessions.html',
                         program=tecfee_program,
                         sessions=available_sessions)

@public_bp.route('/about')
def about():
    """Redirect to main website services page."""
    return redirect('https://www.tutoraide.ca/services.html')

@public_bp.route('/contact')
def contact():
    """Redirect to main website contact page."""
    return redirect('https://www.tutoraide.ca/nous-joindre.html')


