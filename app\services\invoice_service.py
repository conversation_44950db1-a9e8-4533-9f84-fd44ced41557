# app/services/invoice_service.py
from app.extensions import db
from app.models.appointment import Appointment
from app.models.invoice import Invoice, InvoiceItem
from app.models.client import Client, IndividualClient
from app.models.service import TutorService
from app.models.subscription import Subscription
from app.models.invoice_generation import InvoiceGenerationSettings
from app.models.notification import Notification
from datetime import datetime, timedelta
import stripe
from flask import current_app, url_for
from sqlalchemy import text
from sqlalchemy.exc import IntegrityError

class InvoiceService:

    @staticmethod
    def generate_invoice(client_id, appointment_ids=None, start_date=None, end_date=None):
        """Generate an invoice for a client with race condition protection.

        Args:
            client_id: ID of the client to invoice
            appointment_ids: Optional list of specific appointment IDs to include
            start_date: Optional start date for appointments to include
            end_date: Optional end date for appointments to include

        Returns:
            The newly created invoice or None if no billable appointments
        """
        try:
            # Use serializable transaction to prevent race conditions
            with db.session.begin():
                # Set transaction isolation level for PostgreSQL/MySQL
                try:
                    db.session.execute(text('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE'))
                except Exception:
                    # SQLite doesn't support this, continue anyway
                    pass

                # Lock the client record to prevent concurrent invoice generation
                client = db.session.query(Client).filter_by(id=client_id).with_for_update().first()
                if not client:
                    return None

                # Query completed appointments that haven't been invoiced yet with row-level locking
                query = db.session.query(Appointment).filter(
                    Appointment.client_id == client_id,
                    Appointment.status == 'completed'
                ).outerjoin(  # Left join to find appointments not already on an invoice
                    InvoiceItem, Appointment.id == InvoiceItem.appointment_id
                ).filter(
                    InvoiceItem.id == None  # Only include appointments not already invoiced
                ).with_for_update()

                # Add date filters if provided
                if start_date:
                    query = query.filter(Appointment.start_time >= start_date)
                if end_date:
                    query = query.filter(Appointment.start_time <= end_date)

                # Filter by specific appointment IDs if provided
                if appointment_ids:
                    query = query.filter(Appointment.id.in_(appointment_ids))

                # Get unbilled appointments with locks
                unbilled_appointments = query.all()

                if not unbilled_appointments:
                    return None

                # Double-check that none of these appointments have been invoiced
                # in another concurrent transaction
                for appointment in unbilled_appointments:
                    existing_invoice_items = db.session.query(InvoiceItem).filter_by(
                        appointment_id=appointment.id
                    ).first()
                    if existing_invoice_items:
                        current_app.logger.warning(
                            f"Appointment {appointment.id} already has invoice items, skipping"
                        )
                        return None

                # Create a new invoice
                invoice = Invoice(
                    client_id=client_id,
                    invoice_date=datetime.now().date(),
                    due_date=(datetime.now() + timedelta(days=30)).date(),  # Due in 30 days
                    total_amount=0,  # Will calculate below
                    status='pending'
                )
                db.session.add(invoice)
                db.session.flush()  # Get the invoice ID

                total_amount = 0

                # Add invoice items for each appointment
                for appointment in unbilled_appointments:
                    tutor_service = TutorService.query.get(appointment.tutor_service_id)
                    if not tutor_service:
                        continue

                    # Calculate duration in hours
                    duration_hours = appointment.duration_minutes / 60

                    # Calculate amount for this appointment
                    service_amount = float(tutor_service.client_rate) * duration_hours

                    # Create invoice item for service
                    service_item = InvoiceItem(
                        invoice_id=invoice.id,
                        appointment_id=appointment.id,
                        amount=service_amount,
                        description=f"{tutor_service.service.name} with {appointment.tutor.first_name} {appointment.tutor.last_name} on {appointment.start_time.strftime('%Y-%m-%d %H:%M')} ({duration_hours:.2f} hours)"
                    )
                    db.session.add(service_item)

                    total_amount += service_amount

                    # Add transport fee if applicable (not for subscription-based appointments)
                    if not appointment.is_subscription_based and appointment.transport_fee and float(appointment.transport_fee) > 0:
                        transport_item = InvoiceItem(
                            invoice_id=invoice.id,
                            appointment_id=appointment.id,
                            amount=float(appointment.transport_fee),
                            description=f"Transport fee for {appointment.start_time.strftime('%Y-%m-%d %H:%M')} session"
                        )
                        db.session.add(transport_item)

                        total_amount += float(appointment.transport_fee)

                # Update invoice total
                invoice.total_amount = total_amount

                # Transaction will be committed automatically by the context manager
                return invoice

        except IntegrityError as e:
            # Handle database constraint violations (e.g., duplicate invoice items)
            db.session.rollback()
            current_app.logger.error(f"Integrity error during invoice generation for client {client_id}: {str(e)}")
            return None
        except Exception as e:
            # Handle any other errors
            db.session.rollback()
            current_app.logger.error(f"Error generating invoice for client {client_id}: {str(e)}")
            return None

    @staticmethod
    def create_payment_intent(invoice_id, client_id=None):
        """Create a Stripe payment intent for an invoice with race condition protection.

        Args:
            invoice_id: ID of the invoice to pay
            client_id: Optional ID of the client making the payment

        Returns:
            PaymentIntent object or None if error
        """
        try:
            # Use serializable transaction to prevent race conditions
            with db.session.begin():
                # Set transaction isolation level for PostgreSQL/MySQL
                try:
                    db.session.execute(text('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE'))
                except Exception:
                    # SQLite doesn't support this, continue anyway
                    pass

                # Lock the invoice record to prevent concurrent payment intent creation
                invoice = db.session.query(Invoice).filter_by(id=invoice_id).with_for_update().first()
                if not invoice or invoice.status != 'pending':
                    return None

                # Check if payment intent already exists
                if invoice.stripe_payment_intent_id:
                    # Return existing payment intent
                    stripe.api_key = current_app.config['STRIPE_SECRET_KEY']
                    try:
                        existing_intent = stripe.PaymentIntent.retrieve(invoice.stripe_payment_intent_id)
                        return existing_intent
                    except stripe.error.StripeError:
                        # If intent doesn't exist in Stripe, continue to create new one
                        pass

                # If client_id is provided, ensure they have access to this invoice
                if client_id:
                    client = db.session.query(Client).filter_by(id=client_id).with_for_update().first()
                    if not client:
                        return None

                    # Verify this client has access to the invoice
                    accessible_clients = invoice.get_accessible_clients()
                    if client not in accessible_clients:
                        return None
                else:
                    # Default to the invoice's client
                    client = db.session.query(Client).filter_by(id=invoice.client_id).with_for_update().first()
                    if not client:
                        return None

                stripe.api_key = current_app.config['STRIPE_SECRET_KEY']

                # Ensure client has a Stripe customer ID with race condition protection
                if not client.stripe_customer_id:
                    # Create a new customer in Stripe
                    customer = stripe.Customer.create(
                        email=client.email or (client.user.email if client.user else None),
                        name=f"{client.first_name} {client.last_name}",
                        phone=client.phone,
                        metadata={
                            'client_id': client.id
                        }
                    )
                    client.stripe_customer_id = customer.id
                    # No need to commit here, will be committed by context manager

                # Create payment intent
                amount_cents = int(float(invoice.total_amount) * 100)  # Convert to cents
                intent = stripe.PaymentIntent.create(
                    amount=amount_cents,
                    currency='usd',
                    customer=client.stripe_customer_id,
                    metadata={
                        'invoice_id': invoice.id,
                        'client_id': client.id
                    },
                    description=f"Invoice #{invoice.id} - Tutoring Services"
                )

                # Update invoice with payment intent ID
                invoice.stripe_payment_intent_id = intent.id

                # Transaction will be committed automatically by the context manager
                return intent

        except stripe.error.StripeError as e:
            # Handle any Stripe errors
            db.session.rollback()
            current_app.logger.error(f"Stripe error creating payment intent for invoice {invoice_id}: {str(e)}")
            return None
        except Exception as e:
            # Handle any other errors
            db.session.rollback()
            current_app.logger.error(f"Error creating payment intent for invoice {invoice_id}: {str(e)}")
            return None

    @staticmethod
    def process_payment_success(payment_intent_id, client_id=None):
        """Process a successful payment with race condition protection.

        Args:
            payment_intent_id: Stripe payment intent ID
            client_id: ID of the client who made the payment

        Returns:
            Tuple of (success, invoice)
        """
        try:
            # Use serializable transaction to prevent race conditions
            with db.session.begin():
                # Set transaction isolation level for PostgreSQL/MySQL
                try:
                    db.session.execute(text('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE'))
                except Exception:
                    # SQLite doesn't support this, continue anyway
                    pass

                # Lock the invoice record to prevent concurrent payment processing
                invoice = db.session.query(Invoice).filter_by(
                    stripe_payment_intent_id=payment_intent_id
                ).with_for_update().first()

                if not invoice:
                    return False, None

                # Check if invoice is already paid to prevent double processing
                if invoice.status == 'paid':
                    current_app.logger.info(f"Invoice {invoice.id} already marked as paid, skipping")
                    return True, invoice

                # Mark the invoice as paid
                invoice.mark_as_paid(client_id)

                # Transaction will be committed automatically by the context manager
                return True, invoice

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error processing payment for intent {payment_intent_id}: {str(e)}")
            return False, None

    @staticmethod
    def get_invoices_for_client(client_id):
        """Get all invoices for a client.

        Args:
            client_id: ID of the client

        Returns:
            List of Invoice objects
        """
        client = Client.query.get(client_id)
        if not client:
            return []

        return Invoice.query.filter_by(client_id=client_id).all()

    @staticmethod
    def generate_invoice_for_appointment(appointment_id):
        """Generate an invoice for a single appointment with race condition protection.

        Args:
            appointment_id: ID of the completed appointment

        Returns:
            The newly created invoice or None if already invoiced or not eligible
        """
        try:
            # Use serializable transaction to prevent race conditions
            with db.session.begin():
                # Set transaction isolation level for PostgreSQL/MySQL
                try:
                    db.session.execute(text('SET TRANSACTION ISOLATION LEVEL SERIALIZABLE'))
                except Exception:
                    # SQLite doesn't support this, continue anyway
                    pass

                # Lock the appointment and invoice generation settings to prevent concurrent processing
                appointment = db.session.query(Appointment).filter_by(id=appointment_id).with_for_update().first()
                if not appointment or appointment.status != 'completed':
                    return None

                # Skip subscription-based appointments (they're handled differently)
                if appointment.is_subscription_based:
                    return None

                # Check if this appointment already has an invoice with locking
                existing_setting = db.session.query(InvoiceGenerationSettings).filter_by(
                    appointment_id=appointment_id
                ).with_for_update().first()

                if existing_setting and existing_setting.invoice_generated:
                    return None

                # Double-check that no invoice items exist for this appointment
                existing_invoice_item = db.session.query(InvoiceItem).filter_by(
                    appointment_id=appointment_id
                ).first()
                if existing_invoice_item:
                    current_app.logger.warning(
                        f"Appointment {appointment_id} already has invoice items, skipping"
                    )
                    return None

                # Generate invoice for just this appointment
                invoice = InvoiceService.generate_invoice(appointment.client_id, appointment_ids=[appointment_id])

                if invoice:
                    # Record that we've generated an invoice for this appointment
                    if not existing_setting:
                        setting = InvoiceGenerationSettings(
                            appointment_id=appointment_id,
                            invoice_generated=True,
                            invoice_id=invoice.id
                        )
                        db.session.add(setting)
                    else:
                        existing_setting.invoice_generated = True
                        existing_setting.invoice_id = invoice.id

                    # Transaction will be committed automatically by the context manager

                return invoice

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error generating invoice for appointment {appointment_id}: {str(e)}")
            return None

    @staticmethod
    def generate_invoice_for_appointment_in_transaction(appointment_id):
        """Generate an invoice for a specific appointment within an existing transaction.

        This method is designed to be called from within an existing database transaction
        and does not manage its own transaction boundaries.

        Args:
            appointment_id: ID of the appointment to generate invoice for

        Returns:
            The newly created invoice or None if already invoiced or not eligible
        """
        try:
            # Get the appointment (no locking since we're in an existing transaction)
            appointment = Appointment.query.filter_by(id=appointment_id).first()
            if not appointment or appointment.status != 'completed':
                return None

            # Skip subscription-based appointments (they're handled differently)
            if appointment.is_subscription_based:
                return None

            # Check if this appointment already has an invoice
            existing_setting = InvoiceGenerationSettings.query.filter_by(
                appointment_id=appointment_id
            ).first()

            if existing_setting and existing_setting.invoice_generated:
                current_app.logger.info(f"Invoice already generated for appointment {appointment_id}")
                return None

            # Check if there are any existing invoice items for this appointment
            existing_invoice_items = InvoiceItem.query.filter_by(
                appointment_id=appointment_id
            ).first()

            if existing_invoice_items:
                current_app.logger.info(f"Appointment {appointment_id} already has invoice items")
                return None

            # Generate invoice for just this appointment (within existing transaction)
            invoice = InvoiceService._generate_invoice_in_transaction(appointment.client_id, appointment_ids=[appointment_id])

            if invoice:
                # Record that we've generated an invoice for this appointment
                if not existing_setting:
                    setting = InvoiceGenerationSettings(
                        appointment_id=appointment_id,
                        invoice_generated=True,
                        invoice_id=invoice.id
                    )
                    db.session.add(setting)
                else:
                    existing_setting.invoice_generated = True
                    existing_setting.invoice_id = invoice.id

                # Don't commit here - let the calling method handle the transaction

            return invoice

        except Exception as e:
            current_app.logger.error(f"Error generating invoice for appointment {appointment_id} in transaction: {str(e)}")
            raise  # Re-raise the exception to let the calling method handle it

    @staticmethod
    def _generate_invoice_in_transaction(client_id, appointment_ids=None):
        """Generate an invoice within an existing transaction.

        This is a helper method that doesn't manage transaction boundaries.

        Args:
            client_id: ID of the client to generate invoice for
            appointment_ids: Optional list of specific appointment IDs to include

        Returns:
            The newly created invoice or None if no billable appointments
        """
        # Get the client (no locking since we're in an existing transaction)
        client = Client.query.filter_by(id=client_id).first()
        if not client:
            return None

        # Build query for unbilled appointments
        query = db.session.query(Appointment).filter(
            Appointment.client_id == client_id,
            Appointment.status == 'completed',
            Appointment.is_subscription_based == False
        )

        # If specific appointment IDs are provided, filter by them
        if appointment_ids:
            query = query.filter(Appointment.id.in_(appointment_ids))
        else:
            # Only get appointments that haven't been invoiced yet
            invoiced_appointment_ids = db.session.query(InvoiceItem.appointment_id).filter(
                InvoiceItem.appointment_id.isnot(None)
            ).subquery()
            query = query.filter(~Appointment.id.in_(invoiced_appointment_ids))

        # Get unbilled appointments
        unbilled_appointments = query.all()

        if not unbilled_appointments:
            return None

        # Double-check that none of these appointments have been invoiced
        for appointment in unbilled_appointments:
            existing_invoice_items = db.session.query(InvoiceItem).filter_by(
                appointment_id=appointment.id
            ).first()
            if existing_invoice_items:
                current_app.logger.warning(
                    f"Appointment {appointment.id} already has invoice items, skipping"
                )
                return None

        # Create a new invoice
        invoice = Invoice(
            client_id=client_id,
            invoice_date=datetime.now().date(),
            due_date=(datetime.now() + timedelta(days=30)).date(),  # Due in 30 days
            total_amount=0,  # Will calculate below
            status='pending'
        )
        db.session.add(invoice)
        db.session.flush()  # Get the invoice ID

        # Add invoice items for each appointment
        for appointment in unbilled_appointments:
            tutor_service = TutorService.query.get(appointment.tutor_service_id)
            if not tutor_service:
                continue

            # Calculate duration in hours
            duration_hours = appointment.duration_minutes / 60

            # Calculate amount for this appointment
            service_amount = float(tutor_service.client_rate) * duration_hours

            # Create invoice item for service
            service_item = InvoiceItem(
                invoice_id=invoice.id,
                appointment_id=appointment.id,
                amount=service_amount,
                description=f"{tutor_service.service.name} with {appointment.tutor.first_name} {appointment.tutor.last_name} on {appointment.start_time.strftime('%Y-%m-%d %H:%M')} ({duration_hours:.2f} hours)"
            )
            db.session.add(service_item)
            invoice.total_amount += service_amount

            # Add transport fee if applicable
            if tutor_service.transport_fee and tutor_service.transport_fee > 0:
                transport_item = InvoiceItem(
                    invoice_id=invoice.id,
                    appointment_id=appointment.id,
                    amount=float(tutor_service.transport_fee),
                    description=f"Transport fee for {appointment.start_time.strftime('%Y-%m-%d %H:%M')}"
                )
                db.session.add(transport_item)
                invoice.total_amount += float(tutor_service.transport_fee)

        # Don't commit here - let the calling method handle the transaction
        return invoice

    @staticmethod
    def generate_invoice_for_subscription(subscription_id):
        """Generate an advance invoice for a subscription.

        Args:
            subscription_id: ID of the subscription

        Returns:
            The newly created invoice or None if error
        """
        subscription = Subscription.query.get(subscription_id)
        if not subscription or subscription.status != 'active':
            return None

        # Get the subscription plan
        plan = subscription.plan
        if not plan:
            return None

        # Get the client
        client = Client.query.get(subscription.client_id)
        if not client:
            return None

        # Create a new invoice
        invoice = Invoice(
            client_id=client.id,
            invoice_date=datetime.now().date(),
            due_date=datetime.now().date(),  # Due immediately for subscriptions
            total_amount=float(plan.price),
            status='pending',
            is_subscription_advance=True,
            subscription_id=subscription.id,
            notes=f"Advance payment for {plan.name} subscription. Valid from {subscription.start_date} to {subscription.end_date}."
        )
        db.session.add(invoice)
        db.session.flush()  # Get the invoice ID

        # Create a single invoice item for the subscription
        item = InvoiceItem(
            invoice_id=invoice.id,
            appointment_id=None,  # No specific appointment
            amount=float(plan.price),
            description=f"{plan.name} Subscription - {plan.max_hours} hours included. Valid from {subscription.start_date} to {subscription.end_date}."
        )
        db.session.add(item)

        db.session.commit()

        return invoice

    @staticmethod
    def generate_invoice_for_tecfee_enrollment(client_id, pricing_type, amount):
        """Generate an invoice for TECFÉE program enrollment.

        Args:
            client_id: ID of the client
            pricing_type: Type of pricing ('per_session' or 'full_package')
            amount: Amount to charge

        Returns:
            The newly created invoice or None if error
        """
        try:
            client = Client.query.get(client_id)
            if not client:
                return None

            # Create a new invoice
            invoice = Invoice(
                client_id=client_id,
                invoice_date=datetime.now().date(),
                due_date=datetime.now().date(),  # Due immediately for program enrollment
                total_amount=amount,
                status='pending',
                notes=f"Inscription au programme TECFÉE - {pricing_type}"
            )
            db.session.add(invoice)
            db.session.flush()  # Get the invoice ID

            # Create a single invoice item for the program enrollment
            description = f"Inscription au programme TECFÉE - "
            if pricing_type == 'per_session':
                description += "Paiement par module"
            else:
                description += "Forfait complet (12 modules)"

            item = InvoiceItem(
                invoice_id=invoice.id,
                appointment_id=None,  # No specific appointment for program enrollment
                amount=amount,
                description=description
            )
            db.session.add(item)

            db.session.commit()

            return invoice

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error generating TECFÉE enrollment invoice for client {client_id}: {str(e)}")
            return None

    @staticmethod
    def check_for_expired_subscriptions():
        """Check for subscriptions that have expired and create notifications.

        Returns:
            Number of notifications created
        """
        today = datetime.now().date()

        # Find subscriptions that expired yesterday
        expired_subscriptions = Subscription.query.filter(
            Subscription.end_date < today,
            Subscription.status == 'active'
        ).all()

        count = 0

        for subscription in expired_subscriptions:
            # Update subscription status
            subscription.status = 'expired'

            # Get all managers
            from app.models.user import User

            managers = User.query.filter_by(role='manager').all()

            # Create notification for each manager
            for manager in managers:
                Notification.create_notification(
                    user_id=manager.id,
                    message=f"Subscription #{subscription.id} for {subscription.client.full_name} has expired.",
                    category='subscription',
                    related_id=subscription.id
                )
                count += 1

        db.session.commit()

        return count

    @staticmethod
    def get_unpaid_invoices_by_client():
        """Get all unpaid invoices grouped by client and sorted by total amount.

        Returns:
            Dictionary with client_id as key and list of invoices as value,
            sorted by total amount descending
        """
        # Get all unpaid invoices
        unpaid_invoices = Invoice.query.filter_by(status='pending').all()

        # Group by client
        grouped_invoices = {}

        for invoice in unpaid_invoices:
            if invoice.client_id not in grouped_invoices:
                grouped_invoices[invoice.client_id] = []

            grouped_invoices[invoice.client_id].append(invoice)

        # Sort each client's invoices by amount descending
        for client_id in grouped_invoices:
            grouped_invoices[client_id] = sorted(
                grouped_invoices[client_id],
                key=lambda x: float(x.total_amount),
                reverse=True
            )

        # Sort clients by their total unpaid amount
        client_totals = {}
        for client_id, invoices in grouped_invoices.items():
            client_totals[client_id] = sum(float(invoice.total_amount) for invoice in invoices)

        # Sort the dictionary by total amount
        sorted_clients = sorted(client_totals.items(), key=lambda x: x[1], reverse=True)

        # Create a new ordered dictionary
        sorted_invoices = {}
        for client_id, _ in sorted_clients:
            sorted_invoices[client_id] = grouped_invoices[client_id]

        return sorted_invoices
