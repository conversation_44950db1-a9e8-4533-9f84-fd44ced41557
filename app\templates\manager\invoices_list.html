<!-- app/templates/manager/invoices_list.html -->
{% extends "base.html" %}

{% block title %}Invoices - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Invoices</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.generate_invoices') }}" class="btn btn-primary">
            <i class="fas fa-file-invoice-dollar"></i> Generate Invoices
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.invoices_list') }}" class="row g-3 filter-form">
            <div class="col-md-4">
                <select name="status" class="form-select">
                    <option value="all" {% if request.args.get('status') == 'all' %}selected{% endif %}>All Statuses</option>
                    <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="paid" {% if request.args.get('status') == 'paid' %}selected{% endif %}>Paid</option>
                    <option value="overdue" {% if request.args.get('status') == 'overdue' %}selected{% endif %}>Overdue</option>
                    <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-4">
                <select name="parent_id" class="form-select">
                    <option value="">All Parents</option>
                    {% for parent in parents %}
                        <option value="{{ parent.id }}" {% if request.args.get('parent_id') == parent.id|string %}selected{% endif %}>
                            {{ parent.first_name }} {{ parent.last_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <input type="date" name="start_date" class="form-control" value="{{ request.args.get('start_date', '') }}" placeholder="Start Date">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card shadow">
    <div class="card-body">
        {% if invoices %}
            {% if request.args.get('status', 'all') in ['pending', 'overdue', 'all'] and request.args.get('parent_id', '') == '' %}
                <!-- Group by parent for pending/overdue invoices -->
                {% set parent_groups = {} %}
                {% for invoice in invoices %}
                    {% if invoice.parent_id not in parent_groups %}
                        {% set parent_groups = parent_groups.update({invoice.parent_id: []}) or parent_groups %}
                    {% endif %}
                    {% set _ = parent_groups[invoice.parent_id].append(invoice) %}
                {% endfor %}

                {% for parent_id, parent_invoices in parent_groups.items() %}
                    {% set client = parent_invoices[0].client %}
                    {% set total_amount = 0 %}
                    {% for invoice in parent_invoices %}
                        {% set total_amount = total_amount + invoice.total_amount|float %}
                    {% endfor %}

                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ client.first_name }} {{ client.last_name }} - Total Due: ${{ "%.2f"|format(total_amount) }}</h5>
                            <a href="{{ url_for('manager.invoices_list', parent_id=client.id, status='pending') }}" class="btn btn-sm btn-outline-primary">
                                View Only This Client
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Invoice #</th>
                                            <th>Date</th>
                                            <th>Due Date</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for invoice in parent_invoices %}
                                            <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                                <td>{{ invoice.id }}</td>
                                                <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                                <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                                <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                                <td>
                                                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }}">
                                                        {{ 'Overdue' if invoice.is_overdue else invoice.status | capitalize }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="{{ url_for('manager.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-info email-invoice-btn" data-invoice-id="{{ invoice.id }}" data-bs-toggle="modal" data-bs-target="#emailInvoiceModal">
                                                        <i class="fas fa-envelope"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <!-- Regular invoice list for paid/cancelled or filtered by parent -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Due Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices %}
                                <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                    <td>{{ invoice.id }}</td>
                                    <td>{{ invoice.client.first_name }} {{ invoice.client.last_name }}</td>
                                    <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                    <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }}">
                                            {{ 'Overdue' if invoice.is_overdue else invoice.status | capitalize }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{{ url_for('manager.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                        <button type="button" class="btn btn-sm btn-info email-invoice-btn" data-invoice-id="{{ invoice.id }}" data-bs-toggle="modal" data-bs-target="#emailInvoiceModal">
                                            <i class="fas fa-envelope"></i>
                                        </button>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
                <h5>No invoices found</h5>
                <p class="text-muted">No invoices match your search criteria or no invoices have been generated yet.</p>
                <a href="{{ url_for('manager.generate_invoices') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-file-invoice-dollar"></i> Generate Invoices
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Email Invoice Modal -->
<div class="modal fade" id="emailInvoiceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="emailInvoiceForm" action="" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">Message (Optional)</label>
                        <textarea class="form-control" id="message" name="message" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Email</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Email invoice button handler
        const emailBtns = document.querySelectorAll('.email-invoice-btn');
        const emailForm = document.getElementById('emailInvoiceForm');

        emailBtns.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const invoiceId = this.dataset.invoiceId;
                emailForm.action = "{{ url_for('manager.email_invoice', id=0) }}".replace('0', invoiceId);

                // Fetch client email
                fetch(`/api/invoice/${invoiceId}`)
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('email').value = data.client_email;
                    })
                    .catch(error => {
                        console.error('Error fetching invoice details:', error);
                    });
            });
        });
    });
</script>
{% endblock %}