# app/models/time_off.py
from datetime import datetime
from app.extensions import db

class TimeOff(db.Model):
    __tablename__ = 'time_off_requests'
    
    id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('tutors.id', ondelete='CASCADE'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    reason = db.Column(db.Text, nullable=True)
    status = db.Column(db.String(20), nullable=False, default='pending')  # pending, approved, rejected
    manager_notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    tutor = db.relationship('Tutor', backref='time_off_requests')
    
    def __repr__(self):
        return f'<TimeOff id={self.id} tutor_id={self.tutor_id} status={self.status}>'
    
    @property
    def is_pending(self):
        return self.status == 'pending'
    
    @property
    def is_approved(self):
        return self.status == 'approved'
    
    @property
    def is_rejected(self):
        return self.status == 'rejected'
    
    @property
    def duration_days(self):
        """Calculate the duration in days."""
        if self.start_date and self.end_date:
            delta = (self.end_date - self.start_date).days + 1
            return delta
        return 0
