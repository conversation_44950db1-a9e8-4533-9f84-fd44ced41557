<!-- app/templates/manager/tutor_payments_list.html -->
{% extends "base.html" %}

{% block title %}Tutor Payments - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Tutor Payments</h2>
    </div>
    <div class="col-md-4 text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#processPaymentsModal" {% if not has_pending_payments %}disabled{% endif %}>
            <i class="fas fa-money-bill-wave"></i> Process Selected Payments
        </button>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.tutor_payments_list') }}" class="row g-3 filter-form">
            <div class="col-md-4">
                <label for="tutor_id" class="form-label">Tutor</label>
                <select name="tutor_id" id="tutor_id" class="form-select">
                    <option value="">All Tutors</option>
                    {% for tutor in tutors %}
                        <option value="{{ tutor.id }}" {% if current_tutor_id == tutor.id %}selected{% endif %}>
                            {{ tutor.first_name }} {{ tutor.last_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>All Payments</option>
                    <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="paid" {% if current_status == 'paid' %}selected{% endif %}>Paid</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Payments Table -->
<div class="card shadow">
    <div class="card-body">
        {% if payments %}
            <form id="payment-process-form" method="POST" action="{{ url_for('manager.process_tutor_payments') }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-payments">
                                        <label class="form-check-label" for="select-all-payments"></label>
                                    </div>
                                </th>
                                <th>Tutor</th>
                                <th>Date</th>
                                <th>Appointment</th>
                                <th>Service Amount</th>
                                <th>Transport</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input payment-checkbox" type="checkbox"
                                                   id="payment-{{ payment.id }}" name="payment_ids" value="{{ payment.id }}"
                                                   {% if payment.status not in ['pending', 'ready'] %}disabled{% endif %}>
                                            <label class="form-check-label" for="payment-{{ payment.id }}"></label>
                                        </div>
                                    </td>
                                    <td>{{ payment.tutor.first_name }} {{ payment.tutor.last_name }}</td>
                                    <td>{{ payment.appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <a href="{{ url_for('manager.view_appointment', id=payment.appointment_id) }}">
                                            {{ payment.appointment.start_time.strftime('%H:%M') }} -
                                            {{ payment.appointment.client.first_name }} {{ payment.appointment.client.last_name }}
                                        </a>
                                    </td>
                                    <td>${{ "%.2f"|format(payment.service_amount) }}</td>
                                    <td>${{ "%.2f"|format(payment.transport_amount) }}</td>
                                    <td><strong>${{ "%.2f"|format(payment.total_amount) }}</strong></td>
                                    <td>
                                        {% if payment.status == 'ready' %}
                                            <span class="badge bg-warning">Ready</span>
                                        {% elif payment.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif payment.status == 'paid' %}
                                            <span class="badge bg-success">Paid</span>
                                            <div class="small text-muted">{{ payment.payment_date.strftime('%Y-%m-%d') }}</div>
                                            {% if payment.stripe_payout_id %}
                                                <div class="small text-muted">
                                                    <i class="fas fa-credit-card"></i> {{ payment.stripe_payout_id[:20] }}...
                                                </div>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary">{{ payment.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.status in ['pending', 'ready'] %}
                                            <button type="button" class="btn btn-sm btn-success process-payment-btn"
                                                    data-payment-id="{{ payment.id }}">
                                                <i class="fas fa-check"></i> Process
                                            </button>
                                        {% endif %}

                                        <a href="{{ url_for('manager.view_tutor', id=payment.tutor_id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-user"></i> Tutor
                                        </a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>

            <!-- Totals Section -->
            <div class="row mt-4">
                <div class="col-md-6 offset-md-6">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Payment Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Selected Payments:</span>
                                <span id="selected-count">0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Selected Total:</span>
                                <span id="selected-total">$0.00</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Selected Transport Fees:</span>
                                <span id="selected-transport">$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h5>No payments found</h5>
                <p class="text-muted">No payments match your search criteria or no payments are due at this time.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Process Payments Modal -->
<div class="modal fade" id="processPaymentsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Process Payments</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to process the selected tutor payments?</p>
                <p>This will mark the following payments as paid:</p>
                <ul id="payments-to-process">
                    <li>Please select payments to process</li>
                </ul>
                <div class="alert alert-info">
                    <strong>Total: <span id="modal-total">$0.00</span></strong>
                </div>

                <div class="mt-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="use_stripe_payout" name="use_stripe_payout" value="true" checked>
                        <label class="form-check-label" for="use_stripe_payout">
                            <i class="fas fa-credit-card"></i> Process with Stripe direct bank payouts
                        </label>
                        <div class="form-text">
                            Automatically transfer funds to tutors' bank accounts via Stripe.
                            Requires tutors to have valid Canadian bank account information on file.
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirm-process-payments">Process Payments</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const selectAllCheckbox = document.getElementById('select-all-payments');
        const paymentCheckboxes = document.querySelectorAll('.payment-checkbox:not([disabled])');
        const selectedCountEl = document.getElementById('selected-count');
        const selectedTotalEl = document.getElementById('selected-total');
        const selectedTransportEl = document.getElementById('selected-transport');
        const paymentsToProcessList = document.getElementById('payments-to-process');
        const modalTotalEl = document.getElementById('modal-total');
        const confirmProcessBtn = document.getElementById('confirm-process-payments');
        const paymentForm = document.getElementById('payment-process-form');

        // Payment data
        const paymentData = {
            {% for payment in payments %}
                {{ payment.id }}: {
                    id: {{ payment.id }},
                    tutor: "{{ payment.tutor.first_name }} {{ payment.tutor.last_name }}",
                    date: "{{ payment.appointment.start_time.strftime('%Y-%m-%d') }}",
                    serviceAmount: {{ payment.service_amount }},
                    transportAmount: {{ payment.transport_amount }},
                    totalAmount: {{ payment.total_amount }},
                    status: "{{ payment.status }}"
                },
            {% endfor %}
        };

        // Update selected payments summary
        function updateSelectedPayments() {
            const selectedPayments = [];
            let totalAmount = 0;
            let transportAmount = 0;

            paymentCheckboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const paymentId = checkbox.value;
                    selectedPayments.push(paymentData[paymentId]);
                    totalAmount += parseFloat(paymentData[paymentId].totalAmount);
                    transportAmount += parseFloat(paymentData[paymentId].transportAmount);
                }
            });

            selectedCountEl.textContent = selectedPayments.length;
            selectedTotalEl.textContent = '$' + totalAmount.toFixed(2);
            selectedTransportEl.textContent = '$' + transportAmount.toFixed(2);

            // Update modal content
            if (selectedPayments.length > 0) {
                let listItems = '';
                selectedPayments.forEach(payment => {
                    listItems += `<li>${payment.tutor} - ${payment.date} - $${parseFloat(payment.totalAmount).toFixed(2)}</li>`;
                });
                paymentsToProcessList.innerHTML = listItems;
                modalTotalEl.textContent = '$' + totalAmount.toFixed(2);
            } else {
                paymentsToProcessList.innerHTML = '<li>Please select payments to process</li>';
                modalTotalEl.textContent = '$0.00';
            }
        }

        // Handle select all checkbox
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                paymentCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateSelectedPayments();
            });
        }

        // Handle individual checkboxes
        paymentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Update "select all" checkbox
                selectAllCheckbox.checked = Array.from(paymentCheckboxes).every(cb => cb.checked);
                updateSelectedPayments();
            });
        });

        // Handle individual process payment buttons
        const processButtons = document.querySelectorAll('.process-payment-btn');
        processButtons.forEach(button => {
            button.addEventListener('click', function() {
                const paymentId = this.dataset.paymentId;
                const checkbox = document.getElementById(`payment-${paymentId}`);

                // Clear all checkboxes
                paymentCheckboxes.forEach(cb => {
                    cb.checked = false;
                });

                // Check only this one
                checkbox.checked = true;

                // Update summary
                updateSelectedPayments();

                // Show modal
                const processModal = new bootstrap.Modal(document.getElementById('processPaymentsModal'));
                processModal.show();
            });
        });

        // Handle confirm process button
        if (confirmProcessBtn) {
            confirmProcessBtn.addEventListener('click', function() {
                paymentForm.submit();
            });
        }

        // Initialize summary
        updateSelectedPayments();
    });
</script>
{% endblock %}