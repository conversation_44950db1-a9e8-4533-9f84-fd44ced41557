-- Update TECFÉE Program to 12 Modules Structure
-- This script updates the TECFÉE program with the final 12-module structure
-- Based on the final curriculum with simulation exam at 2h total

-- First, get the TECFÉE program ID
DO $$
DECLARE
    tecfee_program_id INTEGER;
BEGIN
    SELECT id INTO tecfee_program_id FROM programs WHERE code = 'TECFEE';

    IF tecfee_program_id IS NULL THEN
        RAISE EXCEPTION 'TECFÉE program not found. Please run the basic TECFÉE setup first.';
    END IF;

    RAISE NOTICE 'Updating TECFÉE program modules for program ID: %', tecfee_program_id;

    -- Delete existing modules and related data to start fresh
    -- First delete module_sessions that reference module_progress
    DELETE FROM module_sessions WHERE module_progress_id IN (
        SELECT mp.id FROM module_progress mp
        JOIN program_modules pm ON mp.module_id = pm.id
        WHERE pm.program_id = tecfee_program_id
    );

    -- Then delete module_progress records
    DELETE FROM module_progress WHERE module_id IN (
        SELECT id FROM program_modules WHERE program_id = tecfee_program_id
    );

    -- Delete group_sessions that reference modules
    DELETE FROM group_sessions WHERE module_id IN (
        SELECT id FROM program_modules WHERE program_id = tecfee_program_id
    );

    -- Finally delete the modules themselves
    DELETE FROM program_modules WHERE program_id = tecfee_program_id;

    -- Update program description and total sessions
    UPDATE programs SET
        name = 'PARCOURS COMPLET LINGUISTIQUE TECFÉE',
        description = 'Préparation complète au test TECFÉE en 12 modules structurés (10 semaines + simulation)',
        total_sessions = 12,
        session_duration = 60,
        min_participants = 4,
        max_participants = 10
    WHERE id = tecfee_program_id;

    -- Module 1: Manipulation Syntaxiques et Classe de Mots (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Manipulation Syntaxiques et Classe de Mots',
        'Manipulation des structures syntaxiques et identification des classes de mots',
        1,
        60,
        'Maîtriser les manipulations syntaxiques et identifier correctement les classes de mots',
        TRUE
    );

    -- Module 2: Connecteurs (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Connecteurs',
        'Utilisation appropriée des connecteurs logiques et textuels',
        2,
        60,
        'Utiliser correctement les connecteurs pour assurer la cohérence textuelle',
        TRUE
    );

    -- Module 3: Pléonasmes, Impropriétés et Anglicismes (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Pléonasmes, Impropriétés et Anglicismes',
        'Identification et correction des pléonasmes, impropriétés et anglicismes',
        3,
        60,
        'Éviter les erreurs lexicales et les influences de l''anglais',
        TRUE
    );

    -- Module 4: Participe Passé (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Participe Passé',
        'Règles d''accord du participe passé',
        4,
        60,
        'Maîtriser les règles de l''accord du participe passé',
        TRUE
    );

    -- Module 5: Homophones et Logique du Code (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Homophones et Logique du Code',
        'Distinction des homophones et compréhension de la logique du code linguistique',
        5,
        60,
        'Distinguer les homophones et comprendre la logique orthographique',
        TRUE
    );

    -- Module 6: Affixes et Expressions (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Affixes et Expressions',
        'Formation des mots par affixation et expressions figées',
        6,
        60,
        'Comprendre la formation des mots et utiliser correctement les expressions',
        TRUE
    );

    -- Module 7: Syntaxe (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Syntaxe',
        'Analyse syntaxique et construction de phrases',
        7,
        60,
        'Analyser et construire des phrases syntaxiquement correctes',
        TRUE
    );

    -- Module 8: Ponctuation (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Ponctuation',
        'Règles de ponctuation et usage approprié des signes de ponctuation',
        8,
        60,
        'Maîtriser les règles de ponctuation française',
        TRUE
    );

    -- Module 9: Principaux Constituants de la Phrase et Conjugaison (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Principaux Constituants de la Phrase et Conjugaison',
        'Identification des constituants de phrase et maîtrise de la conjugaison',
        9,
        60,
        'Identifier les constituants de phrase et conjuguer correctement',
        TRUE
    );

    -- Module 10: Cas Morphologiques (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Cas Morphologiques',
        'Morphologie et accords grammaticaux',
        10,
        60,
        'Appliquer les règles morphologiques',
        TRUE
    );

    -- Module 11: Les Cas d'Accord Particuliers (1h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Les Cas d''Accord Particuliers',
        'Règles d''accord complexes et cas particuliers',
        11,
        60,
        'Maîtriser les accords grammaticaux complexes',
        TRUE
    );

    -- Module 12: Simulation Examen (2h)
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives, is_active)
    VALUES (
        tecfee_program_id,
        'Simulation Examen',
        'Examen blanc complet dans les conditions réelles du TECFÉE (1h30 + 30 minutes pour la partie rédaction)',
        12,
        120,
        'Se familiariser avec le format d''examen et évaluer sa préparation',
        TRUE
    );

    -- Update pricing for the new 12-module structure
    -- Per session stays the same: $44.99
    -- Full package: 12 × $44.99 = $539.88, but we'll offer it at $399 (saving $140.88)
    UPDATE program_pricing
    SET
        description = 'Package complet - 12 modules (économisez $140!)',
        price = 399.00
    WHERE program_id = tecfee_program_id
    AND pricing_type = 'full_package';

    -- Update per session description to be clearer
    UPDATE program_pricing
    SET description = 'Paiement par module individuel'
    WHERE program_id = tecfee_program_id
    AND pricing_type = 'per_session';

    RAISE NOTICE 'Successfully updated TECFÉE program with 12 modules and updated pricing';

END $$;

-- Show the updated modules
SELECT 'Updated TECFÉE Modules' as info;
SELECT
    pm.module_order,
    pm.name,
    pm.duration_minutes,
    pm.description
FROM program_modules pm
JOIN programs p ON pm.program_id = p.id
WHERE p.code = 'TECFEE'
ORDER BY pm.module_order;

-- Show program summary
SELECT 'TECFÉE Program Summary' as info;
SELECT
    p.name,
    p.description,
    p.total_sessions,
    (SELECT COUNT(*) FROM program_modules WHERE program_id = p.id) as actual_modules_count,
    (SELECT SUM(duration_minutes) FROM program_modules WHERE program_id = p.id) as total_duration_minutes
FROM programs p
WHERE p.code = 'TECFEE';
