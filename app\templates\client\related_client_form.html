<!-- app/templates/client/related_client_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('client.related_clients') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> {{ t('clients.related.back_to_list') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if related_client %}action="{{ url_for('client.edit_related_client', id=related_client.id) }}"{% else %}action="{{ url_for('client.add_related_client') }}"{% endif %}>
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        <label for="relationship_type" class="form-label">{{ t('clients.relationship.type') }} <span class="text-danger">*</span></label>
                        {{ form.relationship_type(class="form-select" + (" is-invalid" if form.relationship_type.errors else "")) }}
                        {% if form.relationship_type.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.relationship_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('clients.relationship.type_help') }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_primary(class="form-check-input" + (" is-invalid" if form.is_primary.errors else "")) }}
                            <label class="form-check-label" for="is_primary">
                                {{ t('clients.is_primary') }}
                            </label>
                            {% if form.is_primary.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.is_primary.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ t('clients.is_primary_help') }}</div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    <h5 class="mb-3">{{ t('clients.personal_info') }}</h5>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">{{ t('clients.first_name') }} <span class="text-danger">*</span></label>
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">{{ t('clients.last_name') }} <span class="text-danger">*</span></label>
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date_of_birth" class="form-label">{{ t('clients.date_of_birth') }}</label>
                        {{ form.date_of_birth(class="form-control" + (" is-invalid" if form.date_of_birth.errors else ""), type="date") }}
                        {% if form.date_of_birth.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.date_of_birth.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">{{ t('clients.phone') }}</label>
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ t('clients.notes') }}</label>
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows=4) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('clients.notes_help') }}</div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                        <a href="{{ url_for('client.related_clients') }}" class="btn btn-outline-secondary me-md-2">
                            {{ t('buttons.cancel') }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            {% if related_client %}
                                {{ t('buttons.update') }}
                            {% else %}
                                {{ t('buttons.create') }}
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
