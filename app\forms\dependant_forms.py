# app/forms/dependant_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DateField, SubmitField, SelectField
from wtforms.validators import DataRequired, Optional, Email, Length

class DependantForm(FlaskForm):
    """Form for creating and editing dependants."""

    # Basic information
    first_name = <PERSON>Field('First Name', validators=[DataRequired(), Length(max=100)])
    last_name = <PERSON>F<PERSON>('Last Name', validators=[DataRequired(), Length(max=100)])

    # Optional contact information
    email = StringField('Email', validators=[Optional(), Email(), Length(max=255)])
    phone = StringField('Phone', validators=[Optional(), Length(max=20)])

    # Structured address fields
    civic_number = StringField('Civic Number', validators=[Optional()])
    street = StringField('Street', validators=[Optional()])
    city = StringField('City', validators=[Optional()])
    postal_code = StringField('Postal Code', validators=[Optional()])
    province = SelectField('Province', choices=[
        ('Quebec', 'Quebec'),
        ('Ontario', 'Ontario'),
        ('British Columbia', 'British Columbia'),
        ('Alberta', 'Alberta'),
        ('Manitoba', 'Manitoba'),
        ('Saskatchewan', 'Saskatchewan'),
        ('Nova Scotia', 'Nova Scotia'),
        ('New Brunswick', 'New Brunswick'),
        ('Newfoundland and Labrador', 'Newfoundland and Labrador'),
        ('Prince Edward Island', 'Prince Edward Island'),
        ('Northwest Territories', 'Northwest Territories'),
        ('Nunavut', 'Nunavut'),
        ('Yukon', 'Yukon')
    ], default='Quebec', validators=[Optional()])
    country = SelectField('Country', choices=[
        ('Canada', 'Canada'),
        ('United States', 'United States'),
        ('Other', 'Other')
    ], default='Canada', validators=[Optional()])

    # Dependant-specific information
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    school_grade = SelectField('School Grade',
                              choices=[
                                  ('', 'Select Grade Level'),
                                  ('accueil', 'Accueil'),
                                  ('primaire_1', 'Primaire 1'),
                                  ('primaire_2', 'Primaire 2'),
                                  ('primaire_3', 'Primaire 3'),
                                  ('primaire_4', 'Primaire 4'),
                                  ('primaire_5', 'Primaire 5'),
                                  ('primaire_6', 'Primaire 6'),
                                  ('secondaire_1', 'Secondaire 1'),
                                  ('secondaire_2', 'Secondaire 2'),
                                  ('secondaire_3', 'Secondaire 3'),
                                  ('secondaire_4', 'Secondaire 4'),
                                  ('secondaire_5', 'Secondaire 5'),
                                  ('cegep', 'CÉGEP'),
                                  ('universite_1', 'Université 1ère année'),
                                  ('universite_2_3', 'Université 2e-3e année'),
                                  ('adulte', 'Adulte'),
                                  ('autre', 'Autre')
                              ],
                              validators=[Optional()])
    notes = TextAreaField('Notes', validators=[Optional()])

    # Language preference
    preferred_language = SelectField('Preferred Language',
                                   choices=[('en', 'English'), ('fr', 'French')],
                                   default='en',
                                   validators=[Optional()])

    submit = SubmitField('Save Dependant')

class DependantRelationshipForm(FlaskForm):
    """Form for managing dependant relationships with clients."""

    client_id = SelectField('Client', coerce=int, validators=[DataRequired()])
    relationship_type = SelectField('Relationship Type',
                                  choices=[
                                      ('child', 'Child'),
                                      ('student', 'Student'),
                                      ('employee', 'Employee'),
                                      ('dependent', 'Dependent'),
                                      ('other', 'Other')
                                  ],
                                  validators=[DataRequired()])
    is_primary = SelectField('Primary Contact',
                           choices=[('true', 'Yes'), ('false', 'No')],
                           default='false',
                           validators=[DataRequired()])

    submit = SubmitField('Save Relationship')

    def __init__(self, *args, **kwargs):
        super(DependantRelationshipForm, self).__init__(*args, **kwargs)
        # Client choices will be populated in the view
        self.client_id.choices = []
