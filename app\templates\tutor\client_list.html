<!-- app/templates/tutor/client_list.html -->
{% extends "base.html" %}

{% block title %}My Clients - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">My Clients</h2>
        <p class="text-muted">View information about clients you are tutoring.</p>
    </div>
    <div class="col-md-4">
        <div class="input-group mb-3">
            <input type="text" id="clientSearch" class="form-control" placeholder="Search clients...">
            <button class="btn btn-outline-secondary" type="button" id="searchButton">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filter Controls -->
<div class="card shadow mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-4">
                <label for="filterType" class="form-label">Client Type</label>
                <select id="filterType" class="form-select">
                    <option value="all">All Types</option>
                    <option value="individual">Individual</option>
                    <option value="institutional">Institutional</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="filterProgram" class="form-label">Program</label>
                <select id="filterProgram" class="form-select">
                    <option value="all">All Programs</option>
                    {% for program in programs %}
                        <option value="{{ program.id }}">{{ program.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="sortBy" class="form-label">Sort By</label>
                <select id="sortBy" class="form-select">
                    <option value="name">Name</option>
                    <option value="recent">Recent Appointments</option>
                    <option value="upcoming">Upcoming Appointments</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Clients List -->
{% if clients %}
    <div class="row" id="clientsContainer">
        {% for client in clients %}
            <div class="col-md-4 mb-4 client-card"
                 data-type="{{ client.client_type }}"
                 data-name="{{ client.first_name }} {{ client.last_name }}"
                 data-programs="{% for enrollment in client.enrollments %}{{ enrollment.program_id }}{% if not loop.last %},{% endif %}{% endfor %}">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ client.first_name }} {{ client.last_name }}</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span><i class="fas fa-user me-2"></i> Client Type</span>
                                <span class="badge bg-{{ 'primary' if client.client_type == 'individual' else 'info' }}">
                                    {{ client.client_type|capitalize }}
                                </span>
                            </li>

                            {% if client.client_type == 'individual' and client.individual_clients and client.individual_clients.date_of_birth %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-birthday-cake me-2"></i> Age</span>
                                    <span>{{ client.individual_clients.age }}</span>
                                </li>
                            {% endif %}

                            {% if client.client_type == 'institutional' and client.institutional_clients %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-building me-2"></i> Institution</span>
                                    <span>{{ client.institutional_clients.institution_name }}</span>
                                </li>
                            {% endif %}

                            <!-- Program Enrollments -->
                            {% if client.enrollments.count() > 0 %}
                                <li class="list-group-item">
                                    <h6 class="mb-2">Programs</h6>
                                    {% for enrollment in client.enrollments %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ enrollment.program.name }}</span>
                                            <div>
                                                <div class="progress" style="width: 60px; height: 8px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: {{ enrollment.completion_percentage or 0 }}%"></div>
                                                </div>
                                                <small class="text-muted">{{ (enrollment.completion_percentage or 0)|round|int }}%</small>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </li>
                            {% endif %}

                            <!-- Upcoming Appointments -->
                            {% if upcoming_appointments.get(client.id, [])|length > 0 %}
                                <li class="list-group-item">
                                    <h6 class="mb-2">Upcoming Appointments</h6>
                                    {% for appointment in upcoming_appointments.get(client.id, [])[:2] %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ appointment.start_time.strftime('%b %d') }}</span>
                                            <span class="badge bg-primary">
                                                {{ appointment.start_time.strftime('%I:%M %p') }}
                                            </span>
                                        </div>
                                    {% endfor %}
                                </li>
                            {% endif %}
                        </ul>

                        {% if client.client_type == 'individual' and client.notes %}
                            <div class="mt-3">
                                <h6>Notes:</h6>
                                <p class="text-muted">{{ client.notes|truncate(100) }}</p>
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-footer text-center">
                        <a href="{{ url_for('tutor.view_client', id=client.id) }}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Details & Appointments
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="card shadow">
        <div class="card-body text-center p-5">
            <i class="fas fa-users fa-4x text-muted mb-3"></i>
            <h4 class="mb-3">No clients found</h4>
            <p class="mb-4">You don't have any clients assigned to you yet. Once you have appointments scheduled, your clients will appear here.</p>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clientSearch = document.getElementById('clientSearch');
        const searchButton = document.getElementById('searchButton');
        const filterType = document.getElementById('filterType');
        const filterProgram = document.getElementById('filterProgram');
        const sortBy = document.getElementById('sortBy');
        const clientCards = document.querySelectorAll('.client-card');

        // Function to filter and sort clients
        function filterAndSortClients() {
            const searchTerm = clientSearch.value.toLowerCase();
            const typeFilter = filterType.value;
            const programFilter = filterProgram.value;
            const sortOption = sortBy.value;

            // Filter clients
            clientCards.forEach(card => {
                const clientName = card.dataset.name.toLowerCase();
                const clientType = card.dataset.type;
                const clientPrograms = card.dataset.programs.split(',');

                let showCard = true;

                // Apply search filter
                if (searchTerm && !clientName.includes(searchTerm)) {
                    showCard = false;
                }

                // Apply type filter
                if (typeFilter !== 'all' && clientType !== typeFilter) {
                    showCard = false;
                }

                // Apply program filter
                if (programFilter !== 'all' && !clientPrograms.includes(programFilter)) {
                    showCard = false;
                }

                // Show or hide card
                card.style.display = showCard ? '' : 'none';
            });

            // Sort clients (basic implementation - would need server-side sorting for more complex sorting)
            if (sortOption === 'name') {
                const container = document.getElementById('clientsContainer');
                Array.from(container.children)
                    .sort((a, b) => a.dataset.name.localeCompare(b.dataset.name))
                    .forEach(card => container.appendChild(card));
            }
        }

        // Add event listeners
        clientSearch.addEventListener('input', filterAndSortClients);
        searchButton.addEventListener('click', filterAndSortClients);
        filterType.addEventListener('change', filterAndSortClients);
        filterProgram.addEventListener('change', filterAndSortClients);
        sortBy.addEventListener('change', filterAndSortClients);
    });
</script>
{% endblock %}
