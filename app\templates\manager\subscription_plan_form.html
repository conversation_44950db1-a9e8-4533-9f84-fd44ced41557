<!-- app/templates/manager/subscription_plan_form.html -->
{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.subscription_plans_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Plans
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" {% if plan %}action="{{ url_for('manager.edit_subscription_plan', id=plan.id) }}"{% else %}action="{{ url_for('manager.new_subscription_plan') }}"{% endif %}>
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">Plan Name</label>
                        {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        {{ form.description(class="form-control", rows=3) }}
                        <div class="form-text">Provide details about what this subscription plan includes.</div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="price" class="form-label">Price ($)</label>
                            {{ form.price(class="form-control" + (" is-invalid" if form.price.errors else "")) }}
                            {% if form.price.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.price.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="duration_months" class="form-label">Duration (months)</label>
                            {{ form.duration_months(class="form-control" + (" is-invalid" if form.duration_months.errors else "")) }}
                            {% if form.duration_months.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.duration_months.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="max_hours" class="form-label">Maximum Hours</label>
                            {{ form.max_hours(class="form-control" + (" is-invalid" if form.max_hours.errors else "")) }}
                            {% if form.max_hours.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.max_hours.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.subscription_plans_list') }}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    {% if plan %}
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">Plan Details</h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">Subscription Statistics</h6>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Active Subscriptions:</span>
                    <span class="badge bg-primary">{{ plan.subscriptions.filter_by(status='active').count() }}</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Total Subscribers:</span>
                    <span class="badge bg-primary">{{ plan.subscriptions.count() }}</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>Created:</span>
                    <span>{{ plan.insert_date.strftime('%Y-%m-%d') }}</span>
                </div>
                
                <div class="d-flex justify-content-between">
                    <span>Last Updated:</span>
                    <span>{{ plan.modification_date.strftime('%Y-%m-%d') }}</span>
                </div>
                
                <hr class="my-3">
                
                <div class="text-center">
                    <a href="{{ url_for('manager.subscriptions_list') }}?plan_id={{ plan.id }}" class="btn btn-primary">
                        View Subscribers
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}