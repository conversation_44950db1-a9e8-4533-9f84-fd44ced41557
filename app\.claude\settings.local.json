{"permissions": {"allow": ["Bash(grep:*)", "Bash(rg:*)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 30 \"appointments/.*edit\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 5 -A 60 \"def edit_appointment\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 \"New status: {appointment.status}\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 \"New status:\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 5 -A 40 \"class AppointmentForm\" forms/manager_forms.py)", "<PERSON><PERSON>(python:*)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 2 -A 2 \"status\" templates/manager/appointment_form.html)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -n \"form.status|status-section\" templates/manager/appointment_form.html)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 5 -A 30 \"def schedule\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 \"Get appointments for the selected period\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 2 -A 20 \"def appointment_detail\" views/manager.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 5 -A 20 \"def schedule\" views/tutor.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 15 \"Get appointments for this tutor\" views/tutor.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 \"appointments = Appointment\" views/tutor.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 2 -A 5 \"Appointment.query\" views/client.py)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 2 -A 2 \"client_name|student_name\" templates/manager/schedule.html)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"showAppointmentDetails|appointmentModal\" templates/manager/schedule.html)", "Bash(/home/<USER>/.npm-global/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -B 5 -A 20 \"def view_appointment\" views/manager.py)"], "deny": []}}