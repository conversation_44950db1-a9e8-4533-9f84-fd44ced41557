# app/models/invoice.py
from datetime import datetime
from app.extensions import db

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('clients.id'), nullable=False)

    invoice_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='pending')
    stripe_payment_intent_id = db.Column(db.String(255), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    paid_by_client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=True)
    paid_date = db.Column(db.DateTime, nullable=True)

    # Fields for subscription-related invoices
    is_subscription_advance = db.Column(db.Boolean, default=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=True)

    # Relationships
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    client = db.relationship('Client', foreign_keys=[client_id], backref='client_invoices', overlaps="invoices")
    paid_by_client = db.relationship('Client', foreign_keys=[paid_by_client_id], backref='client_paid_invoices', overlaps="paid_invoices")

    def __repr__(self):
        return f'<Invoice {self.id} client={self.client_id} amount={self.total_amount}>'

    @property
    def is_paid(self):
        return self.status == 'paid'

    @property
    def is_overdue(self):
        if self.status == 'pending' and self.due_date < datetime.now().date():
            return True
        return False

    @property
    def billed_to(self):
        """Return the client this invoice is billed to."""
        return self.client

    @property
    def paid_by(self):
        """Return the client who paid this invoice."""
        return self.paid_by_client if self.paid_by_client_id else None

    def mark_as_paid(self, client_id=None):
        """Mark this invoice as paid by a specific client"""
        # Only update if not already paid to prevent double processing
        if self.status != 'paid':
            self.status = 'paid'
            self.paid_date = datetime.utcnow()

            if client_id:
                self.paid_by_client_id = client_id

        # Don't commit here - let the calling code handle transaction management
        return True

    def get_accessible_clients(self):
        """Get all clients who should have access to this invoice"""
        from app.models.client import Client, ClientRelationship

        # This invoice is directly linked to a client
        clients = [self.client]

        # Also include any related clients (e.g., parents of individual clients)
        relationships = ClientRelationship.query.filter_by(related_client_id=self.client_id).all()
        for rel in relationships:
            if rel.relationship_type in ['parent', 'guardian'] and rel.client not in clients:
                clients.append(rel.client)

        # Get clients from appointments in this invoice
        from app.models.appointment import Appointment
        appointment_ids = [item.appointment_id for item in self.items.all()]
        appointments = Appointment.query.filter(Appointment.id.in_(appointment_ids)).all()

        # Get all clients from these appointments
        for appointment in appointments:
            if appointment.client_id and appointment.client_id != self.client_id:
                appointment_client = Client.query.get(appointment.client_id)
                if appointment_client and appointment_client not in clients:
                    clients.append(appointment_client)

                    # Also get related clients
                    relationships = ClientRelationship.query.filter_by(related_client_id=appointment.client_id).all()
                    for rel in relationships:
                        if rel.relationship_type in ['parent', 'guardian'] and rel.client not in clients:
                            clients.append(rel.client)

        return clients

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointments.id'), nullable=False)
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    description = db.Column(db.Text, nullable=False)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    appointment = db.relationship('Appointment', overlaps="invoice_items")

    def __repr__(self):
        return f'<InvoiceItem {self.id} invoice={self.invoice_id} appointment={self.appointment_id}>'