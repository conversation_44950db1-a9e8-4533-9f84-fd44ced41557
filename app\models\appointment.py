# app/models/appointment.py
from datetime import datetime, timedelta, date, time
from app.extensions import db

class Appointment(db.Model):
    __tablename__ = 'appointments'

    id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('tutors.id'), nullable=False)
    client_id = db.Column(db.Integer, db.Foreign<PERSON>ey('clients.id'), nullable=False)
    dependant_id = db.Column(db.Integer, db.<PERSON>ey('dependants.id'), nullable=True)  # For appointments with dependants
    tutor_service_id = db.Column(db.Integer, db.<PERSON>ey('tutor_services.id'), nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)  # Always required
    end_time = db.Column(db.DateTime, nullable=False)    # Always required
    duration_minutes = db.Column(db.Integer, nullable=True)  # Computed field for convenience
    status = db.Column(db.String(50), nullable=False, default='scheduled')
    notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    transport_fee = db.Column(db.Numeric(10, 2), nullable=True)
    transport_fee_for_tutor = db.Column(db.Boolean, default=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=True)
    is_subscription_based = db.Column(db.Boolean, default=False)
    # recurring_appointment_id removed - replaced by recurring_template_id in unified system

    # Recurring appointment flag
    is_recurring = db.Column(db.Boolean, nullable=False, default=False)

    # Recurring pattern fields
    frequency = db.Column(db.String(20), nullable=True)  # weekly, biweekly, monthly
    day_of_week = db.Column(db.Integer, nullable=True)    # 0=Monday, 6=Sunday
    week_of_month = db.Column(db.Integer, nullable=True)  # 1-5, for monthly patterns
    pattern_start_date = db.Column(db.Date, nullable=True)
    pattern_end_date = db.Column(db.Date, nullable=True)  # NULL means no end date
    pattern_occurrences = db.Column(db.Integer, nullable=True)  # Alternative to end_date
    last_generated_date = db.Column(db.Date, nullable=True)  # Track the last date appointments were generated

    # Self-referencing foreign key for recurring template relationship
    recurring_template_id = db.Column(db.Integer, db.ForeignKey('appointments.id'), nullable=True)

    # is_program_session = db.Column(db.Boolean, default=False)  # Flag for program-based appointments - commented out until DB migration

    # Relationships with overlaps parameter to fix SQLAlchemy warnings
    client = db.relationship('Client', foreign_keys=[client_id], lazy='joined', overlaps="appointments")
    tutor = db.relationship('Tutor', foreign_keys=[tutor_id], lazy='joined', overlaps="appointments")
    tutor_service = db.relationship('TutorService', foreign_keys=[tutor_service_id], lazy='joined', overlaps="appointments")
    dependant = db.relationship('Dependant', foreign_keys=[dependant_id], lazy='joined', overlaps="appointments")
    subscription = db.relationship('Subscription', foreign_keys=[subscription_id], lazy='select', overlaps="appointments,subscription_usage")

    invoice_items = db.relationship('InvoiceItem', lazy='dynamic')
    subscription_usage = db.relationship('SubscriptionUsage', backref='source_appointment', uselist=False)

    # Self-referencing relationships for recurring templates
    recurring_template = db.relationship('Appointment', remote_side=[id], backref='generated_appointments')

    # The module_session relationship is defined in the ModuleSession model

    def __repr__(self):
        return f'<Appointment {self.id} tutor={self.tutor_id} client={self.client_id}>'

    @property
    def appointment_subject(self):
        """Get the subject of the appointment (dependant if present, otherwise client)."""
        # COALESCE logic: prefer dependant over client
        if self.dependant_id and self.dependant:
            return self.dependant
        elif self.client:
            return self.client
        return None

    @property
    def appointment_subject_name(self):
        """Get the name of the appointment subject (dependant name if present, otherwise client name)."""
        subject = self.appointment_subject
        if subject:
            return f"{subject.first_name} {subject.last_name}"
        else:
            # Fallback: try to get names directly if relationships aren't loaded
            if self.dependant_id:
                # Try to get dependant name
                from app.models.dependant import Dependant
                dependant = Dependant.query.get(self.dependant_id)
                if dependant:
                    return f"{dependant.first_name} {dependant.last_name}"
            
            if self.client_id:
                # Try to get client name
                from app.models.client import Client
                client = Client.query.get(self.client_id)
                if client:
                    return f"{client.first_name} {client.last_name}"
            
            return "Unknown"

    @property
    def is_for_dependant(self):
        """Check if this appointment is for a dependant."""
        return self.dependant_id is not None

    @property
    def calculated_duration_minutes(self):
        """Calculate duration from start_time and end_time."""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return 0

    def get_duration_minutes(self):
        """Get duration in minutes, preferring stored value over calculated."""
        # Use stored duration_minutes if available, otherwise calculate
        if hasattr(self, 'duration_minutes') and self.duration_minutes:
            return self.duration_minutes
        return self.calculated_duration_minutes

    @property
    def is_completed(self):
        return self.status == 'completed'

    @property
    def is_cancelled(self):
        return self.status == 'cancelled'

    @property
    def is_no_show(self):
        return self.status == 'no-show'

    @property
    def is_scheduled(self):
        return self.status == 'scheduled'

    @property
    def is_recurring_template(self):
        """Check if this is a recurring appointment template."""
        return self.is_recurring

    @property
    def is_generated_from_recurring(self):
        """Check if this appointment was generated from a recurring template."""
        return self.recurring_template_id is not None

    def get_next_occurrence(self, from_date=None):
        """Calculate the next occurrence date after the given date (for recurring templates only)."""
        if not self.is_recurring:
            return None

        from datetime import datetime, timedelta
        from_date = from_date or datetime.now().date()

        if self.pattern_end_date and from_date > self.pattern_end_date:
            return None

        if from_date < self.pattern_start_date:
            return self.pattern_start_date

        if self.frequency == 'weekly':
            # Find next date with the same day of week
            days_ahead = self.day_of_week - from_date.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            return from_date + timedelta(days=days_ahead)

        elif self.frequency == 'biweekly':
            # Calculate weeks since start date
            weeks_since_start = (from_date - self.pattern_start_date).days // 7
            # Find next biweekly occurrence
            next_week = ((weeks_since_start // 2) + 1) * 2
            next_date = self.pattern_start_date + timedelta(weeks=next_week)

            # Adjust to correct day of week
            days_ahead = self.day_of_week - next_date.weekday()
            if days_ahead != 0:
                next_date += timedelta(days=days_ahead)

            return next_date

        elif self.frequency == 'monthly':
            # Monthly recurring logic would go here
            # This is more complex and would need proper implementation
            pass

        return None

    def generate_next_appointment(self):
        """Generate the next appointment instance from this recurring template."""
        if not self.is_recurring:
            return None

        next_date = self.get_next_occurrence(self.last_generated_date)
        if not next_date:
            return None

        from datetime import datetime, timedelta

        # Create start and end datetime objects using the template's start_time
        template_time = self.start_time.time()
        start_datetime = datetime.combine(next_date, template_time)

        # Calculate end time using the template's duration
        template_duration = self.calculated_duration_minutes
        end_datetime = start_datetime + timedelta(minutes=template_duration)

        # Create new appointment instance
        appointment = Appointment(
            recurring_template_id=self.id,
            tutor_id=self.tutor_id,
            client_id=self.client_id,
            dependant_id=self.dependant_id,
            tutor_service_id=self.tutor_service_id,
            start_time=start_datetime,
            end_time=end_datetime,
            status='scheduled',
            notes=self.notes,
            transport_fee=self.transport_fee,
            transport_fee_for_tutor=self.transport_fee_for_tutor,
            is_subscription_based=self.is_subscription_based,
            subscription_id=self.subscription_id,
            is_recurring=False  # Generated appointments are not templates
        )

        # Update the last generated date
        self.last_generated_date = next_date

        return appointment