CREATE TABLE public.appointments (
    id integer(32,0) NOT NULL DEFAULT nextval('appointments_id_seq'::regclass),
    tutor_id integer(32,0),
    tutor_service_id integer(32,0),
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    status character varying(50) NOT NULL DEFAULT 'scheduled'::character varying,
    notes text,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    subscription_id integer(32,0),
    is_subscription_based boolean DEFAULT false,
    transport_fee numeric(10,2),
    transport_fee_for_tutor boolean DEFAULT true,
    client_id integer(32,0) NOT NULL,
    dependant_id integer(32,0),
    is_recurring boolean NOT NULL DEFAULT false,
    frequency character varying(20),
    day_of_week integer(32,0),
    week_of_month integer(32,0),
    pattern_start_date date,
    pattern_end_date date,
    pattern_occurrences integer(32,0),
    last_generated_date date,
    recurring_template_id integer(32,0),
    duration_minutes integer(32,0)
);

CREATE TABLE public.client_consents (
    id integer(32,0) NOT NULL DEFAULT nextval('client_consents_id_seq'::regclass),
    client_id integer(32,0) NOT NULL,
    tos_version character varying(50) NOT NULL,
    mandatory_accepted_at timestamp without time zone NOT NULL DEFAULT now(),
    mandatory_ip character varying(45),
    optional_consent boolean NOT NULL DEFAULT false,
    optional_updated_at timestamp without time zone,
    optional_ip character varying(45),
    ip_address character varying(50),
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.client_relationships (
    id integer(32,0) NOT NULL DEFAULT nextval('client_relationships_id_seq'::regclass),
    client_id integer(32,0) NOT NULL,
    related_client_id integer(32,0) NOT NULL,
    relationship_type character varying(50) NOT NULL,
    is_primary boolean DEFAULT false,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.client_transport_fees (
    id integer(32,0) NOT NULL DEFAULT nextval('client_transport_fees_id_seq'::regclass),
    client_id integer(32,0) NOT NULL,
    tutor_id integer(32,0) NOT NULL,
    fee_amount numeric(10,2) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.clients (
    id integer(32,0) NOT NULL DEFAULT nextval('clients_id_seq'::regclass),
    user_id integer(32,0),
    client_type character varying(20) NOT NULL,
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    phone character varying(20),
    email character varying(255),
    address text,
    stripe_customer_id character varying(255),
    is_suspended boolean NOT NULL DEFAULT false,
    suspension_reason character varying(255),
    suspended_at timestamp without time zone,
    preferred_language character varying(2) DEFAULT 'en'::character varying,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    civic_number character varying(20),
    street character varying(255),
    city character varying(100),
    postal_code character varying(10),
    province character varying(50) DEFAULT 'Quebec'::character varying,
    country character varying(50) DEFAULT 'Canada'::character varying
);

CREATE TABLE public.dependant_relationships (
    id integer(32,0) NOT NULL DEFAULT nextval('dependant_relationships_id_seq'::regclass),
    client_id integer(32,0) NOT NULL,
    dependant_id integer(32,0) NOT NULL,
    relationship_type character varying(50) NOT NULL,
    is_primary boolean DEFAULT false,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.dependants (
    id integer(32,0) NOT NULL DEFAULT nextval('dependants_id_seq'::regclass),
    user_id integer(32,0),
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    email character varying(255),
    password_hash character varying(255),
    phone character varying(20),
    address text,
    date_of_birth date,
    school_grade character varying(50),
    notes text,
    is_active boolean NOT NULL DEFAULT true,
    preferred_language character varying(2) DEFAULT 'en'::character varying,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    civic_number character varying(20),
    street character varying(255),
    city character varying(100),
    postal_code character varying(10),
    province character varying(50) DEFAULT 'Quebec'::character varying,
    country character varying(50) DEFAULT 'Canada'::character varying
);

CREATE TABLE public.enrollments (
    id integer(32,0) NOT NULL DEFAULT nextval('enrollments_id_seq'::regclass),
    client_id integer(32,0) NOT NULL,
    program_id integer(32,0) NOT NULL,
    pricing_type character varying(50) NOT NULL,
    enrollment_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    start_date date,
    end_date date,
    status character varying(20) NOT NULL DEFAULT 'active'::character varying,
    total_sessions integer(32,0) NOT NULL DEFAULT 0,
    completed_sessions integer(32,0) NOT NULL DEFAULT 0,
    payment_status character varying(20) NOT NULL DEFAULT 'pending'::character varying,
    total_amount numeric(10,2),
    paid_amount numeric(10,2) DEFAULT 0.00,
    notes text,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.group_session_participants (
    id integer(32,0) NOT NULL DEFAULT nextval('group_session_participants_id_seq'::regclass),
    group_session_id integer(32,0) NOT NULL,
    enrollment_id integer(32,0) NOT NULL,
    registration_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    attendance_status character varying(20) DEFAULT 'registered'::character varying,
    attendance_notes text,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.group_sessions (
    id integer(32,0) NOT NULL DEFAULT nextval('group_sessions_id_seq'::regclass),
    program_id integer(32,0) NOT NULL,
    module_id integer(32,0),
    tutor_id integer(32,0) NOT NULL,
    session_date date NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    max_participants integer(32,0) NOT NULL DEFAULT 10,
    current_participants integer(32,0) NOT NULL DEFAULT 0,
    status character varying(20) NOT NULL DEFAULT 'scheduled'::character varying,
    session_notes text,
    tutor_payment_rate numeric(8,2) NOT NULL DEFAULT 15.00,
    total_tutor_payment numeric(10,2) DEFAULT 0.00,
    meeting_link character varying(500),
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.individual_clients (
    client_id integer(32,0) NOT NULL,
    date_of_birth date,
    school_grade character varying(50),
    notes text
);

CREATE TABLE public.institutional_clients (
    client_id integer(32,0) NOT NULL,
    institution_name character varying(255) NOT NULL,
    institution_type character varying(100),
    contact_person character varying(255),
    tax_id character varying(100),
    billing_address text,
    billing_email character varying(255),
    billing_phone character varying(20),
    contract_start_date date,
    contract_end_date date,
    contract_details text,
    notes text
);

CREATE TABLE public.invoice_generation_settings (
    id integer(32,0) NOT NULL DEFAULT nextval('invoice_generation_settings_id_seq'::regclass),
    appointment_id integer(32,0),
    invoice_generated boolean DEFAULT false,
    invoice_id integer(32,0),
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.invoice_items (
    id integer(32,0) NOT NULL DEFAULT nextval('invoice_items_id_seq'::regclass),
    invoice_id integer(32,0),
    appointment_id integer(32,0),
    amount numeric(10,2) NOT NULL,
    description text NOT NULL,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.invoices (
    id integer(32,0) NOT NULL DEFAULT nextval('invoices_id_seq'::regclass),
    invoice_date date NOT NULL,
    due_date date NOT NULL,
    total_amount numeric(10,2) NOT NULL,
    status character varying(20) NOT NULL DEFAULT 'pending'::character varying,
    stripe_payment_intent_id character varying(255),
    notes text,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    paid_date timestamp without time zone,
    is_subscription_advance boolean DEFAULT false,
    subscription_id integer(32,0),
    client_id integer(32,0) NOT NULL,
    paid_by_client_id integer(32,0)
);

CREATE TABLE public.managers (
    id integer(32,0) NOT NULL DEFAULT nextval('managers_id_seq'::regclass),
    user_id integer(32,0),
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    phone character varying(20),
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.module_progress (
    id integer(32,0) NOT NULL DEFAULT nextval('module_progress_id_seq'::regclass),
    enrollment_id integer(32,0) NOT NULL,
    module_id integer(32,0) NOT NULL,
    status character varying(20) NOT NULL DEFAULT 'not_started'::character varying,
    start_date timestamp without time zone,
    completion_date timestamp without time zone,
    score numeric(5,2),
    notes text,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.module_sessions (
    id integer(32,0) NOT NULL DEFAULT nextval('module_sessions_id_seq'::regclass),
    module_progress_id integer(32,0) NOT NULL,
    appointment_id integer(32,0) NOT NULL,
    session_number integer(32,0) NOT NULL,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.notifications (
    id integer(32,0) NOT NULL DEFAULT nextval('notifications_id_seq'::regclass),
    user_id integer(32,0) NOT NULL,
    message text NOT NULL,
    category character varying(50) NOT NULL,
    related_id integer(32,0),
    related_type character varying(50),
    data jsonb,
    is_read boolean DEFAULT false,
    is_actionable boolean DEFAULT false,
    action_url text,
    priority character varying(20) DEFAULT 'normal'::character varying,
    insert_date timestamp without time zone DEFAULT now(),
    expiry_date timestamp without time zone
);

CREATE TABLE public.program_modules (
    id integer(32,0) NOT NULL DEFAULT nextval('program_modules_id_seq'::regclass),
    program_id integer(32,0) NOT NULL,
    name character varying(200) NOT NULL,
    description text,
    module_order integer(32,0) NOT NULL,
    duration_minutes integer(32,0) NOT NULL DEFAULT 60,
    learning_objectives text,
    is_active boolean NOT NULL DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.program_pricing (
    id integer(32,0) NOT NULL DEFAULT nextval('program_pricing_id_seq'::regclass),
    program_id integer(32,0) NOT NULL,
    pricing_type character varying(50) NOT NULL,
    price numeric(10,2) NOT NULL,
    currency character varying(3) NOT NULL DEFAULT 'CAD'::character varying,
    description text,
    is_active boolean NOT NULL DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.programs (
    id integer(32,0) NOT NULL DEFAULT nextval('programs_id_seq'::regclass),
    name character varying(200) NOT NULL,
    code character varying(50) NOT NULL,
    description text,
    is_active boolean NOT NULL DEFAULT true,
    total_sessions integer(32,0) NOT NULL DEFAULT 1,
    session_duration integer(32,0) NOT NULL DEFAULT 60,
    min_participants integer(32,0) NOT NULL DEFAULT 1,
    max_participants integer(32,0) NOT NULL DEFAULT 10,
    created_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.services (
    id integer(32,0) NOT NULL DEFAULT nextval('services_id_seq'::regclass),
    name character varying(100) NOT NULL,
    description text,
    default_price numeric(10,2) NOT NULL,
    duration_minutes integer(32,0) NOT NULL,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.subscription_plans (
    id integer(32,0) NOT NULL DEFAULT nextval('subscription_plans_id_seq'::regclass),
    name character varying(100) NOT NULL,
    description text,
    price numeric(10,2) NOT NULL,
    duration_months integer(32,0) NOT NULL,
    max_hours integer(32,0) NOT NULL,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.subscription_usage (
    id integer(32,0) NOT NULL DEFAULT nextval('subscription_usage_id_seq'::regclass),
    subscription_id integer(32,0) NOT NULL,
    appointment_id integer(32,0) NOT NULL,
    hours_used double precision NOT NULL,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE public.subscriptions (
    id integer(32,0) NOT NULL DEFAULT nextval('subscriptions_id_seq'::regclass),
    plan_id integer(32,0) NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    hours_used double precision DEFAULT 0.0,
    status character varying(20) NOT NULL DEFAULT 'active'::character varying,
    stripe_subscription_id character varying(255),
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    client_id integer(32,0) NOT NULL
);

CREATE TABLE public.time_off_requests (
    id integer(32,0) NOT NULL DEFAULT nextval('time_off_requests_id_seq'::regclass),
    tutor_id integer(32,0) NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL,
    reason text,
    status character varying(20) NOT NULL DEFAULT 'pending'::character varying,
    manager_notes text,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.tutor_availabilities (
    id integer(32,0) NOT NULL DEFAULT nextval('tutor_availabilities_id_seq'::regclass),
    tutor_id integer(32,0) NOT NULL,
    day_of_week integer(32,0) NOT NULL,
    start_time time without time zone NOT NULL,
    end_time time without time zone NOT NULL,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.tutor_payments (
    id integer(32,0) NOT NULL DEFAULT nextval('tutor_payments_id_seq'::regclass),
    tutor_id integer(32,0) NOT NULL,
    appointment_id integer(32,0) NOT NULL,
    service_amount numeric(10,2) NOT NULL,
    transport_amount numeric(10,2) NOT NULL DEFAULT 0,
    total_amount numeric(10,2) NOT NULL,
    status character varying(20) DEFAULT 'pending'::character varying,
    payment_date timestamp without time zone,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    stripe_payout_id character varying(255)
);

CREATE TABLE public.tutor_service_rates (
    id integer(32,0) NOT NULL DEFAULT nextval('tutor_service_rates_id_seq'::regclass),
    tutor_id integer(32,0) NOT NULL,
    service_id integer(32,0) NOT NULL,
    tutor_rate numeric(10,2) NOT NULL,
    client_rate numeric(10,2) NOT NULL,
    transport_fee numeric(10,2) NOT NULL DEFAULT 0.00,
    transport_fee_description text,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT now(),
    modification_date timestamp without time zone DEFAULT now()
);

CREATE TABLE public.tutor_services (
    id integer(32,0) NOT NULL DEFAULT nextval('tutor_services_id_seq'::regclass),
    tutor_id integer(32,0),
    service_id integer(32,0),
    tutor_rate numeric(10,2) NOT NULL,
    client_rate numeric(10,2) NOT NULL,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    transport_fee numeric(10,2) NOT NULL DEFAULT 0.00,
    transport_fee_description text
);

CREATE TABLE public.tutors (
    id integer(32,0) NOT NULL DEFAULT nextval('tutors_id_seq'::regclass),
    user_id integer(32,0),
    first_name character varying(100) NOT NULL,
    last_name character varying(100) NOT NULL,
    phone character varying(20) NOT NULL,
    bio text,
    qualifications text,
    is_active boolean DEFAULT true,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    street_address text,
    city character varying(100),
    province character varying(100),
    zip_code character varying(20),
    country character varying(100),
    birthdate date,
    bank_transit_number_encrypted character varying(255),
    bank_institution_number_encrypted character varying(255),
    bank_account_number_encrypted character varying(255)
);

CREATE TABLE public.users (
    id integer(32,0) NOT NULL DEFAULT nextval('users_id_seq'::regclass),
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    role character varying(20) NOT NULL,
    is_active boolean DEFAULT true,
    reset_token character varying(255),
    reset_token_expires timestamp without time zone,
    last_login timestamp without time zone,
    insert_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    modification_date timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appointments ADD CONSTRAINT 2200_16502_16_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT 2200_16502_18_not_null CHECK (is_recurring IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT 2200_16502_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT 2200_16502_5_not_null CHECK (start_time IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT 2200_16502_6_not_null CHECK (end_time IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT 2200_16502_7_not_null CHECK (status IS NOT NULL);
ALTER TABLE appointments ADD CONSTRAINT appointments_status_check CHECK ((((status)::text = ANY ((ARRAY['scheduled'::character varying, 'completed'::character varying, 'cancelled'::character varying, 'no-show'::character varying, 'awaiting_confirmation'::character varying, 'confirmed'::character varying, 'rescheduled'::character varying])::text[]))));
ALTER TABLE appointments ADD CONSTRAINT check_appointment_times CHECK (((((is_recurring = false) AND (start_time IS NOT NULL) AND (end_time IS NOT NULL)) OR (is_recurring = true))));
ALTER TABLE appointments ADD CONSTRAINT valid_timeframe CHECK (((end_time > start_time)));
ALTER TABLE appointments ADD CONSTRAINT appointments_subscription_id_fkey FOREIGN KEY (subscription_id) REFERENCES subscriptions(id);
ALTER TABLE appointments ADD CONSTRAINT appointments_tutor_id_fkey FOREIGN KEY (tutor_id) REFERENCES tutors(id);
ALTER TABLE appointments ADD CONSTRAINT appointments_tutor_service_id_fkey FOREIGN KEY (tutor_service_id) REFERENCES tutor_services(id);
ALTER TABLE appointments ADD CONSTRAINT fk_appointments_dependant_id FOREIGN KEY (dependant_id) REFERENCES dependants(id);
ALTER TABLE appointments ADD CONSTRAINT fk_appointments_recurring_template FOREIGN KEY (recurring_template_id) REFERENCES appointments(id);
ALTER TABLE appointments ADD CONSTRAINT appointments_pkey PRIMARY KEY (id);
ALTER TABLE client_consents ADD CONSTRAINT 2200_17181_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE client_consents ADD CONSTRAINT 2200_17181_2_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE client_consents ADD CONSTRAINT 2200_17181_3_not_null CHECK (tos_version IS NOT NULL);
ALTER TABLE client_consents ADD CONSTRAINT 2200_17181_4_not_null CHECK (mandatory_accepted_at IS NOT NULL);
ALTER TABLE client_consents ADD CONSTRAINT 2200_17181_6_not_null CHECK (optional_consent IS NOT NULL);
ALTER TABLE client_consents ADD CONSTRAINT client_consents_pkey PRIMARY KEY (id);
ALTER TABLE client_relationships ADD CONSTRAINT 2200_16983_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE client_relationships ADD CONSTRAINT 2200_16983_2_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE client_relationships ADD CONSTRAINT 2200_16983_3_not_null CHECK (related_client_id IS NOT NULL);
ALTER TABLE client_relationships ADD CONSTRAINT 2200_16983_4_not_null CHECK (relationship_type IS NOT NULL);
ALTER TABLE client_relationships ADD CONSTRAINT client_relationships_pkey PRIMARY KEY (id);
ALTER TABLE client_relationships ADD CONSTRAINT client_relationship_unique UNIQUE (client_id, client_id, client_id, related_client_id, related_client_id, related_client_id, relationship_type, relationship_type, relationship_type);
ALTER TABLE client_transport_fees ADD CONSTRAINT 2200_17213_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE client_transport_fees ADD CONSTRAINT 2200_17213_2_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE client_transport_fees ADD CONSTRAINT 2200_17213_3_not_null CHECK (tutor_id IS NOT NULL);
ALTER TABLE client_transport_fees ADD CONSTRAINT 2200_17213_4_not_null CHECK (fee_amount IS NOT NULL);
ALTER TABLE client_transport_fees ADD CONSTRAINT client_transport_fees_tutor_id_fkey FOREIGN KEY (tutor_id) REFERENCES tutors(id);
ALTER TABLE client_transport_fees ADD CONSTRAINT client_transport_fees_pkey PRIMARY KEY (id);
ALTER TABLE clients ADD CONSTRAINT 2200_17797_10_not_null CHECK (is_suspended IS NOT NULL);
ALTER TABLE clients ADD CONSTRAINT 2200_17797_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE clients ADD CONSTRAINT 2200_17797_3_not_null CHECK (client_type IS NOT NULL);
ALTER TABLE clients ADD CONSTRAINT 2200_17797_4_not_null CHECK (first_name IS NOT NULL);
ALTER TABLE clients ADD CONSTRAINT 2200_17797_5_not_null CHECK (last_name IS NOT NULL);
ALTER TABLE clients ADD CONSTRAINT fk_clients_user_id FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE clients ADD CONSTRAINT clients_pkey PRIMARY KEY (id);
ALTER TABLE dependant_relationships ADD CONSTRAINT 2200_17857_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE dependant_relationships ADD CONSTRAINT 2200_17857_2_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE dependant_relationships ADD CONSTRAINT 2200_17857_3_not_null CHECK (dependant_id IS NOT NULL);
ALTER TABLE dependant_relationships ADD CONSTRAINT 2200_17857_4_not_null CHECK (relationship_type IS NOT NULL);
ALTER TABLE dependant_relationships ADD CONSTRAINT fk_dependant_relationships_client_id FOREIGN KEY (client_id) REFERENCES clients(id);
ALTER TABLE dependant_relationships ADD CONSTRAINT fk_dependant_relationships_dependant_id FOREIGN KEY (dependant_id) REFERENCES dependants(id);
ALTER TABLE dependant_relationships ADD CONSTRAINT dependant_relationships_pkey PRIMARY KEY (id);
ALTER TABLE dependant_relationships ADD CONSTRAINT dependant_relationships_client_id_dependant_id_relationship_key UNIQUE (client_id, client_id, client_id, dependant_id, dependant_id, dependant_id, relationship_type, relationship_type, relationship_type);
ALTER TABLE dependants ADD CONSTRAINT 2200_17844_12_not_null CHECK (is_active IS NOT NULL);
ALTER TABLE dependants ADD CONSTRAINT 2200_17844_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE dependants ADD CONSTRAINT 2200_17844_3_not_null CHECK (first_name IS NOT NULL);
ALTER TABLE dependants ADD CONSTRAINT 2200_17844_4_not_null CHECK (last_name IS NOT NULL);
ALTER TABLE dependants ADD CONSTRAINT fk_dependants_user_id FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE dependants ADD CONSTRAINT dependants_pkey PRIMARY KEY (id);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_10_not_null CHECK (completed_sessions IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_11_not_null CHECK (payment_status IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_2_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_3_not_null CHECK (program_id IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_4_not_null CHECK (pricing_type IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_8_not_null CHECK (status IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT 2200_17606_9_not_null CHECK (total_sessions IS NOT NULL);
ALTER TABLE enrollments ADD CONSTRAINT enrollments_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE enrollments ADD CONSTRAINT fk_enrollments_program FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE enrollments ADD CONSTRAINT enrollments_pkey PRIMARY KEY (id);
ALTER TABLE group_session_participants ADD CONSTRAINT 2200_17678_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE group_session_participants ADD CONSTRAINT 2200_17678_2_not_null CHECK (group_session_id IS NOT NULL);
ALTER TABLE group_session_participants ADD CONSTRAINT 2200_17678_3_not_null CHECK (enrollment_id IS NOT NULL);
ALTER TABLE group_session_participants ADD CONSTRAINT group_session_participants_enrollment_id_fkey FOREIGN KEY (enrollment_id) REFERENCES enrollments(id);
ALTER TABLE group_session_participants ADD CONSTRAINT group_session_participants_group_session_id_fkey FOREIGN KEY (group_session_id) REFERENCES group_sessions(id);
ALTER TABLE group_session_participants ADD CONSTRAINT group_session_participants_pkey PRIMARY KEY (id);
ALTER TABLE group_session_participants ADD CONSTRAINT group_session_participants_group_session_id_enrollment_id_key UNIQUE (group_session_id, group_session_id, enrollment_id, enrollment_id);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_10_not_null CHECK (status IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_12_not_null CHECK (tutor_payment_rate IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_2_not_null CHECK (program_id IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_4_not_null CHECK (tutor_id IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_5_not_null CHECK (session_date IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_6_not_null CHECK (start_time IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_7_not_null CHECK (end_time IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_8_not_null CHECK (max_participants IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT 2200_17652_9_not_null CHECK (current_participants IS NOT NULL);
ALTER TABLE group_sessions ADD CONSTRAINT chk_current_participants_max CHECK (((current_participants <= max_participants)));
ALTER TABLE group_sessions ADD CONSTRAINT fk_group_sessions_module FOREIGN KEY (module_id) REFERENCES program_modules(id);
ALTER TABLE group_sessions ADD CONSTRAINT fk_group_sessions_program FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE group_sessions ADD CONSTRAINT fk_group_sessions_tutor FOREIGN KEY (tutor_id) REFERENCES tutors(id);
ALTER TABLE group_sessions ADD CONSTRAINT group_sessions_module_id_fkey FOREIGN KEY (module_id) REFERENCES program_modules(id);
ALTER TABLE group_sessions ADD CONSTRAINT group_sessions_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE group_sessions ADD CONSTRAINT group_sessions_pkey PRIMARY KEY (id);
ALTER TABLE individual_clients ADD CONSTRAINT 2200_17814_1_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE individual_clients ADD CONSTRAINT individual_clients_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id);
ALTER TABLE individual_clients ADD CONSTRAINT individual_clients_pkey PRIMARY KEY (client_id);
ALTER TABLE institutional_clients ADD CONSTRAINT 2200_17826_1_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE institutional_clients ADD CONSTRAINT 2200_17826_2_not_null CHECK (institution_name IS NOT NULL);
ALTER TABLE institutional_clients ADD CONSTRAINT institutional_clients_client_id_fkey FOREIGN KEY (client_id) REFERENCES clients(id);
ALTER TABLE institutional_clients ADD CONSTRAINT institutional_clients_pkey PRIMARY KEY (client_id);
ALTER TABLE invoice_generation_settings ADD CONSTRAINT 2200_16880_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE invoice_generation_settings ADD CONSTRAINT invoice_generation_settings_appointment_id_fkey FOREIGN KEY (appointment_id) REFERENCES appointments(id);
ALTER TABLE invoice_generation_settings ADD CONSTRAINT invoice_generation_settings_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES invoices(id);
ALTER TABLE invoice_generation_settings ADD CONSTRAINT invoice_generation_settings_pkey PRIMARY KEY (id);
ALTER TABLE invoice_generation_settings ADD CONSTRAINT invoice_generation_settings_appointment_id_key UNIQUE (appointment_id);
ALTER TABLE invoice_items ADD CONSTRAINT 2200_16550_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE invoice_items ADD CONSTRAINT 2200_16550_4_not_null CHECK (amount IS NOT NULL);
ALTER TABLE invoice_items ADD CONSTRAINT 2200_16550_5_not_null CHECK (description IS NOT NULL);
ALTER TABLE invoice_items ADD CONSTRAINT invoice_items_appointment_id_fkey FOREIGN KEY (appointment_id) REFERENCES appointments(id);
ALTER TABLE invoice_items ADD CONSTRAINT invoice_items_invoice_id_fkey FOREIGN KEY (invoice_id) REFERENCES invoices(id);
ALTER TABLE invoice_items ADD CONSTRAINT invoice_items_pkey PRIMARY KEY (id);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_15_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_3_not_null CHECK (invoice_date IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_4_not_null CHECK (due_date IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_5_not_null CHECK (total_amount IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT 2200_16532_6_not_null CHECK (status IS NOT NULL);
ALTER TABLE invoices ADD CONSTRAINT invoices_status_check CHECK ((((status)::text = ANY ((ARRAY['pending'::character varying, 'paid'::character varying, 'overdue'::character varying, 'cancelled'::character varying])::text[]))));
ALTER TABLE invoices ADD CONSTRAINT invoices_subscription_id_fkey FOREIGN KEY (subscription_id) REFERENCES subscriptions(id);
ALTER TABLE invoices ADD CONSTRAINT invoices_pkey PRIMARY KEY (id);
ALTER TABLE managers ADD CONSTRAINT 2200_16405_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE managers ADD CONSTRAINT 2200_16405_3_not_null CHECK (first_name IS NOT NULL);
ALTER TABLE managers ADD CONSTRAINT 2200_16405_4_not_null CHECK (last_name IS NOT NULL);
ALTER TABLE managers ADD CONSTRAINT managers_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE managers ADD CONSTRAINT managers_pkey PRIMARY KEY (id);
ALTER TABLE module_progress ADD CONSTRAINT 2200_17628_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE module_progress ADD CONSTRAINT 2200_17628_2_not_null CHECK (enrollment_id IS NOT NULL);
ALTER TABLE module_progress ADD CONSTRAINT 2200_17628_3_not_null CHECK (module_id IS NOT NULL);
ALTER TABLE module_progress ADD CONSTRAINT 2200_17628_4_not_null CHECK (status IS NOT NULL);
ALTER TABLE module_progress ADD CONSTRAINT module_progress_enrollment_id_fkey FOREIGN KEY (enrollment_id) REFERENCES enrollments(id);
ALTER TABLE module_progress ADD CONSTRAINT module_progress_module_id_fkey FOREIGN KEY (module_id) REFERENCES program_modules(id);
ALTER TABLE module_progress ADD CONSTRAINT module_progress_pkey PRIMARY KEY (id);
ALTER TABLE module_progress ADD CONSTRAINT module_progress_enrollment_id_module_id_key UNIQUE (enrollment_id, enrollment_id, module_id, module_id);
ALTER TABLE module_sessions ADD CONSTRAINT 2200_17104_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE module_sessions ADD CONSTRAINT 2200_17104_2_not_null CHECK (module_progress_id IS NOT NULL);
ALTER TABLE module_sessions ADD CONSTRAINT 2200_17104_3_not_null CHECK (appointment_id IS NOT NULL);
ALTER TABLE module_sessions ADD CONSTRAINT 2200_17104_4_not_null CHECK (session_number IS NOT NULL);
ALTER TABLE module_sessions ADD CONSTRAINT module_sessions_appointment_id_fkey FOREIGN KEY (appointment_id) REFERENCES appointments(id);
ALTER TABLE module_sessions ADD CONSTRAINT module_sessions_pkey PRIMARY KEY (id);
ALTER TABLE module_sessions ADD CONSTRAINT module_session_unique UNIQUE (module_progress_id, module_progress_id, appointment_id, appointment_id);
ALTER TABLE notifications ADD CONSTRAINT 2200_16846_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE notifications ADD CONSTRAINT 2200_16846_2_not_null CHECK (user_id IS NOT NULL);
ALTER TABLE notifications ADD CONSTRAINT 2200_16846_3_not_null CHECK (message IS NOT NULL);
ALTER TABLE notifications ADD CONSTRAINT 2200_16846_4_not_null CHECK (category IS NOT NULL);
ALTER TABLE notifications ADD CONSTRAINT notifications_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE notifications ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_2_not_null CHECK (program_id IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_3_not_null CHECK (name IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_5_not_null CHECK (module_order IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_6_not_null CHECK (duration_minutes IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT 2200_17534_8_not_null CHECK (is_active IS NOT NULL);
ALTER TABLE program_modules ADD CONSTRAINT fk_program_modules_program FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE program_modules ADD CONSTRAINT program_modules_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE program_modules ADD CONSTRAINT program_modules_pkey PRIMARY KEY (id);
ALTER TABLE program_modules ADD CONSTRAINT program_modules_program_id_module_order_key UNIQUE (program_id, program_id, module_order, module_order);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_2_not_null CHECK (program_id IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_3_not_null CHECK (pricing_type IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_4_not_null CHECK (price IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_5_not_null CHECK (currency IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT 2200_17554_7_not_null CHECK (is_active IS NOT NULL);
ALTER TABLE program_pricing ADD CONSTRAINT fk_program_pricing_program FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE program_pricing ADD CONSTRAINT program_pricing_program_id_fkey FOREIGN KEY (program_id) REFERENCES programs(id);
ALTER TABLE program_pricing ADD CONSTRAINT program_pricing_pkey PRIMARY KEY (id);
ALTER TABLE program_pricing ADD CONSTRAINT program_pricing_program_id_pricing_type_key UNIQUE (program_id, program_id, pricing_type, pricing_type);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_2_not_null CHECK (name IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_3_not_null CHECK (code IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_5_not_null CHECK (is_active IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_6_not_null CHECK (total_sessions IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_7_not_null CHECK (session_duration IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_8_not_null CHECK (min_participants IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT 2200_17515_9_not_null CHECK (max_participants IS NOT NULL);
ALTER TABLE programs ADD CONSTRAINT programs_pkey PRIMARY KEY (id);
ALTER TABLE programs ADD CONSTRAINT programs_code_key UNIQUE (code);
ALTER TABLE services ADD CONSTRAINT 2200_16468_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE services ADD CONSTRAINT 2200_16468_2_not_null CHECK (name IS NOT NULL);
ALTER TABLE services ADD CONSTRAINT 2200_16468_4_not_null CHECK (default_price IS NOT NULL);
ALTER TABLE services ADD CONSTRAINT 2200_16468_5_not_null CHECK (duration_minutes IS NOT NULL);
ALTER TABLE services ADD CONSTRAINT services_pkey PRIMARY KEY (id);
ALTER TABLE subscription_plans ADD CONSTRAINT 2200_16592_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE subscription_plans ADD CONSTRAINT 2200_16592_2_not_null CHECK (name IS NOT NULL);
ALTER TABLE subscription_plans ADD CONSTRAINT 2200_16592_4_not_null CHECK (price IS NOT NULL);
ALTER TABLE subscription_plans ADD CONSTRAINT 2200_16592_5_not_null CHECK (duration_months IS NOT NULL);
ALTER TABLE subscription_plans ADD CONSTRAINT 2200_16592_6_not_null CHECK (max_hours IS NOT NULL);
ALTER TABLE subscription_plans ADD CONSTRAINT subscription_plans_pkey PRIMARY KEY (id);
ALTER TABLE subscription_usage ADD CONSTRAINT 2200_16675_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE subscription_usage ADD CONSTRAINT 2200_16675_2_not_null CHECK (subscription_id IS NOT NULL);
ALTER TABLE subscription_usage ADD CONSTRAINT 2200_16675_3_not_null CHECK (appointment_id IS NOT NULL);
ALTER TABLE subscription_usage ADD CONSTRAINT 2200_16675_4_not_null CHECK (hours_used IS NOT NULL);
ALTER TABLE subscription_usage ADD CONSTRAINT subscription_usage_appointment_id_fkey FOREIGN KEY (appointment_id) REFERENCES appointments(id);
ALTER TABLE subscription_usage ADD CONSTRAINT subscription_usage_subscription_id_fkey FOREIGN KEY (subscription_id) REFERENCES subscriptions(id);
ALTER TABLE subscription_usage ADD CONSTRAINT subscription_usage_pkey PRIMARY KEY (id);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_11_not_null CHECK (client_id IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_3_not_null CHECK (plan_id IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_4_not_null CHECK (start_date IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_5_not_null CHECK (end_date IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT 2200_16604_7_not_null CHECK (status IS NOT NULL);
ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_plan_id_fkey FOREIGN KEY (plan_id) REFERENCES subscription_plans(id);
ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_pkey PRIMARY KEY (id);
ALTER TABLE time_off_requests ADD CONSTRAINT 2200_16829_1_not_null CHECK (id IS NOT NULL);
ALTER TABLE time_off_requests ADD CONSTRAINT 2200_16829_2_not_null CHECK (tutor_id IS NOT NULL);

CREATE INDEX idx_appointments_client_id ON appointments (client_id);
CREATE INDEX idx_appointments_dependant_id ON appointments (dependant_id);
CREATE INDEX idx_appointments_duration ON appointments (duration_minutes);
CREATE INDEX idx_appointments_end_time ON appointments (end_time);
CREATE INDEX idx_appointments_is_recurring ON appointments (is_recurring);
CREATE INDEX idx_appointments_pattern_dates ON appointments (pattern_start_date, pattern_end_date);
CREATE INDEX idx_appointments_recurring_template ON appointments (recurring_template_id);
CREATE INDEX idx_appointments_recurring_template_id ON appointments (recurring_template_id);
CREATE INDEX idx_appointments_start_time ON appointments (start_time);
CREATE INDEX idx_appointments_tutor_id ON appointments (tutor_id);
CREATE INDEX idx_client_consents_client_id ON client_consents (client_id);
CREATE UNIQUE INDEX client_relationship_unique ON client_relationships (client_id, related_client_id, relationship_type);
CREATE INDEX idx_client_relationships_client_id ON client_relationships (client_id);
CREATE INDEX idx_client_relationships_related_client_id ON client_relationships (related_client_id);
CREATE INDEX idx_client_relationships_relationship_type ON client_relationships (relationship_type);
CREATE INDEX idx_client_transport_fees_client_id ON client_transport_fees (client_id);
CREATE INDEX idx_client_transport_fees_tutor_id ON client_transport_fees (tutor_id);
CREATE INDEX idx_clients_client_type ON clients (client_type);
CREATE INDEX idx_clients_email ON clients (email);
CREATE INDEX idx_clients_is_suspended ON clients (is_suspended);
CREATE INDEX idx_clients_user_id ON clients (user_id);
CREATE UNIQUE INDEX dependant_relationships_client_id_dependant_id_relationship_key ON dependant_relationships (client_id, dependant_id, relationship_type);
CREATE INDEX idx_dependant_relationships_client_id ON dependant_relationships (client_id);
CREATE INDEX idx_dependant_relationships_dependant_id ON dependant_relationships (dependant_id);
CREATE INDEX idx_dependants_email ON dependants (email);
CREATE INDEX idx_dependants_is_active ON dependants (is_active);
CREATE INDEX idx_dependants_user_id ON dependants (user_id);
CREATE INDEX idx_enrollments_client_id ON enrollments (client_id);
CREATE INDEX idx_enrollments_program_id ON enrollments (program_id);
CREATE INDEX idx_enrollments_status ON enrollments (status);
CREATE UNIQUE INDEX group_session_participants_group_session_id_enrollment_id_key ON group_session_participants (group_session_id, enrollment_id);
CREATE INDEX idx_group_session_participants_enrollment_id ON group_session_participants (enrollment_id);
CREATE INDEX idx_group_session_participants_session_id ON group_session_participants (group_session_id);
CREATE INDEX idx_group_session_participants_session_status ON group_session_participants (group_session_id, attendance_status);
CREATE INDEX idx_group_sessions_date ON group_sessions (session_date);
CREATE INDEX idx_group_sessions_program_id ON group_sessions (program_id);
CREATE INDEX idx_group_sessions_tutor_id ON group_sessions (tutor_id);
CREATE INDEX idx_invoice_generation_appointment ON invoice_generation_settings (appointment_id);
CREATE UNIQUE INDEX invoice_generation_settings_appointment_id_key ON invoice_generation_settings (appointment_id);
CREATE INDEX idx_invoice_items_invoice_id ON invoice_items (invoice_id);
CREATE INDEX idx_invoices_client_id ON invoices (client_id);
CREATE INDEX idx_invoices_paid_by_client_id ON invoices (paid_by_client_id);
CREATE INDEX idx_module_progress_enrollment_id ON module_progress (enrollment_id);
CREATE UNIQUE INDEX module_progress_enrollment_id_module_id_key ON module_progress (enrollment_id, module_id);
CREATE INDEX idx_module_sessions_appointment_id ON module_sessions (appointment_id);
CREATE INDEX idx_module_sessions_module_progress_id ON module_sessions (module_progress_id);
CREATE UNIQUE INDEX module_session_unique ON module_sessions (module_progress_id, appointment_id);
CREATE INDEX idx_notifications_category ON notifications (category);
CREATE INDEX idx_notifications_insert_date ON notifications (insert_date);
CREATE INDEX idx_notifications_is_read ON notifications (is_read);
CREATE INDEX idx_notifications_priority ON notifications (priority);
CREATE INDEX idx_notifications_related ON notifications (related_id, related_type);
CREATE INDEX idx_notifications_user_id ON notifications (user_id);
CREATE INDEX idx_program_modules_order ON program_modules (module_order);
CREATE INDEX idx_program_modules_program_id ON program_modules (program_id);
CREATE UNIQUE INDEX program_modules_program_id_module_order_key ON program_modules (program_id, module_order);
CREATE INDEX idx_program_pricing_program_id ON program_pricing (program_id);
CREATE INDEX idx_program_pricing_type ON program_pricing (pricing_type);
CREATE UNIQUE INDEX program_pricing_program_id_pricing_type_key ON program_pricing (program_id, pricing_type);
CREATE INDEX idx_programs_code ON programs (code);
CREATE INDEX idx_programs_is_active ON programs (is_active);
CREATE UNIQUE INDEX programs_code_key ON programs (code);
CREATE INDEX idx_subscriptions_client_id ON subscriptions (client_id);
CREATE INDEX idx_time_off_dates ON time_off_requests (start_date, end_date);
CREATE INDEX idx_time_off_status ON time_off_requests (status);
CREATE INDEX idx_time_off_tutor_id ON time_off_requests (tutor_id);
CREATE INDEX idx_tutor_availabilities_tutor_id ON tutor_availabilities (tutor_id);
CREATE INDEX idx_tutor_payments_stripe_payout_id ON tutor_payments (stripe_payout_id);
CREATE INDEX idx_tutor_service_rates_service_id ON tutor_service_rates (service_id);
CREATE INDEX idx_tutor_service_rates_tutor_id ON tutor_service_rates (tutor_id);
CREATE UNIQUE INDEX unique_tutor_service ON tutor_service_rates (tutor_id, service_id);
CREATE INDEX idx_tutor_services_service_id ON tutor_services (service_id);
CREATE INDEX idx_tutor_services_tutor_id ON tutor_services (tutor_id);
CREATE UNIQUE INDEX tutor_services_tutor_id_service_id_key ON tutor_services (tutor_id, service_id);
CREATE INDEX idx_users_email ON users (email);
CREATE UNIQUE INDEX users_email_key ON users (email);

CREATE SEQUENCE appointments_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE client_consents_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE client_relationships_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE client_transport_fees_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE clients_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE dependant_relationships_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE dependants_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE enrollments_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE group_session_participants_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE group_sessions_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE invoice_generation_settings_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE invoice_items_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE invoices_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE managers_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE module_progress_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE module_sessions_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE notifications_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE program_modules_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE program_pricing_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE programs_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE services_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE subscription_plans_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE subscription_usage_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE subscriptions_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE time_off_requests_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE tutor_availabilities_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE tutor_payments_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE tutor_service_rates_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE tutor_services_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE tutors_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;
CREATE SEQUENCE users_id_seq START WITH 1 INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 NO CYCLE;